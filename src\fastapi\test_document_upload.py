#!/usr/bin/env python3
"""
测试文档上传和保存功能
"""

import os
import requests
import json

def test_document_upload():
    """测试文档上传API"""
    
    # API端点
    api_url = "http://localhost:8000/api/convert-word-to-structured"
    
    # 检查是否有测试文档
    test_doc_path = "test_document.docx"
    if not os.path.exists(test_doc_path):
        print("❌ 测试文档不存在，请先创建一个测试的Word文档")
        return False
    
    print(f"📁 准备上传测试文档: {test_doc_path}")
    
    try:
        # 准备文件上传
        with open(test_doc_path, 'rb') as f:
            files = {'file': (test_doc_path, f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            
            print("🌐 发送请求到API...")
            response = requests.post(api_url, files=files)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"📊 返回数据:")
            print(f"  - success: {result.get('success')}")
            print(f"  - saved_doc_path: {result.get('saved_doc_path')}")
            print(f"  - filename: {result.get('metadata', {}).get('filename')}")
            print(f"  - document_nodes: {result.get('metadata', {}).get('document_nodes')}")
            
            # 检查文件是否真的被保存了
            saved_path = result.get('saved_doc_path')
            if saved_path and os.path.exists(saved_path):
                print(f"✅ 文档已成功保存到: {saved_path}")
                file_size = os.path.getsize(saved_path)
                print(f"📏 保存的文件大小: {file_size} 字节")
                return True
            else:
                print(f"❌ 文档保存失败，路径不存在: {saved_path}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_batch_insert():
    """测试批量插入API"""
    
    # 首先需要有一个保存的文档路径
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        print("❌ uploads目录不存在，请先运行文档上传测试")
        return False
    
    # 查找最新的上传文档
    uploaded_files = [f for f in os.listdir(uploads_dir) if f.endswith('.docx')]
    if not uploaded_files:
        print("❌ 没有找到已上传的文档，请先运行文档上传测试")
        return False
    
    # 使用最新的文档
    doc_path = os.path.join(uploads_dir, uploaded_files[0])
    print(f"📁 使用文档: {doc_path}")
    
    # API端点
    api_url = "http://localhost:8000/api/word/batch-insert"
    
    # 模拟请求数据
    request_data = {
        "doc_path": doc_path,
        "task_results": [
            {
                "id": "test-task-1",
                "type": "flowchart",
                "status": "completed"
            }
        ],
        "config": {
            "business_flow_pattern": "三、业务流程",
            "operation_flow_pattern": "四、操作流程"
        }
    }
    
    try:
        print("🌐 发送批量插入请求...")
        response = requests.post(
            api_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(request_data)
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量插入请求成功!")
            print(f"📊 返回数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 批量插入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试文档处理功能...")
    print("=" * 50)
    
    print("\n📤 测试1: 文档上传和保存")
    upload_success = test_document_upload()
    
    if upload_success:
        print("\n📥 测试2: 批量插入")
        test_batch_insert()
    
    print("\n🏁 测试完成")
