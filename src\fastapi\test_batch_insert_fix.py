#!/usr/bin/env python3
"""
测试批量插入API修复
"""

import sys
import os
import json
import requests
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_batch_insert_api():
    """测试批量插入API"""
    print("🔍 测试批量插入API修复...")
    
    # 测试数据
    test_data = {
        "doc_path": "uploads/7159ff6f_投标文件-0613.docx",
        "task_results": [
            {
                "id": "section-5-flowchart",
                "type": "flowchart",
                "status": "completed"
            },
            {
                "id": "section-5-ui_prototype",
                "type": "ui_prototype",
                "status": "completed"
            }
        ],
        "config": {
            "business_flow_pattern": "三、业务流程",
            "operation_flow_pattern": "四、操作流程"
        }
    }
    
    # 测试不带 output_path 的请求
    print("📝 测试不带 output_path 的请求...")
    try:
        response = requests.post(
            "http://localhost:8000/api/word/batch-insert",
            json=test_data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 测试带 output_path 的请求
    print("\n📝 测试带 output_path 的请求...")
    test_data_with_output = test_data.copy()
    test_data_with_output["output_path"] = "uploads/output_test_document.docx"
    
    try:
        response = requests.post(
            "http://localhost:8000/api/word/batch-insert",
            json=test_data_with_output,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_batch_insert_api()
