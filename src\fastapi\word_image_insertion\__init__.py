"""
Word精准插图技术验证项目 - 集成版本

基于Playwright的Word文档精准插图解决方案，实现无占位符场景下的自动化图片插入。
集成到招标文档生成系统中。

核心功能：
- 文档解析和精准定位
- 高质量图片生成
- Word文档插图写入
- 格式回写与预览

作者: 基于Playwright项目的技术验证
许可证: Apache 2.0
"""

from .core.document_parser import DocumentParser
from .core.image_generator import ImageGenerator
from .core.word_inserter import WordInserter
from .core.preview_manager import PreviewManager
from .utils.config import Config
from .utils.helpers import setup_logging, validate_dependencies

__version__ = "1.0.0"
__author__ = "Word Image Insertion Team"
__license__ = "Apache 2.0"

# 主要类导出
__all__ = [
    "WordImageInserter",
    "DocumentParser", 
    "ImageGenerator",
    "WordInserter",
    "PreviewManager",
    "Config"
]


class WordImageInserter:
    """
    Word精准插图主类
    
    集成所有核心功能，提供简化的API接口。
    """
    
    def __init__(self, config=None):
        """
        初始化Word插图器
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or Config()
        self.document_parser = DocumentParser(self.config)
        self.image_generator = ImageGenerator(self.config)
        self.word_inserter = WordInserter(self.config)
        self.preview_manager = PreviewManager(self.config)
        
        # 设置日志
        setup_logging(self.config.log_level)

        # 简化的依赖验证 - 避免复杂检查
        try:
            import playwright
            import docx
            from PIL import Image
        except ImportError as e:
            raise ImportError(f"Missing required dependency: {e}")
    
    def parse_document(self, doc_path, heading_pattern=None, keyword_pattern=None):
        """
        解析文档并定位插入点
        
        Args:
            doc_path: Word文档路径
            heading_pattern: 标题样式模式
            keyword_pattern: 关键词模式
            
        Returns:
            list: 插入点列表
        """
        return self.document_parser.find_insertion_points(
            doc_path, heading_pattern, keyword_pattern
        )
    
    def generate_image(self, source, selector=None, output_path=None, **options):
        """
        生成图片
        
        Args:
            source: 图片源（URL、SVG文件路径等）
            selector: CSS选择器（用于网页截图）
            output_path: 输出路径
            **options: 其他选项
            
        Returns:
            str: 生成的图片路径
        """
        return self.image_generator.generate(source, selector, output_path, **options)
    
    def insert_image(self, doc_path, insertion_point, image_path, output_path=None):
        """
        插入图片到Word文档
        
        Args:
            doc_path: 源文档路径
            insertion_point: 插入点信息
            image_path: 图片路径
            output_path: 输出文档路径
            
        Returns:
            str: 输出文档路径
        """
        return self.word_inserter.insert(doc_path, insertion_point, image_path, output_path)
    
    def preview_document(self, doc_path, output_format='pdf'):
        """
        预览文档
        
        Args:
            doc_path: 文档路径
            output_format: 输出格式（pdf/html）
            
        Returns:
            str: 预览文件路径
        """
        return self.preview_manager.generate_preview(doc_path, output_format)
    
    async def process_batch(self, doc_path, image_configs, output_path=None):
        """
        批量处理
        
        Args:
            doc_path: 文档路径
            image_configs: 图片配置列表
            output_path: 输出路径
            
        Returns:
            dict: 处理结果
        """
        results = {
            'success': [],
            'failed': [],
            'output_path': None
        }
        
        current_doc = doc_path
        
        for config in image_configs:
            try:
                # 定位插入点
                targets = self.parse_document(
                    current_doc,
                    config.get('heading_pattern'),
                    config.get('keyword_pattern')
                )
                
                if not targets:
                    results['failed'].append({
                        'config': config,
                        'error': 'No insertion points found'
                    })
                    continue
                
                # 生成图片 - 使用异步方法
                image_path = await self.image_generator.generate_async(
                    config['source'],
                    config.get('selector'),
                    **config.get('options', {})
                )
                
                # 插入图片
                current_doc = self.insert_image(
                    current_doc,
                    targets[0],  # 使用第一个匹配点
                    image_path,
                    output_path
                )
                
                results['success'].append({
                    'config': config,
                    'image_path': image_path,
                    'insertion_point': targets[0]
                })
                
            except Exception as e:
                results['failed'].append({
                    'config': config,
                    'error': str(e)
                })
        
        results['output_path'] = current_doc
        return results


# 便捷函数
def quick_insert(doc_path, url, selector, heading_pattern=None, keyword_pattern=None):
    """
    快速插图函数
    
    Args:
        doc_path: Word文档路径
        url: 网页URL
        selector: CSS选择器
        heading_pattern: 标题模式
        keyword_pattern: 关键词模式
        
    Returns:
        str: 输出文档路径
    """
    inserter = WordImageInserter()
    
    # 定位插入点
    targets = inserter.parse_document(doc_path, heading_pattern, keyword_pattern)
    if not targets:
        raise ValueError("No insertion points found")
    
    # 生成图片
    image_path = inserter.generate_image(url, selector)
    
    # 插入图片
    output_path = inserter.insert_image(doc_path, targets[0], image_path)
    
    return output_path
