#!/usr/bin/env python3
"""
Word精准插图系统 - 基础使用示例
"""

import asyncio
from pathlib import Path
from word_image_insertion import WordImageInserter, Config

async def main():
    """基础使用示例"""
    
    print("🎯 Word精准插图系统 - 基础使用")
    print("=" * 50)
    
    # 创建配置
    config = Config()
    inserter = WordImageInserter(config)
    
    # 文件路径（请根据实际情况修改）
    word_doc = "input/documents/your_document.docx"
    svg_file = "input/images/your_flowchart.svg"
    
    # 检查文件是否存在
    if not Path(word_doc).exists():
        print(f"❌ Word文档不存在: {word_doc}")
        print("请将Word文档放入 input/documents/ 目录")
        return
    
    if not Path(svg_file).exists():
        print(f"❌ SVG文件不存在: {svg_file}")
        print("请将SVG文件放入 input/images/ 目录")
        return
    
    try:
        # 查找插入点
        print(f"🔍 查找插入点...")
        targets = inserter.parse_document(word_doc, keyword_pattern="业务流程")
        
        if targets:
            print(f"✅ 找到 {len(targets)} 个插入点")
            
            # 生成图片
            print(f"🎨 生成高清图片...")
            image_path = await inserter.image_generator.generate_async(
                svg_file,
                output_path="output/generated_image.png",
                viewport_width=1800,
                viewport_height=1200,
                device_scale_factor=2
            )
            
            print(f"✅ 图片生成成功: {Path(image_path).name}")
            
            # 插入图片
            print(f"📝 插入图片到Word文档...")
            final_doc = inserter.insert_image(
                word_doc,
                targets[0],  # 使用第一个插入点
                image_path,
                "output/final_document.docx"
            )
            
            print(f"🎉 完成！最终文档: {final_doc}")
            
        else:
            print("❌ 未找到匹配的插入点")
            print("请检查文档中是否包含'业务流程'相关章节")
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
