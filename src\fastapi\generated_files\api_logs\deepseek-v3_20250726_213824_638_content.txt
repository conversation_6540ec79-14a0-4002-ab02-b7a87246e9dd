=== API调用成功 ===
时间: 2025-07-26 21:42:36
模型: deepseek-v3
内容长度: 26180 字符

=== 生成内容 ===
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技人才总数概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- SLOT::header::BEGIN -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技人才总数概览</h1>
            <p class="text-gray-600">宁波市科技人才数据总览与分析</p>
        </div>
        <!-- SLOT::header::END -->

        <!-- SLOT::filter-section::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">人才类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option>全部类型</option>
                        <option>研发人员</option>
                        <option>科研管理人员</option>
                        <option>专家</option>
                        <option>科技特派员</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属区域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option>全市</option>
                        <option>海曙区</option>
                        <option>江北区</option>
                        <option>鄞州区</option>
                        <option>余姚市</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option>全部行业</option>
                        <option>电子信息</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>智能制造</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end mt-4">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">查询</button>
            </div>
        </div>
        <!-- SLOT::filter-section::END -->

        <!-- SLOT::cards-section::BEGIN -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- CHUNK::card-rd-personnel::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">研发人员</h3>
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">12,847</p>
                    <p class="text-sm text-gray-500 mt-2">同比增长 8.5%</p>
                </div>
            </div>
            <!-- CHUNK::card-rd-personnel::END -->

            <!-- CHUNK::card-research-manager::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">科研管理人员</h3>
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">3,245</p>
                    <p class="text-sm text-gray-500 mt-2">同比增长 5.2%</p>
                </div>
            </div>
            <!-- CHUNK::card-research-manager::END -->

            <!-- CHUNK::card-expert::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">专家</h3>
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">1,876</p>
                    <p class="text-sm text-gray-500 mt-2">同比增长 12.3%</p>
                </div>
            </div>
            <!-- CHUNK::card-expert::END -->

            <!-- CHUNK::card-tech-specialist::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">科技特派员</h3>
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">856</p>
                    <p class="text-sm text-gray-500 mt-2">同比增长 15.7%</p>
                </div>
            </div>
            <!-- CHUNK::card-tech-specialist::END -->
        </div>
        <!-- SLOT::cards-section::END -->

        <!-- SLOT::chart-section::BEGIN -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">人才区域分布</h2>
                <div class="h-80">
                    <canvas id="regionChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">人才行业分布</h2>
                <div class="h-80">
                    <canvas id="industryChart"></canvas>
                </div>
            </div>
        </div>
        <!-- SLOT::chart-section::END -->

        <!-- SLOT::talent-list::BEGIN -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">人才列表</h2>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">导出数据</button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::talent-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张三</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">电子信息</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">35</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-1::END -->
                        <!-- CHUNK::talent-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李四</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研管理人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-2::END -->
                        <!-- CHUNK::talent-row-3::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王五</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专家</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">56</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-3::END -->
                        <!-- CHUNK::talent-row-4::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">赵六</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技特派员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX农业科技公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农业科技</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">38</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-4::END -->
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-4 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::talent-list::END -->
    </div>

    <!-- SLOT::detail-modal::BEGIN -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">人才详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="md:col-span-1">
                        <!-- CHUNK::detail-basic-info::BEGIN -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-xl font-bold text-gray-900" id="detail-name">张三</h4>
                                    <p class="text-sm text-gray-500" id="detail-type">研发人员</p>
                                </div>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <span class="text-gray-500">所属单位：</span>
                                    <span class="font-medium text-gray-900" id="detail-company">宁波市XX科技有限公司</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">行业领域：</span>
                                    <span class="font-medium text-gray-900" id="detail-industry">电子信息</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">年龄：</span>
                                    <span class="font-medium text-gray-900" id="detail-age">35</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">学历：</span>
                                    <span class="font-medium text-gray-900" id="detail-education">博士</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">职称：</span>
                                    <span class="font-medium text-gray-900" id="detail-title">高级工程师</span>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::detail-basic-info::END -->
                    </div>
                    <div class="md:col-span-2">
                        <!-- CHUNK::detail-project-info::BEGIN -->
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">参与项目</h4>
                            <div class="space-y-2">
                                <div class="border-b pb-2">
                                    <p class="font-medium text-gray-900">宁波市智能制造关键技术研发</p>
                                    <p class="text-sm text-gray-500">2022-2024 | 项目负责人</p>
                                </div>
                                <div class="border-b pb-2">
                                    <p class="font-medium text-gray-900">新一代信息技术应用研究</p>
                                    <p class="text-sm text-gray-500">2021-2023 | 核心成员</p>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::detail-project-info::END -->
                        
                        <!-- CHUNK::detail-award-info::BEGIN -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">获奖情况</h4>
                            <div class="space-y-2">
                                <div class="border-b pb-2">
                                    <p class="font-medium text-gray-900">宁波市科技进步一等奖</p>
                                    <p class="text-sm text-gray-500">2023年</p>
                                </div>
                                <div class="border-b pb-2">
                                    <p class="font-medium text-gray-900">浙江省优秀科技工作者</p>
                                    <p class="text-sm text-gray-500">2022年</p>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::detail-award-info::END -->
                    </div>
                </div>
                <div class="flex justify-end mt-6">
                    <button onclick="closeDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::detail-modal::END -->

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            const modal = document.getElementById('detailModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                
                // 模拟根据ID加载数据
                const talentData = getTalentDataById(id);
                if (talentData) {
                    document.getElementById('detail-name').textContent = talentData.name;
                    document.getElementById('detail-type').textContent = talentData.type;
                    document.getElementById('detail-company').textContent = talentData.company;
                    document.getElementById('detail-industry').textContent = talentData.industry;
                    document.getElementById('detail-age').textContent = talentData.age;
                    document.getElementById('detail-education').textContent = talentData.education;
                    document.getElementById('detail-title').textContent = talentData.title;
                }
            }
        }

        function closeDetailModal() {
            const modal = document.getElementById('detailModal');
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // 模拟数据获取
        function getTalentDataById(id) {
            const data = {
                '1': {
                    name: '张三',
                    type: '研发人员',
                    company: '宁波市XX科技有限公司',
                    industry: '电子信息',
                    age: 35,
                    education: '博士',
                    title: '高级工程师'
                },
                '2': {
                    name: '李四',
                    type: '科研管理人员',
                    company: '宁波市XX研究院',
                    industry: '生物医药',
                    age: 42,
                    education: '硕士',
                    title: '研究员'
                },
                '3': {
                    name: '王五',
                    type: '专家',
                    company: '宁波大学',
                    industry: '新材料',
                    age: 56,
                    education: '博士',
                    title: '教授'
                },
                '4': {
                    name: '赵六',
                    type: '科技特派员',
                    company: '宁波市XX农业科技公司',
                    industry: '农业科技',
                    age: 38,
                    education: '本科',
                    title: '农艺师'
                }
            };
            return data[id] || null;
        }

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 区域分布图表
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'bar',
                data: {
                    labels: ['海曙区', '江北区', '鄞州区', '余姚市', '慈溪市', '宁海县'],
                    datasets: [{
                        label: '人才数量',
                        data: [3245, 2876, 5123, 1987, 1564, 987],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 行业分布图表
            const industryCtx = document.getElementById('industryChart').getContext('2d');
            new Chart(industryCtx, {
                type: 'pie',
                data: {
                    labels: ['电子信息', '生物医药', '新材料', '智能制造', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(245, 158, 11, 0.7)',
                            'rgba(239, 68, 68, 0.7)',
                            'rgba(156, 163, 175, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>
<!-- END -->