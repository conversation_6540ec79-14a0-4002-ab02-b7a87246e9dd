<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试UI原型</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>测试UI原型</h1>
            <p>现代化管理系统界面设计</p>
        </div>
        
        <div class="content">
            <h2>用户信息管理</h2>
            
            <form>
                <div class="form-group">
                    <label for="username">用户名：</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名">
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱：</label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-group">
                    <label for="role">角色：</label>
                    <select id="role" name="role">
                        <option value="">请选择角色</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                        <option value="guest">访客</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="description">描述：</label>
                    <textarea id="description" name="description" rows="4" placeholder="请输入用户描述"></textarea>
                </div>
                
                <button type="submit" class="btn">保存</button>
                <button type="reset" class="btn btn-secondary">重置</button>
            </form>
            
            <h3>用户列表</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td><EMAIL></td>
                        <td>管理员</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>2024-01-01 10:00:00</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>user001</td>
                        <td><EMAIL></td>
                        <td>普通用户</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>2024-01-02 14:30:00</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>guest</td>
                        <td><EMAIL></td>
                        <td>访客</td>
                        <td><span class="status-inactive">禁用</span></td>
                        <td>2024-01-03 09:15:00</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>