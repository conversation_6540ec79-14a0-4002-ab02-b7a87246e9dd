#!/usr/bin/env python3
"""
Word图像插入功能测试脚本

测试完整的Word图像插入工作流程
"""

import asyncio
import json
import sys
from pathlib import Path
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, str(Path.cwd()))

async def test_word_insertion():
    """测试Word图像插入功能"""
    print("🧪 开始测试Word图像插入功能")
    print("=" * 50)
    
    try:
        # 导入模块
        from word_image_insertion import WordImageInserter, Config
        from word_image_insertion.utils.helpers import (
            create_sample_document, 
            create_sample_svg, 
            create_sample_html,
            validate_dependencies
        )
        
        print("✅ 模块导入成功")
        
        # 验证依赖
        print("\n🔍 验证依赖...")
        deps_result = validate_dependencies()
        
        if not deps_result['all_ok']:
            print("❌ 依赖验证失败:")
            for error in deps_result['errors']:
                print(f"  - {error}")
            for warning in deps_result['warnings']:
                print(f"  ⚠️ {warning}")
            return False
        
        print("✅ 所有依赖验证通过")
        
        # 创建测试目录
        test_dir = Path(tempfile.mkdtemp(prefix="word_insertion_test_"))
        print(f"\n📁 测试目录: {test_dir}")
        
        try:
            # 创建测试文件
            print("\n📝 创建测试文件...")
            
            doc_path = create_sample_document(
                str(test_dir / "test_document.docx"), 
                include_images=True
            )
            svg_path = create_sample_svg(
                str(test_dir / "test_flowchart.svg"),
                "业务流程图"
            )
            html_path = create_sample_html(
                str(test_dir / "test_ui.html"),
                "操作界面原型"
            )
            
            print(f"✅ 测试文档: {Path(doc_path).name}")
            print(f"✅ 测试SVG: {Path(svg_path).name}")
            print(f"✅ 测试HTML: {Path(html_path).name}")
            
            # 初始化Word插图器
            print("\n🔧 初始化Word插图器...")
            config = Config()
            inserter = WordImageInserter(config)
            
            # 测试文档解析
            print("\n📖 测试文档解析...")
            insertion_points = inserter.parse_document(
                doc_path, 
                keyword_pattern="业务流程"
            )
            
            if insertion_points:
                print(f"✅ 找到 {len(insertion_points)} 个插入点")
                for i, point in enumerate(insertion_points):
                    print(f"  {i+1}. 段落 {point.paragraph_index}: {point.text[:50]}...")
            else:
                print("⚠️ 未找到插入点，创建默认插入点")
                # 创建一个默认的插入点用于测试
                from word_image_insertion.core.document_parser import InsertionPoint
                from docx import Document
                doc = Document(doc_path)
                insertion_points = [InsertionPoint(
                    paragraph_index=len(doc.paragraphs) - 1,
                    paragraph=doc.paragraphs[-1],
                    style_name='Normal',
                    text='测试插入点',
                    match_type='test',
                    match_content='test'
                )]
            
            # 测试图像生成
            print("\n🖼️ 测试图像生成...")
            
            # 生成SVG图像
            svg_image_path = await inserter.image_generator.generate_async(
                svg_path,
                output_path=str(test_dir / "generated_svg.png")
            )
            print(f"✅ SVG图像生成: {Path(svg_image_path).name}")
            
            # 生成HTML图像
            html_image_path = await inserter.image_generator.generate_async(
                html_path,
                output_path=str(test_dir / "generated_html.png")
            )
            print(f"✅ HTML图像生成: {Path(html_image_path).name}")
            
            # 测试Word插入
            print("\n📄 测试Word插入...")
            
            output_doc_path = str(test_dir / "output_document.docx")
            
            # 插入SVG图像
            result_path = inserter.insert_image(
                doc_path,
                insertion_points[0],
                svg_image_path,
                output_doc_path
            )
            
            print(f"✅ 图像插入成功: {Path(result_path).name}")
            
            # 测试批量处理
            print("\n🔄 测试批量处理...")
            
            batch_configs = [
                {
                    'source': svg_path,
                    'keyword_pattern': '业务流程',
                    'options': {'image_width': 5.0, 'format': 'png'}
                },
                {
                    'source': html_path,
                    'keyword_pattern': '操作流程',
                    'options': {'image_width': 5.5, 'format': 'png'}
                }
            ]
            
            batch_result = await inserter.process_batch(
                doc_path,
                batch_configs,
                str(test_dir / "batch_output.docx")
            )
            
            print(f"✅ 批量处理完成:")
            print(f"  - 成功: {len(batch_result['success'])}")
            print(f"  - 失败: {len(batch_result['failed'])}")
            print(f"  - 输出: {Path(batch_result['output_path']).name}")
            
            # 测试预览功能
            print("\n👁️ 测试预览功能...")
            
            try:
                preview_path = inserter.preview_document(
                    batch_result['output_path'],
                    'pdf'
                )
                print(f"✅ PDF预览生成: {Path(preview_path).name}")
            except Exception as e:
                print(f"⚠️ 预览功能测试失败: {e}")
                print("  (这可能是因为LibreOffice未安装)")
            
            # 显示测试结果
            print(f"\n📊 测试结果:")
            print(f"  - 测试目录: {test_dir}")
            print(f"  - 生成文件数: {len(list(test_dir.glob('*')))}")
            
            for file_path in sorted(test_dir.glob('*')):
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"    {file_path.name} ({size_mb:.2f} MB)")
            
            print("\n🎉 所有测试通过！")
            return True
            
        finally:
            # 清理测试文件（可选）
            try:
                # shutil.rmtree(test_dir)
                # print(f"🧹 清理测试目录: {test_dir}")
                print(f"💾 测试文件保留在: {test_dir}")
            except Exception as e:
                print(f"⚠️ 清理失败: {e}")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请先运行: python setup_word_insertion.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_integration():
    """测试API集成"""
    print("\n🌐 测试API集成...")
    
    try:
        import httpx
        
        # 测试健康检查
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/api/health")
                if response.status_code == 200:
                    health_data = response.json()
                    print("✅ API健康检查通过")
                    print(f"  状态: {health_data.get('status')}")
                    
                    services = health_data.get('services', {})
                    if 'word_image_insertion' in services:
                        print("✅ Word图像插入服务可用")
                    else:
                        print("⚠️ Word图像插入服务未在健康检查中")
                else:
                    print(f"⚠️ API健康检查失败: {response.status_code}")
            except httpx.ConnectError:
                print("⚠️ 无法连接到API服务器")
                print("  请确保FastAPI服务器正在运行: uvicorn app.main:app --reload")
                
    except ImportError:
        print("⚠️ httpx未安装，跳过API测试")

def main():
    """主函数"""
    print("🚀 Word图像插入功能完整测试")
    print("=" * 60)
    
    # 运行异步测试
    success = asyncio.run(test_word_insertion())
    
    if success:
        # 测试API集成
        asyncio.run(test_api_integration())
        
        print("\n✨ 测试总结:")
        print("✅ 核心功能测试通过")
        print("✅ 模块集成正常")
        print("✅ 文件生成成功")
        
        print("\n📋 下一步:")
        print("1. 启动FastAPI服务器")
        print("2. 在前端测试Word导出功能")
        print("3. 验证完整的工作流程")
        
        return 0
    else:
        print("\n❌ 测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
