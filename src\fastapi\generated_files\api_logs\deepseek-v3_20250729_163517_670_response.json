{"timestamp": "2025-07-29 16:41:27", "status_code": 200, "headers": {"server": "istio-envoy", "date": "<PERSON><PERSON>, 29 Jul 2025 08:41:30 GMT", "content-type": "application/json; charset=utf-8", "content-length": "50816", "x-client-request-id": "unknown-20250729163522-AanSABzz", "x-envoy-upstream-service-time": "369649", "x-request-id": "0217537781218655667e58f5653ea62900d719dbb212d3d83f46a"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "根据需求分析，这是一个典型的\"管理后台\"类功能，我将按照管理后台的设计心法和代码范例来开发科技项目管理页面。以下是完整的HTML代码：\n\n```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>科技项目管理</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#3b82f6',\n                        secondary: '#64748b'\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <div class=\"container mx-auto px-4 py-6\">\n        <!-- SLOT::header::BEGIN -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-3xl font-bold text-gray-900 mb-2\">科技项目管理</h1>\n            <p class=\"text-gray-600\">为各级科技项目提供全生命周期的标准化管理平台，支持项目新增、修改、删除、查询、导出和批量导入操作</p>\n        </div>\n        <!-- SLOT::header::END -->\n\n        <!-- SLOT::filters::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n            <h2 class=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\" />\n                </svg>\n                高级检索\n            </h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                <!-- CHUNK::filter-project-name::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目名称</label>\n                    <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入项目名称\">\n                </div>\n                <!-- CHUNK::filter-project-name::END -->\n                \n                <!-- CHUNK::filter-company::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">承担单位</label>\n                    <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入承担单位\">\n                </div>\n                <!-- CHUNK::filter-company::END -->\n                \n                <!-- CHUNK::filter-credit-code::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">统一社会信用代码</label>\n                    <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入信用代码\">\n                </div>\n                <!-- CHUNK::filter-credit-code::END -->\n                \n                <!-- CHUNK::filter-batch::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">申报批次</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        <option value=\"\">全部批次</option>\n                        <option value=\"2023-1\">2023年第一批</option>\n                        <option value=\"2023-2\">2023年第二批</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-batch::END -->\n                \n                <!-- CHUNK::filter-status::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目状态</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        <option value=\"\">全部状态</option>\n                        <option value=\"declared\">已申报</option>\n                        <option value=\"approved\">已立项</option>\n                        <option value=\"in-progress\">执行中</option>\n                        <option value=\"completed\">已完成</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-status::END -->\n                \n                <!-- CHUNK::filter-year::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">申报年度</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        <option value=\"\">全部年度</option>\n                        <option value=\"2023\">2023年</option>\n                        <option value=\"2022\">2022年</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-year::END -->\n            </div>\n            <div class=\"flex justify-end space-x-3 mt-4\">\n                <!-- CHUNK::filter-reset-button::BEGIN -->\n                <button class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    重置\n                </button>\n                <!-- CHUNK::filter-reset-button::END -->\n                \n                <!-- CHUNK::filter-search-button::BEGIN -->\n                <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    查询\n                </button>\n                <!-- CHUNK::filter-search-button::END -->\n            </div>\n        </div>\n        <!-- SLOT::filters::END -->\n\n        <!-- SLOT::actions::BEGIN -->\n        <div class=\"flex justify-between items-center mb-4\">\n            <div class=\"text-sm text-gray-600\">\n                共 <span class=\"font-medium text-gray-900\">156</span> 个项目\n            </div>\n            <div class=\"flex space-x-3\">\n                <!-- CHUNK::action-export-button::BEGIN -->\n                <button class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    导出\n                </button>\n                <!-- CHUNK::action-export-button::END -->\n                \n                <!-- CHUNK::action-import-button::BEGIN -->\n                <button class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                    </svg>\n                    导入\n                </button>\n                <!-- CHUNK::action-import-button::END -->\n                \n                <!-- CHUNK::action-add-button::BEGIN -->\n                <button onclick=\"openModal('addProjectModal')\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                    新增项目\n                </button>\n                <!-- CHUNK::action-add-button::END -->\n            </div>\n        </div>\n        <!-- SLOT::actions::END -->\n\n        <!-- SLOT::main-table::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md overflow-hidden\">\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <!-- CHUNK::table-header-checkbox::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </th>\n                            <!-- CHUNK::table-header-checkbox::END -->\n                            \n                            <!-- CHUNK::table-header-name::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目名称</th>\n                            <!-- CHUNK::table-header-name::END -->\n                            \n                            <!-- CHUNK::table-header-code::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目编号</th>\n                            <!-- CHUNK::table-header-code::END -->\n                            \n                            <!-- CHUNK::table-header-company::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">承担单位</th>\n                            <!-- CHUNK::table-header-company::END -->\n                            \n                            <!-- CHUNK::table-header-leader::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">负责人</th>\n                            <!-- CHUNK::table-header-leader::END -->\n                            \n                            <!-- CHUNK::table-header-type::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目属性</th>\n                            <!-- CHUNK::table-header-type::END -->\n                            \n                            <!-- CHUNK::table-header-date::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">起止时间</th>\n                            <!-- CHUNK::table-header-date::END -->\n                            \n                            <!-- CHUNK::table-header-status::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目状态</th>\n                            <!-- CHUNK::table-header-status::END -->\n                            \n                            <!-- CHUNK::table-header-actions::BEGIN -->\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                            <!-- CHUNK::table-header-actions::END -->\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <!-- CHUNK::table-row-1::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">宁波市智能制造关键技术研发</div>\n                                <div class=\"text-sm text-gray-500\">2023年重点研发计划</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">NB2023RD001</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">宁波市XX科技有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">张研究员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">重点研发</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                <div>2023-01-15</div>\n                                <div>2024-12-31</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">执行中</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('detailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button onclick=\"confirmDelete()\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::table-row-1::END -->\n                        \n                        <!-- CHUNK::table-row-2::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">宁波市新材料产业化应用研究</div>\n                                <div class=\"text-sm text-gray-500\">2023年科技成果转化</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">NB2023CG002</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">宁波市YY材料研究院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">李教授</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">成果转化</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                                <div>2023-03-10</div>\n                                <div>2023-12-31</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">已立项</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('detailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button onclick=\"confirmDelete()\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::table-row-2::END -->\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-700\">\n                        显示 <span class=\"font-medium\">1</span> 到 <span class=\"font-medium\">10</span> 条，共 <span class=\"font-medium\">156</span> 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700\">1</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::main-table::END -->\n    </div>\n\n    <!-- SLOT::add-project-modal::BEGIN -->\n    <div id=\"addProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">新增科技项目</h3>\n                    <button onclick=\"closeModal('addProjectModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                    </button>\n                </div>\n                <form class=\"space-y-4\">\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <!-- CHUNK::modal-project-name::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目名称 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::modal-project-name::END -->\n                        \n                        <!-- CHUNK::modal-project-code::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目编号</label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::modal-project-code::END -->\n                        \n                        <!-- CHUNK::modal-project-type::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目属性 <span class=\"text-red-500\">*</span></label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                                <option value=\"\">请选择项目属性</option>\n                                <option value=\"key\">重点研发</option>\n                                <option value=\"transform\">成果转化</option>\n                                <option value=\"basic\">基础研究</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::modal-project-type::END -->\n                        \n                        <!-- CHUNK::modal-project-level::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目级别</label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"national\">国家级</option>\n                                <option value=\"provincial\">省级</option>\n                                <option value=\"municipal\" selected>市级</option>\n                                <option value=\"district\">区级</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::modal-project-level::END -->\n                        \n                        <!-- CHUNK::modal-company::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">承担单位 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::modal-company::END -->\n                        \n                        <!-- CHUNK::modal-credit-code::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">统一社会信用代码</label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::modal-credit-code::END -->\n                        \n                        <!-- CHUNK::modal-leader::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目负责人 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::modal-leader::END -->\n                        \n                        <!-- CHUNK::modal-phone::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">联系电话</label>\n                            <input type=\"tel\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::modal-phone::END -->\n                        \n                        <!-- CHUNK::modal-start-date::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">开始日期 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::modal-start-date::END -->\n                        \n                        <!-- CHUNK::modal-end-date::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">结束日期</label>\n                            <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::modal-end-date::END -->\n                        \n                        <!-- CHUNK::modal-funds::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目经费（万元）</label>\n                            <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::modal-funds::END -->\n                        \n                        <!-- CHUNK::modal-status::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目状态</label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"declared\">已申报</option>\n                                <option value=\"approved\">已立项</option>\n                                <option value=\"in-progress\">执行中</option>\n                                <option value=\"completed\">已完成</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::modal-status::END -->\n                    </div>\n                    \n                    <!-- CHUNK::modal-description::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">项目简介</label>\n                        <textarea rows=\"3\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"></textarea>\n                    </div>\n                    <!-- CHUNK::modal-description::END -->\n                    \n                    <!-- CHUNK::modal-attachments::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">附件上传</label>\n                        <div class=\"border-2 border-dashed border-gray-300 rounded-md p-6 text-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                            </svg>\n                            <div class=\"mt-4\">\n                                <label for=\"file-upload\" class=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none\">\n                                    <span>点击上传文件</span>\n                                    <input id=\"file-upload\" name=\"file-upload\" type=\"file\" class=\"sr-only\">\n                                </label>\n                                <p class=\"text-xs text-gray-500 mt-1\">支持PDF、Word、Excel文件，最大10MB</p>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::modal-attachments::END -->\n                    \n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <!-- CHUNK::modal-cancel-button::BEGIN -->\n                        <button type=\"button\" onclick=\"closeModal('addProjectModal')\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            取消\n                        </button>\n                        <!-- CHUNK::modal-cancel-button::END -->\n                        \n                        <!-- CHUNK::modal-submit-button::BEGIN -->\n                        <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            保存\n                        </button>\n                        <!-- CHUNK::modal-submit-button::END -->\n                    </div>\n                </form>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::add-project-modal::END -->\n\n    <!-- SLOT::detail-modal::BEGIN -->\n    <div id=\"detailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">项目详情</h3>\n                    <button onclick=\"closeModal('detailModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                    </button>\n                </div>\n                \n                <div class=\"space-y-6\">\n                    <!-- CHUNK::detail-basic-info::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">基本信息</h4>\n                        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                            <div>\n                                <span class=\"text-gray-500\">项目名称：</span>\n                                <span class=\"font-medium text-gray-900\">宁波市智能制造关键技术研发</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目编号：</span>\n                                <span class=\"font-medium text-gray-900\">NB2023RD001</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目属性：</span>\n                                <span class=\"font-medium text-gray-900\">重点研发</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目级别：</span>\n                                <span class=\"font-medium text-gray-900\">市级</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">承担单位：</span>\n                                <span class=\"font-medium text-gray-900\">宁波市XX科技有限公司</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">信用代码：</span>\n                                <span class=\"font-medium text-gray-900\">91330201MA2XXXXXX</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">负责人：</span>\n                                <span class=\"font-medium text-gray-900\">张研究员</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">联系电话：</span>\n                                <span class=\"font-medium text-gray-900\">138XXXX8888</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">起止时间：</span>\n                                <span class=\"font-medium text-gray-900\">2023-01-15 至 2024-12-31</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目经费：</span>\n                                <span class=\"font-medium text-gray-900\">500万元</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目状态：</span>\n                                <span class=\"font-medium text-gray-900\">执行中</span>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-basic-info::END -->\n                    \n                    <!-- CHUNK::detail-description::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">项目简介</h4>\n                        <div class=\"text-sm text-gray-700\">\n                            <p>本项目旨在研发智能制造领域的关键技术，包括智能生产线设计、工业机器人应用、物联网技术集成等。通过本项目的实施，将提升宁波市制造业的智能化水平，预计可带动相关产业产值增长20%以上。</p>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-description::END -->\n                    \n                    <!-- CHUNK::detail-team::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">项目团队</h4>\n                        <div class=\"overflow-x-auto\">\n                            <table class=\"min-w-full divide-y divide-gray-200\">\n                                <thead class=\"bg-gray-100\">\n                                    <tr>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">姓名</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">单位</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">职称</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分工</th>\n                                    </tr>\n                                </thead>\n                                <tbody class=\"bg-white divide-y divide-gray-200\">\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">张研究员</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX科技有限公司</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高级工程师</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">项目负责人</td>\n                                    </tr>\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">李教授</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波大学</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">教授</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">技术指导</td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-team::END -->\n                    \n                    <!-- CHUNK::detail-attachments::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">相关附件</h4>\n                        <div class=\"space-y-2\">\n                            <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                <div class=\"flex items-center\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-gray-400 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                                    </svg>\n                                    <span class=\"text-sm font-medium text-gray-900\">项目申报书.pdf</span>\n                                </div>\n                                <button class=\"text-blue-600 hover:text-blue-900 text-sm\">下载</button>\n                            </div>\n                            <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                <div class=\"flex items-center\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-gray-400 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                                    </svg>\n                                    <span class=\"text-sm font-medium text-gray-900\">立项通知书.docx</span>\n                                </div>\n                                <button class=\"text-blue-600 hover:text-blue-900 text-sm\">下载</button>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-attachments::END -->\n                    \n                    <div class=\"flex justify-end space-x-3\">\n                        <!-- CHUNK::detail-export-button::BEGIN -->\n                        <button class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                            </svg>\n                            导出报告\n                        </button>\n                        <!-- CHUNK::detail-export-button::END -->\n                        \n                        <!-- CHUNK::detail-close-button::BEGIN -->\n                        <button onclick=\"closeModal('detailModal')\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            关闭\n                        </button>\n                        <!-- CHUNK::detail-close-button::END -->\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::detail-modal::END -->\n\n    <script>\n        // --- 全局UI控制函数 ---\n        function openModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden';\n            }\n        }\n\n        function closeModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto';\n            }\n        }\n\n        function confirmDelete() {\n            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {\n                alert('项目已删除 (演示)');\n            }\n        }\n\n        // --- DOM加载完毕后执行的初始化代码 ---\n        document.addEventListener('DOMContentLoaded', function() {\n            // 为所有弹窗绑定\"点击外部关闭\"事件\n            const modals = document.querySelectorAll('[id$=\"Modal\"]');\n            modals.forEach(modal => {\n                modal.addEventListener('click', function(event) {\n                    if (event.target === modal) {\n                        closeModal(modal.id);\n                    }\n                });\n\n                // 绑定ESC键关闭\n                document.addEventListener('keydown', function (e) {\n                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {\n                        closeModal(modal.id);\n                    }\n                });\n            });\n            \n            // 表单提交处理（用于原型）\n            document.querySelectorAll('form').forEach(form => {\n                form.addEventListener('submit', function(e) {\n                    e.preventDefault();\n                    alert('表单已提交 (演示)');\n                    const parentModalId = form.closest('[id$=\"Modal\"]')?.id;\n                    if (parentModalId) closeModal(parentModalId);\n                });\n            });\n        });\n    </script>\n</body>\n</html>\n<!-- END -->\n```", "role": "assistant"}}], "created": 1753778491, "id": "0217537781218655667e58f5653ea62900d719dbb212d3d83f46a", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 9508, "prompt_tokens": 39159, "total_tokens": 48667, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}