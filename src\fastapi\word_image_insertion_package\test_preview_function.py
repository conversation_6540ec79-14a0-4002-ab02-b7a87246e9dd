#!/usr/bin/env python3
"""
测试预览功能
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

from word_image_insertion import WordImageInserter, Config
from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager

def test_preview_function(use_enhanced: bool = True):
    """测试预览功能"""
    
    preview_type = "增强预览" if use_enhanced else "标准预览"
    print(f"🔍 {preview_type}功能测试")
    print("=" * 50)
    
    # 创建预览管理器
    if use_enhanced:
        preview_manager = EnhancedPreviewManager()
        print("📄 使用增强预览模式（PDF+HTML双视图同步）")
    else:
        config = Config()
        inserter = WordImageInserter(config)
        preview_manager = inserter.preview_manager
        print("📄 使用标准预览模式")

    # 检查LibreOffice是否可用
    print("1. 检查LibreOffice安装状态...")
    libreoffice_path = preview_manager._find_libreoffice()
    
    if libreoffice_path:
        print(f"✅ LibreOffice已安装: {libreoffice_path}")
    else:
        print("❌ LibreOffice未安装或未找到")
        print("\n📋 安装指南:")
        print("1. 访问: https://www.libreoffice.org/download/")
        print("2. 下载适合你系统的版本")
        print("3. 安装后重新运行此测试")
        return False
    
    # 测试文档路径（优先使用完整测试生成的文档）
    test_docs = [
        "complete_test_output/final_complete_document.docx",
        "test_simple_output.docx",
        "test_operation_flow_output.docx"
    ]

    test_doc = None
    for doc_path in test_docs:
        if Path(doc_path).exists():
            test_doc = doc_path
            break

    if not test_doc:
        print(f"❌ 没有找到测试文档，请先运行以下任一测试:")
        print("  - python test_complete_workflow.py")
        print("  - python test_simple_insertion.py")
        print("  - python test_operation_flow_insertion.py")
        return False

    print(f"📄 使用测试文档: {test_doc}")
    
    try:
        if use_enhanced:
            # 增强预览模式
            print(f"\n2. 生成增强预览（PDF+HTML双视图同步）...")
            print(f"   源文档: {test_doc}")

            # 创建双视图预览
            viewer_path = preview_manager.create_dual_view_preview(test_doc)
            print(f"✅ 增强预览生成成功: {Path(viewer_path).name}")

            # 检查预览文件
            if Path(viewer_path).exists():
                size_kb = Path(viewer_path).stat().st_size / 1024
                print(f"   预览页面大小: {size_kb:.2f} KB")

                # 打开增强预览
                success = preview_manager.open_preview(viewer_path)
                if success:
                    print("🎭 增强预览已在浏览器中打开")
                    print("💡 功能特性:")
                    print("   📄 左侧PDF视图（带书签导航）")
                    print("   📝 右侧HTML视图（带锚点跳转）")
                    print("   🔄 双向同步滚动")
                    print("   🎨 现代化交互界面")
                else:
                    print("❌ 预览打开失败")
                    return False
            else:
                print("❌ 预览文件未生成")
                return False

        else:
            # 标准预览模式
            print(f"\n2. 测试PDF预览...")
            print(f"   源文档: {test_doc}")

            # 生成PDF预览
            pdf_path = inserter.preview_document(test_doc, 'pdf')
            print(f"✅ PDF预览生成成功: {pdf_path}")

            # 检查PDF文件是否存在
            if Path(pdf_path).exists():
                size_mb = Path(pdf_path).stat().st_size / (1024 * 1024)
                print(f"   文件大小: {size_mb:.2f} MB")

                # 尝试打开PDF
                import webbrowser
                webbrowser.open(f"file:///{Path(pdf_path).absolute().as_posix()}")
                print("📖 PDF已在浏览器中打开")
            else:
                print("❌ PDF文件未生成")
                return False

            print(f"\n3. 测试HTML预览...")

            # 生成HTML预览
            html_path = inserter.preview_document(test_doc, 'html')
            print(f"✅ HTML预览生成成功: {html_path}")

            # 检查HTML文件是否存在
            if Path(html_path).exists():
                size_kb = Path(html_path).stat().st_size / 1024
                print(f"   文件大小: {size_kb:.2f} KB")

                # 尝试打开HTML
                import webbrowser
                webbrowser.open(f"file:///{Path(html_path).absolute().as_posix()}")
                print("🌐 HTML已在浏览器中打开")
            else:
                print("❌ HTML文件未生成")
                return False
        
        print(f"\n✅ 预览功能测试通过！")
        print(f"📁 生成的预览文件:")
        print(f"   PDF: {pdf_path}")
        print(f"   HTML: {html_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预览功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_libreoffice_installation():
    """检查LibreOffice安装状态"""
    
    print("🔍 LibreOffice安装检查")
    print("=" * 50)
    
    import subprocess
    import os
    
    # 检查常见的安装路径
    windows_paths = [
        r"C:\Program Files\LibreOffice\program\soffice.exe",
        r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
    ]
    
    print("1. 检查常见安装路径...")
    for path in windows_paths:
        if Path(path).exists():
            print(f"✅ 找到: {path}")
            try:
                result = subprocess.run([path, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"   版本: {version}")
                    return True
            except:
                print(f"   ⚠️  文件存在但无法执行")
        else:
            print(f"❌ 未找到: {path}")
    
    print("\n2. 检查PATH环境变量...")
    for cmd in ['soffice', 'libreoffice']:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ 在PATH中找到命令: {cmd}")
                version = result.stdout.strip()
                print(f"   版本: {version}")
                return True
        except:
            print(f"❌ PATH中未找到命令: {cmd}")
    
    print("\n❌ LibreOffice未安装或未正确配置")
    print("\n📋 安装建议:")
    print("1. 下载LibreOffice: https://www.libreoffice.org/download/")
    print("2. 选择Windows版本下载")
    print("3. 运行安装程序，使用默认设置")
    print("4. 安装完成后重新运行此测试")
    
    return False

def main():
    """主函数"""

    print("🔍 Word文档预览功能测试")
    print("=" * 50)

    # 首先检查LibreOffice安装
    if not check_libreoffice_installation():
        return

    print("\n" + "="*50)
    print("📋 选择预览模式:")
    print("  1. 增强预览（推荐）- PDF+HTML双视图同步")
    print("  2. 标准预览 - 传统PDF/HTML预览")
    print("="*50)

    while True:
        try:
            choice = input("请选择预览模式 (1/2，默认1): ").strip()
            if not choice:
                choice = "1"

            if choice == "1":
                use_enhanced = True
                break
            elif choice == "2":
                use_enhanced = False
                break
            else:
                print("❌ 请输入1或2")
        except KeyboardInterrupt:
            print("\n\n⏹️  测试被用户中断")
            return

    print("\n" + "="*50)

    # 测试预览功能
    success = test_preview_function(use_enhanced)

    if success:
        print(f"\n🎉 {'增强' if use_enhanced else '标准'}预览测试通过！")

        if use_enhanced:
            print("\n💡 增强预览特性:")
            print("  🎯 PDF+HTML双视图并排显示")
            print("  🔄 智能同步滚动和导航")
            print("  🎨 现代化交互界面")
            print("  📱 响应式布局设计")
            print("  ⌨️  丰富的快捷键支持")
        else:
            print("\n💡 标准预览特性:")
            print("  📄 基础PDF预览")
            print("  📝 基础HTML预览")
            print("  🔧 简单可靠的转换")
    else:
        print(f"\n❌ {'增强' if use_enhanced else '标准'}预览测试失败")

    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
