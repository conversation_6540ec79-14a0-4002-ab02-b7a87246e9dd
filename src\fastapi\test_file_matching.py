#!/usr/bin/env python3
"""
测试文件匹配逻辑
"""

import os
import json
import requests
import time

def create_test_files():
    """创建测试文件"""
    print("🧪 创建测试文件...")
    
    # 确保generated_files目录存在
    generated_files_dir = "generated_files"
    if not os.path.exists(generated_files_dir):
        os.makedirs(generated_files_dir)
        print(f"✅ 创建目录: {generated_files_dir}")
    
    # 创建测试SVG文件（模拟不同的命名模式）
    test_svg_files = [
        "flowchart_section_5_20250728_143000.svg",  # 模拟实际生成的文件名
        "flowchart_用户管理_20250728_143100.svg",
        "ui_prototype_section_5_20250728_143200.html",  # 模拟实际生成的文件名
        "ui_prototype_用户管理_20250728_143300.html",
        # 添加更多真实的文件名模式
        "flowchart_section-5-flowchart_20250728_143400.svg",  # 直接使用任务ID
        "ui_prototype_section-5-ui_prototype_20250728_143500.html"  # 直接使用任务ID
    ]
    
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect x="50" y="50" width="100" height="60" fill="lightblue" stroke="black"/>
  <text x="100" y="85" text-anchor="middle">测试流程图</text>
</svg>'''
    
    html_content = '''<!DOCTYPE html>
<html>
<head><title>测试UI原型</title></head>
<body>
  <h1>测试UI原型</h1>
  <p>这是一个测试的UI原型页面</p>
</body>
</html>'''
    
    for filename in test_svg_files:
        file_path = os.path.join(generated_files_dir, filename)
        content = svg_content if filename.endswith('.svg') else html_content
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 创建测试文件: {file_path}")
        
        # 稍微延迟以确保文件时间戳不同
        time.sleep(0.1)
    
    return test_svg_files

def test_file_matching_logic():
    """测试文件匹配逻辑"""
    print("\n🧪 测试文件匹配逻辑...")
    
    generated_files_dir = 'generated_files'
    if not os.path.exists(generated_files_dir):
        print("❌ generated_files目录不存在")
        return
    
    # 测试不同的任务ID
    test_cases = [
        {
            "task_id": "section-5-flowchart",
            "task_type": "flowchart",
            "expected_pattern": "flowchart_"
        },
        {
            "task_id": "section-5-ui_prototype", 
            "task_type": "ui_prototype",
            "expected_pattern": "ui_prototype_"
        }
    ]
    
    for case in test_cases:
        task_id = case["task_id"]
        task_type = case["task_type"]
        expected_pattern = case["expected_pattern"]
        
        print(f"\n📋 测试任务: {task_id} ({task_type})")
        
        if task_type == 'flowchart':
            # 多种查找策略
            svg_files = []
            
            # 策略1: 精确匹配任务ID
            svg_files = [f for f in os.listdir(generated_files_dir) if f.startswith(f'flowchart_{task_id}') and f.endswith('.svg')]
            print(f"  策略1 (精确匹配): {svg_files}")
            
            # 策略2: 如果任务ID包含section信息，尝试提取section部分
            if not svg_files and '-' in task_id:
                section_part = task_id.split('-')[1]  # 从section-5-flowchart提取5
                svg_files = [f for f in os.listdir(generated_files_dir) if f'section_{section_part}' in f and f.endswith('.svg')]
                print(f"  策略2 (section匹配): {svg_files}")
            
            # 策略3: 模糊匹配，查找最新的flowchart文件
            if not svg_files:
                all_svg_files = [f for f in os.listdir(generated_files_dir) if f.startswith('flowchart_') and f.endswith('.svg')]
                if all_svg_files:
                    # 按修改时间排序，取最新的
                    all_svg_files.sort(key=lambda x: os.path.getmtime(os.path.join(generated_files_dir, x)), reverse=True)
                    svg_files = [all_svg_files[0]]
                    print(f"  策略3 (最新文件): {svg_files}")
            
            if svg_files:
                print(f"  ✅ 找到文件: {svg_files[0]}")
            else:
                print(f"  ❌ 未找到文件")
        
        elif task_type == 'ui_prototype':
            # 多种查找策略
            html_files = []
            
            # 策略1: 精确匹配任务ID
            html_files = [f for f in os.listdir(generated_files_dir) if f.startswith(f'ui_prototype_{task_id}') and f.endswith('.html')]
            print(f"  策略1 (精确匹配): {html_files}")
            
            # 策略2: 如果任务ID包含section信息，尝试提取section部分
            if not html_files and '-' in task_id:
                section_part = task_id.split('-')[1]  # 从section-5-ui_prototype提取5
                html_files = [f for f in os.listdir(generated_files_dir) if f'section_{section_part}' in f and f.endswith('.html')]
                print(f"  策略2 (section匹配): {html_files}")
            
            # 策略3: 模糊匹配，查找最新的ui_prototype文件
            if not html_files:
                all_html_files = [f for f in os.listdir(generated_files_dir) if f.startswith('ui_prototype_') and f.endswith('.html')]
                if all_html_files:
                    # 按修改时间排序，取最新的
                    all_html_files.sort(key=lambda x: os.path.getmtime(os.path.join(generated_files_dir, x)), reverse=True)
                    html_files = [all_html_files[0]]
                    print(f"  策略3 (最新文件): {html_files}")
            
            if html_files:
                print(f"  ✅ 找到文件: {html_files[0]}")
            else:
                print(f"  ❌ 未找到文件")

def test_batch_insert_with_real_files():
    """使用真实文件测试批量插入"""
    print("\n🧪 测试批量插入API...")
    
    # 首先需要有一个测试文档
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        print("❌ uploads目录不存在，请先运行文档上传测试")
        return
    
    # 查找最新的上传文档
    uploaded_files = [f for f in os.listdir(uploads_dir) if f.endswith('.docx')]
    if not uploaded_files:
        print("❌ 没有找到已上传的文档")
        return
    
    # 使用最新的文档
    doc_path = os.path.join(uploads_dir, uploaded_files[0])
    print(f"📁 使用文档: {doc_path}")
    
    # 测试批量插入API
    api_url = "http://localhost:8000/api/word/batch-insert"
    request_data = {
        "doc_path": doc_path,
        "task_results": [
            {
                "id": "section-5-flowchart",
                "type": "flowchart",
                "status": "completed"
            },
            {
                "id": "section-5-ui_prototype",
                "type": "ui_prototype", 
                "status": "completed"
            }
        ],
        "config": {
            "business_flow_pattern": "三、业务流程",
            "operation_flow_pattern": "四、操作流程"
        }
    }
    
    try:
        print("🌐 发送批量插入请求...")
        response = requests.post(
            api_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(request_data)
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量插入API测试成功")
            print(f"📊 插入结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get('output_path')
        else:
            print(f"❌ 批量插入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 批量插入测试失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("🧪 开始测试文件匹配逻辑...")
    print("=" * 60)
    
    # 创建测试文件
    test_files = create_test_files()
    
    # 测试文件匹配逻辑
    test_file_matching_logic()
    
    # 测试批量插入API
    test_batch_insert_with_real_files()
    
    print("\n🏁 测试完成")
    print("=" * 60)
