#!/usr/bin/env python3
"""
Word精准插图系统 - 预览功能示例
"""

from pathlib import Path
from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager

def main():
    """预览功能示例"""
    
    print("🔍 Word文档预览功能示例")
    print("=" * 50)
    
    # 文件路径
    word_doc = "input/documents/your_document.docx"
    
    if not Path(word_doc).exists():
        print(f"❌ Word文档不存在: {word_doc}")
        print("请将Word文档放入 input/documents/ 目录")
        return
    
    try:
        # 创建增强预览管理器
        preview_manager = EnhancedPreviewManager()
        
        print(f"📄 文档: {word_doc}")
        print(f"🔧 LibreOffice: {preview_manager.libreoffice_path}")
        
        # 创建双视图预览
        print(f"🎭 创建增强预览（PDF+HTML双视图同步）...")
        viewer_path = preview_manager.create_dual_view_preview(word_doc)
        
        print(f"✅ 预览创建成功: {Path(viewer_path).name}")
        
        # 在浏览器中打开
        print(f"🌐 在浏览器中打开预览...")
        success = preview_manager.open_preview(viewer_path)
        
        if success:
            print("🎉 增强预览已打开！")
            print("💡 功能特性:")
            print("  📄 左侧PDF视图（带书签导航）")
            print("  📝 右侧HTML视图（带锚点跳转）")
            print("  🔄 双向同步滚动")
            print("  🎨 现代化交互界面")
        else:
            print("❌ 预览打开失败")
            
    except Exception as e:
        print(f"❌ 预览失败: {e}")

if __name__ == "__main__":
    main()
