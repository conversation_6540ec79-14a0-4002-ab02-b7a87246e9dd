"""
图片生成模块

集成Playwright截图功能和CairoSVG，实现高分辨率图片生成和尺寸处理。
支持多种图片源：网页截图、SVG转换、本地图片处理。
"""

import asyncio
import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse

from playwright.async_api import async_playwright, Browser, Page
from PIL import Image

logger = logging.getLogger(__name__)


class ImageGenerator:
    """图片生成器"""
    
    def __init__(self, config=None):
        self.config = config
        self.browser: Optional[Browser] = None
        self.playwright = None
        self.default_dpi = 300
        self.default_device_scale_factor = 3  # 提升到3倍缩放
        self.high_quality_mode = True  # 启用高质量模式
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        try:
            await self._close_browser()
        except Exception:
            pass  # 忽略清理时的异常
    
    def generate(self, source: str, selector: Optional[str] = None, 
                output_path: Optional[str] = None, **options) -> str:
        """
        生成图片（同步接口）
        
        Args:
            source: 图片源（URL、SVG文件路径等）
            selector: CSS选择器（用于网页截图）
            output_path: 输出路径
            **options: 其他选项
            
        Returns:
            str: 生成的图片路径
        """
        return asyncio.run(self.generate_async(source, selector, output_path, **options))
    
    async def generate_async(self, source: str, selector: Optional[str] = None,
                           output_path: Optional[str] = None, **options) -> str:
        """
        生成图片（异步接口）
        
        Args:
            source: 图片源（URL、SVG文件路径等）
            selector: CSS选择器（用于网页截图）
            output_path: 输出路径
            **options: 其他选项
            
        Returns:
            str: 生成的图片路径
        """
        logger.info(f"Generating image from source: {source}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(source, options.get('format', 'png'))
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 根据源类型选择生成方法
        source_type = self._detect_source_type(source)
        
        if source_type == 'url':
            image_path = await self._generate_from_url(source, selector, output_path, **options)
        elif source_type == 'svg':
            image_path = await self._generate_from_svg(source, output_path, **options)
        elif source_type == 'image':
            image_path = await self._process_existing_image(source, output_path, **options)
        else:
            raise ValueError(f"Unsupported source type: {source_type}")
        
        # 后处理：尺寸调整和格式转换
        final_path = await self._post_process_image(image_path, output_path, **options)
        
        logger.info(f"Image generated successfully: {final_path}")
        return str(final_path)
    
    def _detect_source_type(self, source: str) -> str:
        """检测源类型"""
        source_path = Path(source)
        
        # 检查是否是URL
        parsed = urlparse(source)
        if parsed.scheme in ('http', 'https'):
            return 'url'
        
        # 检查文件扩展名
        if source_path.suffix.lower() == '.svg':
            return 'svg'
        elif source_path.suffix.lower() in ('.png', '.jpg', '.jpeg', '.gif', '.bmp'):
            return 'image'
        elif source_path.suffix.lower() in ('.html', '.htm'):
            return 'url'  # HTML文件作为URL处理
        
        # 默认作为URL处理
        return 'url'
    
    def _generate_output_path(self, source: str, format: str = 'png') -> str:
        """生成输出路径"""
        source_path = Path(source)
        timestamp = int(asyncio.get_event_loop().time() * 1000)
        
        if source_path.exists():
            base_name = source_path.stem
        else:
            base_name = f"generated_{timestamp}"
        
        output_dir = Path(tempfile.gettempdir()) / "word_image_insertion"
        output_dir.mkdir(exist_ok=True)
        
        return str(output_dir / f"{base_name}_{timestamp}.{format}")
    
    async def _ensure_browser(self):
        """确保浏览器已启动"""
        if not self.browser:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps',
                    '--disable-extensions'
                ]
            )
            logger.debug("Browser launched successfully")
    
    async def _close_browser(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            self.browser = None
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
            logger.debug("Browser closed successfully")
    
    async def _generate_from_url(self, url: str, selector: Optional[str], 
                               output_path: Path, **options) -> str:
        """从URL生成图片"""
        await self._ensure_browser()
        
        page = await self.browser.new_page(
            device_scale_factor=self.default_device_scale_factor,
            viewport={'width': options.get('width', 1920), 'height': options.get('height', 1080)}
        )
        
        try:
            # 处理本地HTML文件
            if Path(url).exists() and Path(url).suffix.lower() in ('.html', '.htm'):
                url = f"file://{Path(url).absolute()}"
            
            logger.debug(f"Loading URL: {url}")
            await page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 等待页面完全加载
            await page.wait_for_timeout(2000)
            
            # 截图选项
            screenshot_options = {
                'path': str(output_path),
                'type': 'png',
                'full_page': not selector,
                'omit_background': options.get('transparent', False)
            }
            
            if selector:
                # 等待选择器出现
                await page.wait_for_selector(selector, timeout=10000)
                element = await page.query_selector(selector)
                if element:
                    await element.screenshot(**screenshot_options)
                else:
                    raise ValueError(f"Element not found: {selector}")
            else:
                await page.screenshot(**screenshot_options)
            
            logger.debug(f"Screenshot saved: {output_path}")
            return str(output_path)
            
        finally:
            await page.close()
    
    async def _generate_from_svg(self, svg_path: str, output_path: Path, **options) -> str:
        """从SVG生成图片"""
        svg_path = Path(svg_path)
        if not svg_path.exists():
            raise FileNotFoundError(f"SVG file not found: {svg_path}")
        
        # 使用Playwright渲染SVG
        await self._ensure_browser()
        
        page = await self.browser.new_page(
            device_scale_factor=self.default_device_scale_factor,
            viewport={'width': options.get('width', 1920), 'height': options.get('height', 1080)}
        )
        
        try:
            # 创建包含SVG的HTML页面
            svg_content = svg_path.read_text(encoding='utf-8')
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ margin: 0; padding: 20px; background: white; }}
                    svg {{ max-width: 100%; height: auto; }}
                </style>
            </head>
            <body>
                {svg_content}
            </body>
            </html>
            """
            
            await page.set_content(html_content, wait_until='networkidle')
            await page.wait_for_timeout(1000)
            
            # 截图
            await page.screenshot(
                path=str(output_path),
                type='png',
                full_page=True,
                omit_background=options.get('transparent', False)
            )
            
            logger.debug(f"SVG converted to PNG: {output_path}")
            return str(output_path)
            
        finally:
            await page.close()
    
    async def _process_existing_image(self, image_path: str, output_path: Path, **options) -> str:
        """处理现有图片"""
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        # 如果不需要处理，直接复制
        if not any(key in options for key in ['width', 'height', 'dpi', 'format']):
            import shutil
            shutil.copy2(image_path, output_path)
            return str(output_path)
        
        # 使用PIL处理图片
        with Image.open(image_path) as img:
            # 格式转换
            if img.mode in ('RGBA', 'LA') and options.get('format', 'png').lower() != 'png':
                img = img.convert('RGB')
            
            # 尺寸调整
            if 'width' in options or 'height' in options:
                width = options.get('width', img.width)
                height = options.get('height', img.height)
                img = img.resize((width, height), Image.Resampling.LANCZOS)
            
            # 保存
            save_options = {}
            if options.get('dpi'):
                save_options['dpi'] = (options['dpi'], options['dpi'])
            
            img.save(output_path, **save_options)
        
        logger.debug(f"Image processed: {output_path}")
        return str(output_path)
    
    async def _post_process_image(self, image_path: str, output_path: Path, **options) -> Path:
        """后处理图片"""
        if not options.get('post_process', True):
            return Path(image_path)
        
        # 如果已经是目标路径，无需处理
        if Path(image_path) == output_path:
            return output_path
        
        # 使用PIL进行后处理
        with Image.open(image_path) as img:
            # DPI设置
            dpi = options.get('dpi', self.default_dpi)
            
            # 质量优化
            if self.high_quality_mode:
                # 确保图片质量
                if img.mode != 'RGB' and output_path.suffix.lower() in ('.jpg', '.jpeg'):
                    img = img.convert('RGB')
            
            # 保存最终图片
            save_options = {
                'dpi': (dpi, dpi),
                'optimize': True
            }
            
            if output_path.suffix.lower() in ('.jpg', '.jpeg'):
                save_options['quality'] = 95
            
            img.save(output_path, **save_options)
        
        # 清理临时文件
        if Path(image_path) != output_path and Path(image_path).exists():
            Path(image_path).unlink()
        
        logger.debug(f"Post-processing completed: {output_path}")
        return output_path
