"""
图片生成模块

集成Playwright截图功能和CairoSVG，实现高分辨率图片生成和尺寸处理。
支持多种图片源：网页截图、SVG转换、本地图片处理。
"""

import asyncio
import logging
import tempfile
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse

# Windows 环境下设置事件循环策略以修复 Playwright 问题
if sys.platform == 'win32':
    try:
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    except:
        pass  # 如果已经设置过，忽略错误

from playwright.async_api import async_playwright, Browser, Page
from PIL import Image

logger = logging.getLogger(__name__)


class ImageGenerator:
    """图片生成器"""
    
    def __init__(self, config=None):
        self.config = config
        self.browser: Optional[Browser] = None
        self.playwright = None
        self.default_dpi = 300
        self.default_device_scale_factor = 3  # 提升到3倍缩放
        self.high_quality_mode = True  # 启用高质量模式
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        try:
            await self._close_browser()
        except Exception:
            pass  # 忽略清理时的异常
    
    def generate(self, source: str, selector: Optional[str] = None, 
                output_path: Optional[str] = None, **options) -> str:
        """
        生成图片（同步接口）
        
        Args:
            source: 图片源（URL、SVG文件路径等）
            selector: CSS选择器（用于网页截图）
            output_path: 输出路径
            **options: 其他选项
            
        Returns:
            str: 生成的图片路径
        """
        return asyncio.run(self.generate_async(source, selector, output_path, **options))
    
    async def generate_async(self, source: str, selector: Optional[str] = None,
                           output_path: Optional[str] = None, **options) -> str:
        """
        生成图片（异步接口）
        
        Args:
            source: 图片源（URL、SVG文件路径等）
            selector: CSS选择器（用于网页截图）
            output_path: 输出路径
            **options: 其他选项
            
        Returns:
            str: 生成的图片路径
        """
        logger.info(f"Generating image from source: {source}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(source, options.get('format', 'png'))
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 根据源类型选择生成方法
        source_type = self._detect_source_type(source)
        
        if source_type == 'url':
            image_path = await self._generate_from_url(source, selector, output_path, **options)
        elif source_type == 'svg':
            image_path = await self._generate_from_svg(source, output_path, **options)
        elif source_type == 'image':
            image_path = await self._process_existing_image(source, output_path, **options)
        else:
            raise ValueError(f"Unsupported source type: {source_type}")
        
        # 后处理：尺寸调整和格式转换
        final_path = await self._post_process_image(image_path, output_path, **options)
        
        logger.info(f"Image generated successfully: {final_path}")
        return str(final_path)
    
    def _detect_source_type(self, source: str) -> str:
        """检测源类型"""
        source_lower = source.lower()

        # URL检测（包括file://协议）
        parsed = urlparse(source)
        if parsed.scheme in ('http', 'https', 'file'):
            return 'url'

        # 文件路径检测
        if Path(source).exists():
            if source_lower.endswith('.svg'):
                return 'svg'
            elif source_lower.endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')):
                return 'image'

        # 内容检测
        if source.strip().startswith('<svg'):
            return 'svg'

        raise ValueError(f"Cannot detect source type for: {source}")
    
    async def _generate_from_url(self, url: str, selector: Optional[str],
                               output_path: Path, **options) -> str:
        """从URL生成截图"""
        await self._ensure_browser()
        
        page = await self.browser.new_page()
        
        try:
            # 设置视口和设备缩放
            viewport_size = options.get('viewport_size', {'width': 1920, 'height': 1080})
            device_scale_factor = options.get('device_scale_factor', self.default_device_scale_factor)
            
            await page.set_viewport_size(viewport_size)
            
            # 导航到页面
            timeout = options.get('timeout', 30000)
            await page.goto(url, timeout=timeout, wait_until='networkidle')
            
            # 等待特定元素（如果指定）
            if selector:
                await page.wait_for_selector(selector, timeout=timeout)
            
            # 执行自定义脚本（如果有）
            if 'script' in options:
                await page.evaluate(options['script'])
            
            # 高质量截图设置（PNG不支持quality参数）
            screenshot_options = {
                'path': str(output_path),
                'type': 'png',
                'full_page': options.get('full_page', False),
                'omit_background': False
            }
            
            if selector:
                # 元素截图
                element = await page.locator(selector).first
                await element.screenshot(**screenshot_options)
            else:
                # 页面截图
                await page.screenshot(**screenshot_options)
            
            logger.debug(f"Screenshot saved: {output_path}")
            return str(output_path)
            
        finally:
            await page.close()
    
    async def _generate_from_svg(self, svg_source: str, output_path: Path, **options) -> str:
        """从SVG生成PNG - 使用Playwright"""
        await self._ensure_browser()

        page = await self.browser.new_page()

        try:
            # 读取SVG内容
            if Path(svg_source).exists():
                with open(svg_source, 'r', encoding='utf-8') as f:
                    svg_content = f.read()
            else:
                svg_content = svg_source

            # 创建高质量HTML页面包装SVG
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=3.0">
                <style>
                    * {{
                        box-sizing: border-box;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }}
                    body {{
                        margin: 0;
                        padding: 40px;
                        background: white;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    }}
                    svg {{
                        max-width: 100%;
                        height: auto;
                        display: block;
                        shape-rendering: geometricPrecision;
                        text-rendering: optimizeLegibility;
                    }}
                </style>
            </head>
            <body>
                {svg_content}
            </body>
            </html>
            """

            # 设置页面内容
            await page.set_content(html_content)

            # 等待SVG加载
            await page.wait_for_load_state('networkidle')

            # 高质量截图设置
            device_scale_factor = options.get('device_scale_factor', self.default_device_scale_factor)
            dpi = options.get('dpi', self.default_dpi)

            # 计算高分辨率视口大小
            base_width = options.get('viewport_width', 1200)  # 提升基础分辨率
            base_height = options.get('viewport_height', 800)

            # 根据DPI调整视口大小
            scale_factor = max(1.0, dpi / 150)  # DPI缩放因子
            viewport_width = int(base_width * scale_factor)
            viewport_height = int(base_height * scale_factor)

            await page.set_viewport_size({
                'width': viewport_width,
                'height': viewport_height
            })

            # 高质量截图（PNG不支持quality参数）
            await page.screenshot(
                path=str(output_path),
                type='png',
                full_page=True,
                omit_background=False
            )

            logger.debug(f"SVG converted to PNG using Playwright: {output_path}")
            return str(output_path)

        finally:
            await page.close()
    
    async def _process_existing_image(self, image_path: str, output_path: Path, **options) -> str:
        """处理现有图片"""
        try:
            with Image.open(image_path) as img:
                # 格式转换
                output_format = options.get('format', 'PNG').upper()
                
                # 尺寸调整
                if 'resize' in options:
                    size = options['resize']
                    if isinstance(size, (list, tuple)) and len(size) == 2:
                        img = img.resize(size, Image.Resampling.LANCZOS)
                
                # 保存
                save_options = {}
                if output_format == 'JPEG':
                    save_options['quality'] = options.get('quality', 95)
                    # JPEG不支持透明度，转换为RGB
                    if img.mode in ('RGBA', 'LA'):
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = background
                
                img.save(output_path, format=output_format, **save_options)
                
            logger.debug(f"Image processed: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to process image: {e}")
            raise
    
    async def _post_process_image(self, image_path: str, target_path: Path, **options) -> Path:
        """图片后处理"""
        if image_path == str(target_path):
            return target_path
        
        try:
            with Image.open(image_path) as img:
                # 获取图片信息
                width, height = img.size
                logger.debug(f"Original image size: {width}x{height}")
                
                # DPI设置
                dpi = options.get('dpi', self.default_dpi)
                
                # 计算Word中的显示尺寸（英寸）
                width_inches = width / dpi
                height_inches = height / dpi
                
                # 最大宽度限制（Word页面宽度约6.5英寸）
                max_width_inches = options.get('max_width_inches', 6.5)
                if width_inches > max_width_inches:
                    scale_factor = max_width_inches / width_inches
                    new_width = int(width * scale_factor)
                    new_height = int(height * scale_factor)
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.debug(f"Resized image to: {new_width}x{new_height}")
                
                # 保存最终图片
                img.save(target_path, format='PNG', dpi=(dpi, dpi))
                
            # 清理临时文件
            if image_path != str(target_path) and Path(image_path).exists():
                Path(image_path).unlink()
                
            return target_path
            
        except Exception as e:
            logger.error(f"Failed to post-process image: {e}")
            raise
    
    async def _ensure_browser(self):
        """确保浏览器已启动 - 高质量模式"""
        if not self.browser:
            self.playwright = await async_playwright().start()

            # 高质量浏览器启动参数
            browser_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--force-device-scale-factor=3',  # 强制3倍缩放
                '--high-dpi-support=1',
                '--force-color-profile=srgb'
            ]

            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=browser_args
            )
            logger.debug("High-quality browser launched")

    async def _close_browser(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            self.browser = None
            logger.debug("Browser closed")

        if hasattr(self, 'playwright') and self.playwright:
            await self.playwright.stop()
            self.playwright = None
            logger.debug("Playwright stopped")
    
    def _generate_output_path(self, source: str, format: str = 'png') -> str:
        """生成输出路径"""
        # 从源生成文件名
        if urlparse(source).scheme in ('http', 'https'):
            # URL源
            filename = f"screenshot_{hash(source) & 0x7FFFFFFF}.{format}"
        else:
            # 文件源
            source_path = Path(source)
            filename = f"{source_path.stem}_processed.{format}"
        
        # 使用临时目录
        temp_dir = Path(tempfile.gettempdir()) / 'word_image_insertion'
        temp_dir.mkdir(exist_ok=True)
        
        return str(temp_dir / filename)
    
    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        """获取图片信息"""
        try:
            with Image.open(image_path) as img:
                info = {
                    'size': img.size,
                    'format': img.format,
                    'mode': img.mode,
                    'dpi': img.info.get('dpi', (72, 72)),
                    'file_size': Path(image_path).stat().st_size
                }
                
                # 计算Word中的显示尺寸
                width, height = img.size
                dpi_x, dpi_y = info['dpi']
                info['display_size_inches'] = (width / dpi_x, height / dpi_y)
                
                return info
                
        except Exception as e:
            logger.error(f"Failed to get image info: {e}")
            return {}
    
    def __del__(self):
        """析构函数"""
        # 在Windows上，异步资源清理可能导致警告
        # 这里只做简单的标记，让系统自然清理
        if self.browser:
            self.browser = None
        if self.playwright:
            self.playwright = None
