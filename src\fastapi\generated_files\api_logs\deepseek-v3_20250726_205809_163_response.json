{"timestamp": "2025-07-26 20:59:14", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Sat, 26 Jul 2025 12:59:25 GMT", "content-type": "application/json; charset=utf-8", "content-length": "7704", "x-client-request-id": "unknown-20250726205821-ItOGyFmM", "x-envoy-upstream-service-time": "64688", "x-request-id": "021753534701014dd3a5faac23f0c9eed6bd811669e43cb14fc35"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Here's the professional SVG flowchart based on your requirements:\n\n```svg\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" font-family=\"'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif\">\n  <!-- 定义 -->\n  <defs>\n    <!-- 箭头样式 -->\n    <marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">\n      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#333\" />\n    </marker>\n    <!-- 阴影滤镜 -->\n    <filter id=\"soft-shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.1\" />\n    </filter>\n  </defs>\n\n  <!-- 背景 -->\n  <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n\n  <!-- 主标题 -->\n  <text x=\"600\" y=\"50\" font-size=\"24\" text-anchor=\"middle\" font-weight=\"600\" fill=\"#333\">科技人才数据可视化分析系统</text>\n\n  <!-- 阶段一：数据汇聚 -->\n  <text x=\"600\" y=\"100\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段一：数据汇聚与展示</text>\n  \n  <!-- 节点1: 数据汇聚 -->\n  <g transform=\"translate(500, 130)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">全市科技人才数据汇聚</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">实时刷新概览卡片和分布大屏</text>\n  </g>\n\n  <!-- 阶段二：交互筛选 -->\n  <text x=\"600\" y=\"250\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段二：交互筛选与联动</text>\n\n  <!-- 节点2: 类型切换 -->\n  <g transform=\"translate(350, 300)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才类别选择</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">通过类型切换区筛选</text>\n  </g>\n\n  <!-- 节点3: 数据联动 -->\n  <g transform=\"translate(650, 300)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">全局数据联动</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">刷新概览卡片和分布数据</text>\n  </g>\n\n  <!-- 连接线 1 -> 2/3 (曲线) -->\n  <path d=\"M 550 200 C 450 250, 400 270, 450 300\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 650 200 C 750 250, 800 270, 750 300\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段三：人才详情 -->\n  <text x=\"600\" y=\"420\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段三：人才详情展示</text>\n\n  <!-- 节点4: 人才清册 -->\n  <g transform=\"translate(350, 470)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才清册</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">展示符合条件的人员信息</text>\n  </g>\n\n  <!-- 节点5: 人才详情 -->\n  <g transform=\"translate(650, 470)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才详情</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">科研背景、项目、成果等</text>\n  </g>\n\n  <!-- 连接线 2/3 -> 4/5 (曲线) -->\n  <path d=\"M 450 370 C 400 420, 380 440, 450 470\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 750 370 C 800 420, 820 440, 750 470\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段四：数据操作 -->\n  <text x=\"600\" y=\"590\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段四：数据操作与审计</text>\n\n  <!-- 节点6: 数据操作 -->\n  <g transform=\"translate(500, 640)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据操作</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">精细筛选、导出、记录日志</text>\n  </g>\n\n  <!-- 连接线 4/5 -> 6 (曲线) -->\n  <path d=\"M 450 540 C 500 580, 550 600, 500 640\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 750 540 C 700 580, 650 600, 700 640\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n</svg>\n```\n\nThis SVG flowchart features:\n\n1. Clear four-stage structure with distinct visual grouping\n2. Color-coded nodes following your specified palette:\n   - Stage 1 (Data Aggregation): Light blue\n   - Stage 2 (Interactive Filtering): Light green\n   - Stage 3 (Detail Display): Light purple\n   - Stage 4 (Data Operations): Light yellow\n3. Professional styling with:\n   - Rounded rectangles with soft shadows\n   - Two-level text hierarchy in each node\n   - Smooth Bézier curve connections with arrowheads\n   - Consistent typography with Source Han Sans CN\n4. Logical flow that matches your process description exactly\n5. Ample white space for readability\n\nThe flowchart visually represents the entire workflow from data aggregation to detailed analysis and operations, with clear connections between each step.", "role": "assistant"}}], "created": 1753534765, "id": "021753534701014dd3a5faac23f0c9eed6bd811669e43cb14fc35", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 1884, "prompt_tokens": 2306, "total_tokens": 4190, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}