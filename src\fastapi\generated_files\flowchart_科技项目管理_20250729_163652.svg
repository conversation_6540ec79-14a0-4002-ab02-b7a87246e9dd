<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技项目管理流程</text>

  <!-- 阶段一：业务入口 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：业务入口</text>
  
  <!-- 节点1: 主入口 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目管理模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">查询/录入/导入等业务入口</text>
  </g>

  <!-- 阶段二：核心操作 -->
  <text x="600" y="240" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：核心操作</text>

  <!-- 节点2: 查询操作 -->
  <g transform="translate(200, 270)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选条件检索项目数据</text>
  </g>

  <!-- 节点3: 新增项目 -->
  <g transform="translate(500, 270)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写信息并上传相关资料</text>
  </g>

  <!-- 节点4: 批量导入 -->
  <g transform="translate(800, 270)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">下载模板批量录入项目</text>
  </g>

  <!-- 连接线 主入口 -> 各操作 -->
  <path d="M 600 200 Q 600 220 600 240" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 240 Q 450 250 300 270" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 240 Q 600 250 600 270" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 240 Q 750 250 900 270" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点5: 项目详情 -->
  <g transform="translate(200, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示关键信息及成果附件</text>
  </g>

  <!-- 节点6: 批量操作 -->
  <g transform="translate(500, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">导出/删除/成果关联</text>
  </g>

  <!-- 节点7: 变更记录 -->
  <g transform="translate(800, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时记录历史变更日志</text>
  </g>

  <!-- 连接线 操作 -> 功能 -->
  <path d="M 300 340 Q 300 370 300 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 340 Q 600 370 600 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 340 Q 900 370 900 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：关联与扩展 -->
  <text x="600" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：关联与扩展</text>

  <!-- 节点8: 成果关联 -->
  <g transform="translate(400, 550)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">科技成果关联</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-150">项目与成果灵活关联</tspan>
      <tspan dx="150">支持快速跳转至成果管理</tspan>
    </text>
  </g>

  <!-- 连接线 功能 -> 关联 -->
  <path d="M 300 470 C 300 500, 350 520, 400 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 470 C 600 500, 600 520, 600 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 470 C 900 500, 850 520, 800 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据输出 -->
  <text x="600" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据输出</text>

  <!-- 节点9: 数据导出 -->
  <g transform="translate(500, 710)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成导出文件或更新关联</text>
  </g>

  <!-- 连接线 关联 -> 输出 -->
  <path d="M 600 630 Q 600 660 600 690" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>