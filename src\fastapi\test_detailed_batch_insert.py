#!/usr/bin/env python3
"""
详细测试批量插入功能
"""

import sys
import os
import json
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path.cwd()))

async def test_detailed_batch_insert():
    """详细测试批量插入功能"""
    print("🔍 详细测试批量插入功能...")
    
    try:
        from word_image_insertion import WordImageInserter, Config as WordInsertionConfig
        
        # 创建Word插图器
        config = WordInsertionConfig()
        inserter = WordImageInserter(config)
        
        # 文档路径
        doc_path = "uploads/7159ff6f_投标文件-0613.docx"
        
        # 检查文档是否存在
        if not os.path.exists(doc_path):
            print(f"❌ 文档不存在: {doc_path}")
            return
        
        print(f"✅ 找到文档: {doc_path}")
        
        # 检查生成的文件是否存在
        svg_file = "generated_files/flowchart_科技项目管理_20250729_163652.svg"
        html_file = "generated_files/ui_prototype_科技项目管理_20250729_164127.html"
        
        print(f"🔍 检查SVG文件: {svg_file}")
        if os.path.exists(svg_file):
            print(f"✅ SVG文件存在，大小: {os.path.getsize(svg_file)} 字节")
        else:
            print(f"❌ SVG文件不存在")
        
        print(f"🔍 检查HTML文件: {html_file}")
        if os.path.exists(html_file):
            print(f"✅ HTML文件存在，大小: {os.path.getsize(html_file)} 字节")
        else:
            print(f"❌ HTML文件不存在")
        
        # 构建图像配置
        image_configs = []
        
        # 流程图配置
        if os.path.exists(svg_file):
            image_configs.append({
                'source': svg_file,
                'keyword_pattern': '三、业务流程',
                'options': {'image_width': 5.0, 'format': 'png'}
            })
        
        # UI原型配置
        if os.path.exists(html_file):
            image_configs.append({
                'source': html_file,
                'keyword_pattern': '四、操作流程',
                'options': {'image_width': 5.5, 'format': 'png'}
            })
        
        print(f"📊 配置了 {len(image_configs)} 个图像插入任务")
        
        if not image_configs:
            print("⚠️ 没有可用的图像文件")
            return
        
        # 先测试文档解析
        print("\n🔍 测试文档解析...")
        for config in image_configs:
            print(f"📋 测试关键词: {config['keyword_pattern']}")
            try:
                targets = inserter.parse_document(doc_path, keyword_pattern=config['keyword_pattern'])
                print(f"✅ 找到 {len(targets)} 个插入点")
                for i, target in enumerate(targets):
                    print(f"  - 插入点 {i+1}: 段落 {target.paragraph_index}")
            except Exception as e:
                print(f"❌ 解析失败: {e}")
        
        # 执行批量插入
        print("\n🚀 执行批量插入...")
        result = await inserter.process_batch(
            doc_path,
            image_configs,
            None  # 使用默认输出路径
        )
        
        print("✅ 批量插入完成!")
        print(f"📊 结果统计:")
        print(f"  - 成功: {len(result['success'])}")
        print(f"  - 失败: {len(result['failed'])}")
        print(f"  - 输出文档: {result['output_path']}")
        
        if result['success']:
            print("✅ 成功的任务:")
            for success in result['success']:
                print(f"  - 源文件: {success['config']['source']}")
                print(f"    关键词: {success['config']['keyword_pattern']}")
                print(f"    图像路径: {success['image_path']}")
        
        if result['failed']:
            print("❌ 失败的任务:")
            for failed in result['failed']:
                print(f"  - 源文件: {failed['config']['source']}")
                print(f"    关键词: {failed['config']['keyword_pattern']}")
                print(f"    错误: {failed['error']}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_detailed_batch_insert())
