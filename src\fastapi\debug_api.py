#!/usr/bin/env python3
"""
调试API调用问题
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path.cwd()))

async def test_batch_insert():
    """测试批量插入API逻辑"""
    print("🔍 测试批量插入API逻辑...")
    
    try:
        from word_image_insertion import WordImageInserter, Config as WordInsertionConfig
        
        # 模拟请求数据
        mock_request = {
            "doc_path": "test_document.docx",
            "task_results": [
                {
                    "id": "test_task_1",
                    "type": "flowchart", 
                    "status": "completed"
                },
                {
                    "id": "test_task_2",
                    "type": "ui_prototype",
                    "status": "completed"
                }
            ],
            "config": {
                "business_flow_pattern": "三、业务流程",
                "operation_flow_pattern": "四、操作流程"
            }
        }
        
        print(f"📋 模拟请求数据: {json.dumps(mock_request, indent=2, ensure_ascii=False)}")
        
        # 步骤1: 处理文档路径
        doc_path = mock_request["doc_path"]
        print(f"📄 原始文档路径: {doc_path}")
        
        if not os.path.isabs(doc_path):
            possible_paths = [
                doc_path,
                f"uploads/{doc_path}",
                f"generated_files/{doc_path}",
                f"temp/{doc_path}"
            ]
            
            print("🔍 搜索文档路径:")
            for path in possible_paths:
                exists = os.path.exists(path)
                print(f"  {path}: {'✅ 存在' if exists else '❌ 不存在'}")
                if exists:
                    doc_path = path
                    break
            else:
                print(f"❌ 文档未找到: {mock_request['doc_path']}")
                # 创建一个测试文档
                from word_image_insertion.utils.helpers import create_sample_document
                doc_path = create_sample_document("test_document.docx", include_images=True)
                print(f"✅ 创建测试文档: {doc_path}")
        
        # 步骤2: 创建Word插图器
        print("🔧 创建Word插图器...")
        config = WordInsertionConfig()
        inserter = WordImageInserter(config)
        print("✅ Word插图器创建成功")
        
        # 步骤3: 构建插入配置
        print("📝 构建插入配置...")
        image_configs = []
        
        # 确保generated_files目录存在
        os.makedirs("generated_files", exist_ok=True)
        
        for task_result in mock_request["task_results"]:
            task_id = task_result.get('id', '')
            task_type = task_result.get('type', '')
            status = task_result.get('status', '')
            
            print(f"  处理任务: {task_id} ({task_type}) - {status}")
            
            if status != 'completed':
                print(f"    ⏭️ 跳过未完成任务")
                continue
            
            if task_type == 'flowchart':
                # 查找或创建SVG文件
                svg_files = [f for f in os.listdir('generated_files') if f.startswith(f'flowchart_{task_id}') and f.endswith('.svg')]
                
                if not svg_files:
                    # 创建测试SVG文件
                    from word_image_insertion.utils.helpers import create_sample_svg
                    svg_path = create_sample_svg(f"generated_files/flowchart_{task_id}.svg", "测试流程图")
                    svg_files = [f"flowchart_{task_id}.svg"]
                    print(f"    ✅ 创建测试SVG: {svg_path}")
                
                if svg_files:
                    image_configs.append({
                        'source': f"generated_files/{svg_files[0]}",
                        'keyword_pattern': '三、业务流程',
                        'options': {'image_width': 5.0, 'format': 'png'}
                    })
                    print(f"    ✅ 添加SVG配置: {svg_files[0]}")
            
            elif task_type == 'ui_prototype':
                # 查找或创建HTML文件
                html_files = [f for f in os.listdir('generated_files') if f.startswith(f'ui_prototype_{task_id}') and f.endswith('.html')]
                
                if not html_files:
                    # 创建测试HTML文件
                    from word_image_insertion.utils.helpers import create_sample_html
                    html_path = create_sample_html(f"generated_files/ui_prototype_{task_id}.html", "测试UI原型")
                    html_files = [f"ui_prototype_{task_id}.html"]
                    print(f"    ✅ 创建测试HTML: {html_path}")
                
                if html_files:
                    image_configs.append({
                        'source': f"generated_files/{html_files[0]}",
                        'keyword_pattern': '四、操作流程',
                        'options': {'image_width': 5.5, 'format': 'png'}
                    })
                    print(f"    ✅ 添加HTML配置: {html_files[0]}")
        
        print(f"📊 总共配置了 {len(image_configs)} 个图像插入任务")
        
        if not image_configs:
            print("⚠️ 没有图像需要插入")
            return {
                "success": True,
                "message": "No images to insert",
                "output_path": doc_path,
                "insertions_count": 0
            }
        
        # 步骤4: 执行批量插入
        print("🚀 执行批量插入...")
        result = await inserter.process_batch(
            doc_path,
            image_configs,
            None  # 使用默认输出路径
        )
        
        print("✅ 批量插入完成!")
        print(f"📊 结果统计:")
        print(f"  - 成功: {len(result['success'])}")
        print(f"  - 失败: {len(result['failed'])}")
        print(f"  - 输出文档: {result['output_path']}")
        
        if result['failed']:
            print("❌ 失败的任务:")
            for failed in result['failed']:
                print(f"  - {failed}")
        
        # 处理结果，确保JSON可序列化
        success_details = []
        for item in result['success']:
            success_details.append({
                'source': item['config']['source'],
                'keyword_pattern': item['config']['keyword_pattern'],
                'image_path': item['image_path']
            })

        failed_details = []
        for item in result['failed']:
            failed_details.append({
                'source': item['config']['source'] if 'config' in item else 'unknown',
                'error': item['error']
            })

        return {
            "success": True,
            "output_path": result['output_path'],
            "insertions_count": len(result['success']),
            "failed_count": len(result['failed']),
            "success_details": success_details,
            "failed_details": failed_details
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

def main():
    """主函数"""
    print("🧪 调试Word图像插入API")
    print("=" * 50)
    
    result = asyncio.run(test_batch_insert())
    
    print("\n" + "=" * 50)
    print("📋 最终结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
