"""
配置管理模块

管理项目的各种配置参数，支持从文件、环境变量等加载配置。
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class ImageConfig:
    """图片生成配置"""
    default_dpi: int = 300
    max_width_inches: float = 6.5
    device_scale_factor: int = 2
    default_format: str = 'png'
    quality: int = 95
    timeout: int = 30000


@dataclass
class DocumentConfig:
    """文档处理配置"""
    cache_enabled: bool = True
    max_cache_size: int = 100
    default_heading_pattern: str = 'Heading 5'
    context_lines: int = 2


@dataclass
class PreviewConfig:
    """预览配置"""
    auto_open: bool = False
    cleanup_hours: int = 24
    pdf_export_options: str = 'SelectPdfVersion=1;ExportBookmarks=1'


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_enabled: bool = False
    file_path: str = 'word_image_insertion.log'


class Config:
    """主配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
        """
        # 默认配置
        self.image = ImageConfig()
        self.document = DocumentConfig()
        self.preview = PreviewConfig()
        self.logging = LoggingConfig()
        
        # 项目根目录
        self.project_root = Path(__file__).parent.parent
        
        # 临时目录
        self.temp_dir = Path.home() / '.word_image_insertion' / 'temp'
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存目录
        self.cache_dir = Path.home() / '.word_image_insertion' / 'cache'
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置文件
        if config_file:
            self.load_from_file(config_file)
        else:
            # 尝试加载默认配置文件
            default_config = self.project_root / 'config.json'
            if default_config.exists():
                self.load_from_file(str(default_config))
        
        # 从环境变量加载
        self.load_from_env()
        
        # 设置日志级别
        self.log_level = getattr(logging, self.logging.level.upper(), logging.INFO)
    
    def load_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                logger.warning(f"Config file not found: {config_file}")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            if 'image' in config_data:
                self._update_dataclass(self.image, config_data['image'])
            
            if 'document' in config_data:
                self._update_dataclass(self.document, config_data['document'])
            
            if 'preview' in config_data:
                self._update_dataclass(self.preview, config_data['preview'])
            
            if 'logging' in config_data:
                self._update_dataclass(self.logging, config_data['logging'])
            
            logger.info(f"Configuration loaded from: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to load config from file: {e}")
    
    def load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            'WII_IMAGE_DPI': ('image', 'default_dpi', int),
            'WII_IMAGE_MAX_WIDTH': ('image', 'max_width_inches', float),
            'WII_IMAGE_SCALE_FACTOR': ('image', 'device_scale_factor', int),
            'WII_IMAGE_FORMAT': ('image', 'default_format', str),
            'WII_IMAGE_QUALITY': ('image', 'quality', int),
            'WII_IMAGE_TIMEOUT': ('image', 'timeout', int),
            
            'WII_DOC_CACHE_ENABLED': ('document', 'cache_enabled', lambda x: x.lower() == 'true'),
            'WII_DOC_MAX_CACHE_SIZE': ('document', 'max_cache_size', int),
            'WII_DOC_HEADING_PATTERN': ('document', 'default_heading_pattern', str),
            'WII_DOC_CONTEXT_LINES': ('document', 'context_lines', int),
            
            'WII_PREVIEW_AUTO_OPEN': ('preview', 'auto_open', lambda x: x.lower() == 'true'),
            'WII_PREVIEW_CLEANUP_HOURS': ('preview', 'cleanup_hours', int),
            
            'WII_LOG_LEVEL': ('logging', 'level', str),
            'WII_LOG_FILE_ENABLED': ('logging', 'file_enabled', lambda x: x.lower() == 'true'),
            'WII_LOG_FILE_PATH': ('logging', 'file_path', str),
        }
        
        for env_var, (section, key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    converted_value = converter(value)
                    config_obj = getattr(self, section)
                    setattr(config_obj, key, converted_value)
                    logger.debug(f"Set {section}.{key} = {converted_value} from {env_var}")
                except Exception as e:
                    logger.warning(f"Failed to convert env var {env_var}={value}: {e}")
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            config_data = {
                'image': asdict(self.image),
                'document': asdict(self.document),
                'preview': asdict(self.preview),
                'logging': asdict(self.logging)
            }
            
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to save config to file: {e}")
    
    def _update_dataclass(self, obj, data: Dict[str, Any]):
        """更新dataclass对象"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
            else:
                logger.warning(f"Unknown config key: {key}")
    
    def get_playwright_options(self) -> Dict[str, Any]:
        """获取Playwright选项"""
        return {
            'headless': True,
            'args': ['--no-sandbox', '--disable-dev-shm-usage'],
            'device_scale_factor': self.image.device_scale_factor,
            'timeout': self.image.timeout
        }
    
    def get_image_options(self) -> Dict[str, Any]:
        """获取图片生成选项"""
        return {
            'dpi': self.image.default_dpi,
            'max_width_inches': self.image.max_width_inches,
            'format': self.image.default_format,
            'quality': self.image.quality
        }
    
    def get_document_options(self) -> Dict[str, Any]:
        """获取文档处理选项"""
        return {
            'cache_enabled': self.document.cache_enabled,
            'max_cache_size': self.document.max_cache_size,
            'heading_pattern': self.document.default_heading_pattern,
            'context_lines': self.document.context_lines
        }
    
    def get_preview_options(self) -> Dict[str, Any]:
        """获取预览选项"""
        return {
            'auto_open': self.preview.auto_open,
            'cleanup_hours': self.preview.cleanup_hours,
            'pdf_export_options': self.preview.pdf_export_options
        }
    
    def create_default_config_file(self, config_file: str):
        """创建默认配置文件"""
        self.save_to_file(config_file)
        logger.info(f"Default configuration file created: {config_file}")
    
    def validate(self) -> bool:
        """验证配置"""
        errors = []
        
        # 验证图片配置
        if self.image.default_dpi <= 0:
            errors.append("Image DPI must be positive")
        
        if self.image.max_width_inches <= 0:
            errors.append("Max width must be positive")
        
        if self.image.device_scale_factor <= 0:
            errors.append("Device scale factor must be positive")
        
        if self.image.quality < 1 or self.image.quality > 100:
            errors.append("Image quality must be between 1 and 100")
        
        # 验证文档配置
        if self.document.max_cache_size <= 0:
            errors.append("Max cache size must be positive")
        
        if self.document.context_lines < 0:
            errors.append("Context lines must be non-negative")
        
        # 验证预览配置
        if self.preview.cleanup_hours <= 0:
            errors.append("Cleanup hours must be positive")
        
        # 验证日志配置
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.logging.level.upper() not in valid_log_levels:
            errors.append(f"Log level must be one of: {valid_log_levels}")
        
        if errors:
            for error in errors:
                logger.error(f"Configuration error: {error}")
            return False
        
        return True
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(image={self.image}, document={self.document}, preview={self.preview}, logging={self.logging})"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'image': asdict(self.image),
            'document': asdict(self.document),
            'preview': asdict(self.preview),
            'logging': asdict(self.logging),
            'project_root': str(self.project_root),
            'temp_dir': str(self.temp_dir),
            'cache_dir': str(self.cache_dir)
        }
