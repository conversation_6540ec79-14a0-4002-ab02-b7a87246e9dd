#!/usr/bin/env python3
"""
Word图像插入功能集成验证脚本

验证所有引用是否正确，确保没有对word_image_insertion_package的引用
"""

import os
import sys
import importlib
from pathlib import Path

def check_imports():
    """检查所有导入是否正确"""
    print("🔍 检查模块导入...")
    
    try:
        # 检查核心模块
        from word_image_insertion import WordImageInserter, Config
        print("✅ 核心模块导入成功")
        
        # 检查子模块
        from word_image_insertion.core.document_parser import DocumentParser
        from word_image_insertion.core.image_generator import ImageGenerator
        from word_image_insertion.core.word_inserter import WordInserter
        from word_image_insertion.core.preview_manager import PreviewManager
        from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager
        print("✅ 核心子模块导入成功")
        
        # 检查工具模块
        from word_image_insertion.utils.config import Config
        from word_image_insertion.utils.helpers import setup_logging, validate_dependencies
        print("✅ 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def check_file_references():
    """检查文件中是否有对word_image_insertion_package的引用"""
    print("\n🔍 检查文件引用...")
    
    files_to_check = [
        "app/main.py",
        "test_word_insertion.py",
        "setup_word_insertion.py",
        "WORD_INSERTION_README.md"
    ]
    
    bad_references = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'word_image_insertion_package' in content:
                    bad_references.append(file_path)
                    print(f"❌ 发现错误引用: {file_path}")
                else:
                    print(f"✅ 引用正确: {file_path}")
                    
            except Exception as e:
                print(f"⚠️ 无法检查文件 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    return len(bad_references) == 0

def check_api_endpoints():
    """检查API端点是否正确配置"""
    print("\n🔍 检查API端点...")
    
    try:
        # 检查main.py中的API端点
        with open("app/main.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_endpoints = [
            "/api/word/insert-image",
            "/api/word/batch-insert", 
            "/api/word/preview"
        ]
        
        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing_endpoints.append(endpoint)
            else:
                print(f"✅ API端点存在: {endpoint}")
        
        if missing_endpoints:
            print(f"❌ 缺少API端点: {missing_endpoints}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查API端点失败: {e}")
        return False

def check_frontend_integration():
    """检查前端集成"""
    print("\n🔍 检查前端集成...")
    
    frontend_file = "../renderer/src/pages/tenderdocumentchat/components/StepDocumentExport.tsx"
    
    if not os.path.exists(frontend_file):
        print(f"⚠️ 前端文件不存在: {frontend_file}")
        return False
    
    try:
        with open(frontend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_features = [
            "exportToWordDocument",
            "isWordExporting",
            "wordExportProgress",
            "/api/word/batch-insert",
            "FileWordOutlined"
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)
            else:
                print(f"✅ 前端功能存在: {feature}")
        
        if missing_features:
            print(f"❌ 缺少前端功能: {missing_features}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查前端集成失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        'playwright',
        'PIL',  # Pillow
        'docx',  # python-docx
        'fastapi',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ 依赖包存在: {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ 缺少依赖包: {package}")
    
    if missing_packages:
        print(f"\n📋 安装缺少的依赖包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        "word_image_insertion/__init__.py",
        "word_image_insertion/core/__init__.py",
        "word_image_insertion/core/document_parser.py",
        "word_image_insertion/core/image_generator.py",
        "word_image_insertion/core/word_inserter.py",
        "word_image_insertion/core/preview_manager.py",
        "word_image_insertion/core/enhanced_preview.py",
        "word_image_insertion/utils/__init__.py",
        "word_image_insertion/utils/config.py",
        "word_image_insertion/utils/helpers.py",
        "requirements.txt",
        "word_insertion_config.json"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ 文件缺失: {file_path}")
    
    return len(missing_files) == 0

def main():
    """主函数"""
    print("🔍 Word图像插入功能集成验证")
    print("=" * 50)
    
    checks = [
        ("模块导入", check_imports),
        ("文件引用", check_file_references),
        ("API端点", check_api_endpoints),
        ("前端集成", check_frontend_integration),
        ("依赖包", check_dependencies),
        ("文件结构", check_file_structure)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 验证结果总结:")
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有检查通过！Word图像插入功能集成完整。")
        print("\n📋 下一步:")
        print("1. 运行: python -m uvicorn app.main:app --reload")
        print("2. 测试前端Word导出功能")
        print("3. 验证完整工作流程")
        return 0
    else:
        print("❌ 部分检查失败，请根据上述信息修复问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
