=== API调用成功 ===
时间: 2025-07-29 16:15:21
模型: deepseek-v3
内容长度: 50245 字符

=== 生成内容 ===
根据需求分析，这是一个典型的"管理后台"类功能，我将按照管理后台的设计心法和代码范例来构建科技项目管理页面。以下是完整的HTML代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- SLOT::header::BEGIN -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">科技项目管理</h1>
            <p class="text-gray-600">对国家级、省级、市级、区级科技项目进行全生命周期管理</p>
        </div>
        <!-- SLOT::header::END -->

        <!-- SLOT::filters::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- CHUNK::filter-project-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                </div>
                <!-- CHUNK::filter-project-name::END -->
                
                <!-- CHUNK::filter-org-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">承担单位</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入单位名称">
                </div>
                <!-- CHUNK::filter-org-name::END -->
                
                <!-- CHUNK::filter-credit-code::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">统一信用代码</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                </div>
                <!-- CHUNK::filter-credit-code::END -->
                
                <!-- CHUNK::filter-project-level::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目级别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="district">区级</option>
                    </select>
                </div>
                <!-- CHUNK::filter-project-level::END -->
                
                <!-- CHUNK::filter-project-status::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="declared">已申报</option>
                        <option value="approved">已立项</option>
                        <option value="in-progress">执行中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                <!-- CHUNK::filter-project-status::END -->
                
                <!-- CHUNK::filter-year-range::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">立项年度</label>
                    <div class="flex space-x-2">
                        <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="开始年">
                        <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="结束年">
                    </div>
                </div>
                <!-- CHUNK::filter-year-range::END -->
            </div>
            
            <!-- CHUNK::filter-buttons::BEGIN -->
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
            <!-- CHUNK::filter-buttons::END -->
        </div>
        <!-- SLOT::filters::END -->

        <!-- SLOT::main-content::BEGIN -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">项目列表</h2>
                    <div class="flex space-x-3">
                        <!-- CHUNK::batch-export-button::BEGIN -->
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            批量导出
                        </button>
                        <!-- CHUNK::batch-export-button::END -->
                        
                        <!-- CHUNK::import-button::BEGIN -->
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            批量导入
                        </button>
                        <!-- CHUNK::import-button::END -->
                        
                        <!-- CHUNK::add-project-button::BEGIN -->
                        <button onclick="openModal('add-project-modal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            新增项目
                        </button>
                        <!-- CHUNK::add-project-button::END -->
                    </div>
                </div>
            </div>
            
            <!-- CHUNK::project-table::BEGIN -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目级别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::project-table-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研发</div>
                                <div class="text-sm text-gray-500">宁波市科技计划项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB2023A001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15 至 2024-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">执行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('project-detail-modal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('edit-project-modal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::project-table-row-1::END -->
                        
                        <!-- CHUNK::project-table-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">浙江省重点研发计划项目</div>
                                <div class="text-sm text-gray-500">新材料领域</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ZJ2022B002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-06-01 至 2024-05-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 text-blue-800 bg-blue-100 rounded-full">已立项</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('project-detail-modal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('edit-project-modal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::project-table-row-2::END -->
                        
                        <!-- CHUNK::project-table-row-3::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">国家自然科学基金项目</div>
                                <div class="text-sm text-gray-500">信息科学部</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NSFC2021C003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波工程学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-09-01 至 2023-08-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 text-yellow-800 bg-yellow-100 rounded-full">验收中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('project-detail-modal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('edit-project-modal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::project-table-row-3::END -->
                    </tbody>
                </table>
            </div>
            <!-- CHUNK::project-table::END -->
            
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::main-content::END -->
    </div>

    <!-- SLOT::add-project-modal::BEGIN -->
    <div id="add-project-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增科技项目</h3>
                    <button onclick="closeModal('add-project-modal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- CHUNK::modal-project-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称" required>
                        </div>
                        <!-- CHUNK::modal-project-name::END -->
                        
                        <!-- CHUNK::modal-project-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="系统自动生成">
                        </div>
                        <!-- CHUNK::modal-project-code::END -->
                        
                        <!-- CHUNK::modal-org-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">承担单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入承担单位" required>
                        </div>
                        <!-- CHUNK::modal-org-name::END -->
                        
                        <!-- CHUNK::modal-credit-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一信用代码</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                        </div>
                        <!-- CHUNK::modal-credit-code::END -->
                        
                        <!-- CHUNK::modal-project-level::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目级别 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择项目级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <!-- CHUNK::modal-project-level::END -->
                        
                        <!-- CHUNK::modal-project-type::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择项目类型</option>
                                <option value="rd">研发项目</option>
                                <option value="transformation">成果转化</option>
                                <option value="platform">平台建设</option>
                                <option value="talent">人才项目</option>
                            </select>
                        </div>
                        <!-- CHUNK::modal-project-type::END -->
                        
                        <!-- CHUNK::modal-start-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-start-date::END -->
                        
                        <!-- CHUNK::modal-end-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-end-date::END -->
                        
                        <!-- CHUNK::modal-budget::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目预算(万元)</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目预算">
                        </div>
                        <!-- CHUNK::modal-budget::END -->
                        
                        <!-- CHUNK::modal-leader::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目负责人 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入负责人姓名" required>
                        </div>
                        <!-- CHUNK::modal-leader::END -->
                    </div>
                    
                    <!-- CHUNK::modal-description::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目简介"></textarea>
                    </div>
                    <!-- CHUNK::modal-description::END -->
                    
                    <!-- CHUNK::modal-attachments::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel文件，最大10MB</p>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::modal-attachments::END -->
                    
                    <!-- CHUNK::modal-buttons::BEGIN -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('add-project-modal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                    <!-- CHUNK::modal-buttons::END -->
                </form>
            </div>
        </div>
    </div>
    <!-- SLOT::add-project-modal::END -->

    <!-- SLOT::project-detail-modal::BEGIN -->
    <div id="project-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeModal('project-detail-modal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-6">
                    <!-- CHUNK::detail-basic-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">项目名称：</span>
                                <span class="font-medium text-gray-900">宁波市智能制造关键技术研发</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目编号：</span>
                                <span class="font-medium text-gray-900">NB2023A001</span>
                            </div>
                            <div>
                                <span class="text-gray-500">承担单位：</span>
                                <span class="font-medium text-gray-900">宁波市XX科技有限公司</span>
                            </div>
                            <div>
                                <span class="text-gray-500">统一信用代码：</span>
                                <span class="font-medium text-gray-900">91330201MA2XXXXXX</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目级别：</span>
                                <span class="font-medium text-gray-900">市级</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目类型：</span>
                                <span class="font-medium text-gray-900">研发项目</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目负责人：</span>
                                <span class="font-medium text-gray-900">张研究员</span>
                            </div>
                            <div>
                                <span class="text-gray-500">联系电话：</span>
                                <span class="font-medium text-gray-900">138XXXX8888</span>
                            </div>
                            <div>
                                <span class="text-gray-500">开始日期：</span>
                                <span class="font-medium text-gray-900">2023-01-15</span>
                            </div>
                            <div>
                                <span class="text-gray-500">结束日期：</span>
                                <span class="font-medium text-gray-900">2024-12-31</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目预算：</span>
                                <span class="font-medium text-gray-900">120.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目状态：</span>
                                <span class="font-medium text-gray-900">执行中</span>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-basic-info::END -->
                    
                    <!-- CHUNK::detail-team-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目团队</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分工</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张研究员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">138XXXX8888</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术指导</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">139XXXX9999</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- CHUNK::detail-team-info::END -->
                    
                    <!-- CHUNK::detail-funding-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">经费信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">项目总预算：</span>
                                <span class="font-medium text-gray-900">120.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">财政拨款：</span>
                                <span class="font-medium text-gray-900">50.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">单位自筹：</span>
                                <span class="font-medium text-gray-900">70.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">已拨付金额：</span>
                                <span class="font-medium text-gray-900">30.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">经费使用率：</span>
                                <span class="font-medium text-gray-900">25%</span>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-funding-info::END -->
                    
                    <!-- CHUNK::detail-attachments::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">相关附件</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">项目申报书.pdf</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">立项通知书.docx</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-attachments::END -->
                    
                    <!-- CHUNK::detail-buttons::BEGIN -->
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeModal('project-detail-modal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            关闭
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            导出报告
                        </button>
                    </div>
                    <!-- CHUNK::detail-buttons::END -->
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::project-detail-modal::END -->

    <!-- SLOT::edit-project-modal::BEGIN -->
    <div id="edit-project-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑科技项目</h3>
                    <button onclick="closeModal('edit-project-modal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- CHUNK::edit-project-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市智能制造关键技术研发" required>
                        </div>
                        <!-- CHUNK::edit-project-name::END -->
                        
                        <!-- CHUNK::edit-project-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="NB2023A001" readonly>
                        </div>
                        <!-- CHUNK::edit-project-code::END -->
                        
                        <!-- CHUNK::edit-org-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">承担单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市XX科技有限公司" required>
                        </div>
                        <!-- CHUNK::edit-org-name::END -->
                        
                        <!-- CHUNK::edit-credit-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一信用代码</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="91330201MA2XXXXXX">
                        </div>
                        <!-- CHUNK::edit-credit-code::END -->
                        
                        <!-- CHUNK::edit-project-level::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目级别 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="municipal" selected>市级</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <!-- CHUNK::edit-project-level::END -->
                        
                        <!-- CHUNK::edit-project-type::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="rd" selected>研发项目</option>
                                <option value="transformation">成果转化</option>
                                <option value="platform">平台建设</option>
                                <option value="talent">人才项目</option>
                            </select>
                        </div>
                        <!-- CHUNK::edit-project-type::END -->
                        
                        <!-- CHUNK::edit-start-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2023-01-15" required>
                        </div>
                        <!-- CHUNK::edit-start-date::END -->
                        
                        <!-- CHUNK::edit-end-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2024-12-31" required>
                        </div>
                        <!-- CHUNK::edit-end-date::END -->
                        
                        <!-- CHUNK::edit-budget::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目预算(万元)</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="120">
                        </div>
                        <!-- CHUNK::edit-budget::END -->
                        
                        <!-- CHUNK::edit-leader::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目负责人 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张研究员" required>
                        </div>
                        <!-- CHUNK::edit-leader::END -->
                        
                        <!-- CHUNK::edit-status::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目状态 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="in-progress" selected>执行中</option>
                                <option value="declared">已申报</option>
                                <option value="approved">已立项</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                        <!-- CHUNK::edit-status::END -->
                    </div>
                    
                    <!-- CHUNK::edit-description::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">本项目旨在研发智能制造领域的关键技术，提升宁波市制造业的智能化水平...</textarea>
                    </div>
                    <!-- CHUNK::edit-description::END -->
                    
                    <!-- CHUNK::edit-buttons::BEGIN -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('edit-project-modal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                    <!-- CHUNK::edit-buttons::END -->
                </form>
            </div>
        </div>
    </div>
    <!-- SLOT::edit-project-modal::END -->

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // ========= 为所有弹窗绑定"点击外部关闭"事件 =========
            const modals = document.querySelectorAll('[id$="-modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 绑定ESC键关闭
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // ========= 表单提交处理（用于原型） =========
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const modalId = form.closest('[id$="-modal"]')?.id;
                    if (modalId) {
                        alert('表单已提交 (原型演示)');
                        closeModal(modalId);
                    }
                });
            });
            
            // ========= 文件上传处理 =========
            document.getElementById('file-upload')?.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    alert('文件已选择: ' + e.target.files[0].name);
                }
            });
        });
    </script>
</body>
</html>
<!-- END -->
```