#!/usr/bin/env python3
"""
一键运行所有测试
"""

import asyncio
import subprocess
import sys
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {command}")
    print("-" * 60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=False,
            text=True,
            cwd=Path.cwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_libreoffice():
    """检查LibreOffice是否安装"""
    print("\n🔍 检查LibreOffice安装状态...")
    
    try:
        result = subprocess.run(
            ["soffice", "--version"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print(f"✅ LibreOffice已安装: {result.stdout.strip()}")
            return True
    except:
        pass
    
    # 检查Windows路径
    windows_paths = [
        r"C:\Program Files\LibreOffice\program\soffice.exe",
        r"C:\Program Files (x86)\LibreOffice\program\soffice.exe"
    ]
    
    for path in windows_paths:
        if Path(path).exists():
            print(f"✅ LibreOffice已安装: {path}")
            return True
    
    print("❌ LibreOffice未安装")
    print("📋 安装指南:")
    print("  1. 访问: https://www.libreoffice.org/download/")
    print("  2. 下载Windows版本")
    print("  3. 安装后重新运行此测试")
    return False

def main():
    """主函数"""
    
    print("🎭 Word精准插图 - 完整测试套件")
    print("=" * 60)
    print("测试内容:")
    print("  1. 完整工作流测试 (SVG + HTML → Word)")
    print("  2. 预览功能测试 (Word → PDF)")
    print("  3. 文档结构验证")
    print("=" * 60)
    
    # 检查必要文件
    files_dir = Path("word_image_insertion/files")

    print("\n📋 检查必要文件...")

    # 查找Word文档
    word_files = list(files_dir.glob("*.docx"))
    svg_files = list(files_dir.glob("*.svg"))
    html_files = list(files_dir.glob("*.html"))

    print(f"  📄 Word文档: {len(word_files)} 个")
    for f in word_files:
        print(f"    ✅ {f}")

    print(f"  🎨 SVG文件: {len(svg_files)} 个")
    for f in svg_files:
        print(f"    ✅ {f}")

    print(f"  🌐 HTML文件: {len(html_files)} 个")
    for f in html_files:
        print(f"    ✅ {f}")

    if not word_files:
        print(f"\n❌ 未找到Word文档文件 (*.docx)")
        print(f"请将Word文档放入 {files_dir} 目录")
        return False

    if not svg_files and not html_files:
        print(f"\n❌ 未找到SVG或HTML文件")
        print(f"请将SVG或HTML文件放入 {files_dir} 目录")
        return False
    
    # 测试1: 完整工作流
    success1 = run_command(
        "python test_complete_workflow.py",
        "完整工作流测试 (SVG + HTML → Word)"
    )
    
    if not success1:
        print("\n❌ 完整工作流测试失败，停止后续测试")
        return False
    
    # 检查生成的文档
    final_doc = Path("complete_test_output/final_complete_document.docx")
    if final_doc.exists():
        print(f"\n✅ 最终文档已生成: {final_doc}")
        
        # 打开文档供用户查看
        try:
            import webbrowser
            webbrowser.open(f"file:///{final_doc.absolute().as_posix()}")
            print("📖 文档已在默认程序中打开")
        except:
            print("⚠️  无法自动打开文档，请手动查看")
    else:
        print(f"\n❌ 最终文档未生成")
        return False
    
    # 测试2: 预览功能
    print(f"\n" + "="*60)
    print("🔍 准备测试预览功能...")
    
    libreoffice_available = check_libreoffice()
    
    if libreoffice_available:
        success2 = run_command(
            "python test_preview_function.py",
            "预览功能测试 (Word → PDF)"
        )
        
        if success2:
            print("\n✅ 预览功能测试成功")
        else:
            print("\n⚠️  预览功能测试失败，但不影响核心功能")
    else:
        print("\n⚠️  跳过预览功能测试（LibreOffice未安装）")
        success2 = True  # 不影响整体测试结果
    
    # 总结
    print(f"\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    print(f"  完整工作流测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  预览功能测试: {'✅ 成功' if success2 else '⚠️  跳过/失败'}")
    
    if success1:
        print(f"\n🎉 核心功能测试通过！")
        print(f"📁 生成的文件:")
        print(f"  📄 最终Word文档: complete_test_output/final_complete_document.docx")
        print(f"  🎨 业务流程图: complete_test_output/business_flow_from_svg.png")
        print(f"  📸 操作界面图: complete_test_output/operation_ui_from_html.png")
        
        if libreoffice_available and success2:
            print(f"  📋 PDF预览: 已生成（查看浏览器）")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 打开Word文档查看图片插入效果")
        print(f"  2. 验证图片是否在正确的章节位置")
        print(f"  3. 检查图片质量和大小")
        
        if not libreoffice_available:
            print(f"  4. 安装LibreOffice以启用PDF预览功能")
        
        return True
    else:
        print(f"\n❌ 测试失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
