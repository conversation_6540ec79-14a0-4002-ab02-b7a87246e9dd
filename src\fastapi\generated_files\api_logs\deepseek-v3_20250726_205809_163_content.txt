=== API调用成功 ===
时间: 2025-07-26 20:59:14
模型: deepseek-v3
内容长度: 5352 字符

=== 生成内容 ===
Here's the professional SVG flowchart based on your requirements:

```svg
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="24" text-anchor="middle" font-weight="600" fill="#333">科技人才数据可视化分析系统</text>

  <!-- 阶段一：数据汇聚 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与展示</text>
  
  <!-- 节点1: 数据汇聚 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">全市科技人才数据汇聚</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时刷新概览卡片和分布大屏</text>
  </g>

  <!-- 阶段二：交互筛选 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：交互筛选与联动</text>

  <!-- 节点2: 类型切换 -->
  <g transform="translate(350, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才类别选择</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">通过类型切换区筛选</text>
  </g>

  <!-- 节点3: 数据联动 -->
  <g transform="translate(650, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">全局数据联动</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">刷新概览卡片和分布数据</text>
  </g>

  <!-- 连接线 1 -> 2/3 (曲线) -->
  <path d="M 550 200 C 450 250, 400 270, 450 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 200 C 750 250, 800 270, 750 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：人才详情 -->
  <text x="600" y="420" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：人才详情展示</text>

  <!-- 节点4: 人才清册 -->
  <g transform="translate(350, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才清册</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示符合条件的人员信息</text>
  </g>

  <!-- 节点5: 人才详情 -->
  <g transform="translate(650, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">科研背景、项目、成果等</text>
  </g>

  <!-- 连接线 2/3 -> 4/5 (曲线) -->
  <path d="M 450 370 C 400 420, 380 440, 450 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 370 C 800 420, 820 440, 750 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据操作 -->
  <text x="600" y="590" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据操作与审计</text>

  <!-- 节点6: 数据操作 -->
  <g transform="translate(500, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">精细筛选、导出、记录日志</text>
  </g>

  <!-- 连接线 4/5 -> 6 (曲线) -->
  <path d="M 450 540 C 500 580, 550 600, 500 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 540 C 700 580, 650 600, 700 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
</svg>
```

This SVG flowchart features:

1. Clear four-stage structure with distinct visual grouping
2. Color-coded nodes following your specified palette:
   - Stage 1 (Data Aggregation): Light blue
   - Stage 2 (Interactive Filtering): Light green
   - Stage 3 (Detail Display): Light purple
   - Stage 4 (Data Operations): Light yellow
3. Professional styling with:
   - Rounded rectangles with soft shadows
   - Two-level text hierarchy in each node
   - Smooth Bézier curve connections with arrowheads
   - Consistent typography with Source Han Sans CN
4. Logical flow that matches your process description exactly
5. Ample white space for readability

The flowchart visually represents the entire workflow from data aggregation to detailed analysis and operations, with clear connections between each step.