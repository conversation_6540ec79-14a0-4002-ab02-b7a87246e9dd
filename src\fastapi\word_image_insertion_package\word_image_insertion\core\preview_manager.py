"""
格式回写与预览模块

集成LibreOffice转换和PDF预览功能，实现文档格式回写和即时预览。
核心功能：LibreOffice headless转PDF，浏览器预览，支持书签和跳转。
"""

import logging
import os
import subprocess
import tempfile
import webbrowser
from pathlib import Path
from typing import Optional, Dict, Any, List
import shutil
import json

logger = logging.getLogger(__name__)


class PreviewManager:
    """预览管理器"""
    
    def __init__(self, config=None):
        self.config = config
        self.libreoffice_path = self._find_libreoffice()
        self.temp_dir = Path(tempfile.gettempdir()) / 'word_image_insertion_preview'
        self.temp_dir.mkdir(exist_ok=True)
        
    def generate_preview(self, doc_path: str, output_format: str = 'pdf') -> str:
        """
        生成文档预览
        
        Args:
            doc_path: 文档路径
            output_format: 输出格式（pdf/html）
            
        Returns:
            str: 预览文件路径
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Generating {output_format.upper()} preview for: {doc_path}")
        
        if output_format.lower() == 'pdf':
            return self._convert_to_pdf(doc_path)
        elif output_format.lower() == 'html':
            return self._convert_to_html(doc_path)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def _convert_to_pdf(self, doc_path: Path) -> str:
        """转换为PDF"""
        if not self.libreoffice_path:
            raise RuntimeError("LibreOffice not found. Please install LibreOffice.")
        
        output_dir = self.temp_dir / 'pdf'
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / f"{doc_path.stem}.pdf"
        
        try:
            # LibreOffice命令行转换 - 增强版参数
            cmd = [
                str(self.libreoffice_path),
                '--headless',           # 无头模式
                '--invisible',          # 不可见模式
                '--nodefault',          # 不加载默认文档
                '--nolockcheck',        # 不检查文件锁
                '--nologo',             # 不显示启动画面
                '--norestore',          # 不恢复前次会话
                '--convert-to',
                'pdf:writer_pdf_Export:SelectPdfVersion=1;ExportBookmarks=1',
                '--outdir',
                str(output_dir),
                str(doc_path)
            ]

            logger.debug(f"Running LibreOffice command: {' '.join(cmd[:3])} ... {doc_path.name}")

            # Windows系统使用特殊标志避免弹窗
            creation_flags = 0
            if os.name == 'nt':
                creation_flags = subprocess.CREATE_NO_WINDOW

            # 使用DEVNULL避免控制台输出
            with open(os.devnull, 'w') as devnull:
                result = subprocess.run(
                    cmd,
                    stdout=devnull,
                    stderr=devnull,
                    timeout=120,  # 增加超时时间
                    creationflags=creation_flags
                )
            
            if result.returncode != 0:
                logger.error(f"LibreOffice conversion failed: {result.stderr}")
                raise RuntimeError(f"PDF conversion failed: {result.stderr}")
            
            if not output_path.exists():
                raise RuntimeError("PDF file was not created")
            
            logger.info(f"PDF generated successfully: {output_path}")
            return str(output_path)
            
        except subprocess.TimeoutExpired:
            logger.error("LibreOffice conversion timed out")
            raise RuntimeError("PDF conversion timed out")
        except Exception as e:
            logger.error(f"Failed to convert to PDF: {e}")
            raise
    
    def _convert_to_html(self, doc_path: Path) -> str:
        """转换为HTML"""
        if not self.libreoffice_path:
            raise RuntimeError("LibreOffice not found. Please install LibreOffice.")
        
        output_dir = self.temp_dir / 'html'
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / f"{doc_path.stem}.html"
        
        try:
            # LibreOffice命令行转换
            cmd = [
                str(self.libreoffice_path),
                '--headless',
                '--convert-to',
                'html:HTML:EmbedImages',
                '--outdir',
                str(output_dir),
                str(doc_path)
            ]
            
            logger.debug(f"Running LibreOffice command: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                logger.error(f"LibreOffice conversion failed: {result.stderr}")
                raise RuntimeError(f"HTML conversion failed: {result.stderr}")
            
            if not output_path.exists():
                raise RuntimeError("HTML file was not created")
            
            logger.info(f"HTML generated successfully: {output_path}")
            return str(output_path)
            
        except subprocess.TimeoutExpired:
            logger.error("LibreOffice conversion timed out")
            raise RuntimeError("HTML conversion timed out")
        except Exception as e:
            logger.error(f"Failed to convert to HTML: {e}")
            raise
    
    def open_preview(self, preview_path: str) -> bool:
        """
        在浏览器中打开预览
        
        Args:
            preview_path: 预览文件路径
            
        Returns:
            bool: 是否成功打开
        """
        try:
            preview_path = Path(preview_path)
            if not preview_path.exists():
                logger.error(f"Preview file not found: {preview_path}")
                return False
            
            # 使用默认浏览器打开
            webbrowser.open(f"file://{preview_path.absolute()}")
            logger.info(f"Preview opened in browser: {preview_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to open preview: {e}")
            return False
    
    def create_pdf_viewer_page(self, pdf_path: str) -> str:
        """
        创建PDF查看器页面
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            str: 查看器页面路径
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF not found: {pdf_path}")
        
        viewer_dir = self.temp_dir / 'viewer'
        viewer_dir.mkdir(exist_ok=True)
        
        # 复制PDF到查看器目录
        pdf_copy = viewer_dir / pdf_path.name
        shutil.copy2(pdf_path, pdf_copy)
        
        # 创建HTML查看器页面
        viewer_html = viewer_dir / 'viewer.html'
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档预览 - {pdf_path.stem}</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .title {{
            font-size: 18px;
            font-weight: bold;
        }}
        .controls {{
            display: flex;
            gap: 10px;
        }}
        .btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }}
        .btn:hover {{
            background: #2980b9;
        }}
        .pdf-container {{
            width: 100%;
            height: 80vh;
            border: none;
        }}
        .info {{
            padding: 15px 20px;
            background: #ecf0f1;
            border-top: 1px solid #bdc3c7;
            font-size: 14px;
            color: #7f8c8d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Word文档预览 - {pdf_path.stem}</div>
            <div class="controls">
                <button class="btn" onclick="window.print()">打印</button>
                <button class="btn" onclick="downloadPdf()">下载PDF</button>
                <button class="btn" onclick="window.close()">关闭</button>
            </div>
        </div>
        <iframe class="pdf-container" src="{pdf_copy.name}#toolbar=1&navpanes=1&scrollbar=1"></iframe>
        <div class="info">
            <strong>提示：</strong>使用浏览器的缩放功能调整显示大小，使用Ctrl+F搜索内容。
            生成时间：{pdf_path.stat().st_mtime}
        </div>
    </div>
    
    <script>
        function downloadPdf() {{
            const link = document.createElement('a');
            link.href = '{pdf_copy.name}';
            link.download = '{pdf_path.name}';
            link.click();
        }}
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {{
            if (e.ctrlKey && e.key === 'p') {{
                e.preventDefault();
                window.print();
            }}
        }});
    </script>
</body>
</html>
        """
        
        with open(viewer_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"PDF viewer page created: {viewer_html}")
        return str(viewer_html)
    
    def _find_libreoffice(self) -> Optional[Path]:
        """查找LibreOffice安装路径 - 增强版检测"""
        # 首先尝试PATH中的命令
        for cmd in ['soffice', 'libreoffice']:
            try:
                result = subprocess.run([cmd, '--version'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.debug(f"Found LibreOffice command: {cmd}")
                    return Path(cmd)  # 返回命令名，系统会在PATH中查找
            except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                continue

        # Windows系统的特殊处理
        if os.name == 'nt':
            windows_paths = [
                r"C:\Program Files\LibreOffice\program\soffice.exe",
                r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
                r"C:\Users\<USER>\AppData\Local\Programs\LibreOffice\program\soffice.exe",
                r"D:\Program Files\LibreOffice\program\soffice.exe",
                r"D:\Program Files (x86)\LibreOffice\program\soffice.exe",
            ]

            for path_template in windows_paths:
                # 展开环境变量
                expanded_path = os.path.expandvars(path_template)
                path = Path(expanded_path)

                if path.exists():
                    try:
                        # 测试是否能正常执行
                        result = subprocess.run([str(path), '--version'],
                                              capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            logger.debug(f"Found LibreOffice at: {path}")
                            return path
                    except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                        continue

        # Linux/macOS路径
        unix_paths = [
            Path("/usr/bin/libreoffice"),
            Path("/usr/local/bin/libreoffice"),
            Path("/opt/libreoffice/program/soffice"),
            Path("/Applications/LibreOffice.app/Contents/MacOS/soffice"),
        ]

        for path in unix_paths:
            if path.exists():
                try:
                    result = subprocess.run([str(path), '--version'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        logger.debug(f"Found LibreOffice at: {path}")
                        return path
                except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                    continue

        logger.warning("LibreOffice not found in common locations")
        return None
    
    def batch_convert(self, doc_paths: List[str], output_format: str = 'pdf') -> Dict[str, Any]:
        """
        批量转换文档
        
        Args:
            doc_paths: 文档路径列表
            output_format: 输出格式
            
        Returns:
            Dict: 转换结果
        """
        results = {
            'success': [],
            'failed': [],
            'total': len(doc_paths)
        }
        
        for doc_path in doc_paths:
            try:
                preview_path = self.generate_preview(doc_path, output_format)
                results['success'].append({
                    'source': doc_path,
                    'preview': preview_path
                })
                logger.debug(f"Converted successfully: {doc_path}")
                
            except Exception as e:
                results['failed'].append({
                    'source': doc_path,
                    'error': str(e)
                })
                logger.error(f"Failed to convert {doc_path}: {e}")
        
        logger.info(f"Batch conversion completed: {len(results['success'])}/{results['total']} successful")
        return results
    
    def cleanup_temp_files(self, older_than_hours: int = 24):
        """
        清理临时文件
        
        Args:
            older_than_hours: 清理多少小时前的文件
        """
        import time
        
        if not self.temp_dir.exists():
            return
        
        cutoff_time = time.time() - (older_than_hours * 3600)
        cleaned_count = 0
        
        try:
            for file_path in self.temp_dir.rglob('*'):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
            
            # 清理空目录
            for dir_path in self.temp_dir.rglob('*'):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    dir_path.rmdir()
            
            logger.info(f"Cleaned up {cleaned_count} temporary files")
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
    
    def get_preview_info(self, preview_path: str) -> Dict[str, Any]:
        """
        获取预览文件信息
        
        Args:
            preview_path: 预览文件路径
            
        Returns:
            Dict: 文件信息
        """
        try:
            preview_path = Path(preview_path)
            if not preview_path.exists():
                return {}
            
            stat = preview_path.stat()
            
            info = {
                'path': str(preview_path),
                'size': stat.st_size,
                'size_mb': round(stat.st_size / 1024 / 1024, 2),
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'format': preview_path.suffix.lower(),
                'exists': True
            }
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get preview info: {e}")
            return {'exists': False, 'error': str(e)}
