{"timestamp": "2025-07-28 16:32:40", "model": "deepseek-v3", "api_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "headers": {"Content-Type": "application/json"}, "request_data": {"model": "ep-20250326212423-9dhws", "messages": [{"role": "system", "content": "你是一个专业的全栈开发师，精通产品规划与UI/UX设计，能够根据功能设计文档创建美观的HTML原型页面。"}, {"role": "user", "content": "### **【提示词模板正文 V6.0 - 原子化锚点版】**\n\n### [SYSTEM] 角色定义\n你是一位追求卓越、拥有双重专长的全栈开发专家与UI/UX设计师。你既是构建**秩序井然的后台管理系统**的架构师，也是打造**富有洞察力的数据可视化大屏**的艺术家。\n\n### [SYSTEM] 输出合同与锚点规范\n1.  **原子化双层锚点系统 (Atomic Dual-Layer Anchors)**: 你必须在代码中植入两种锚点，实现最大程度的可修改性：\n    *   **`<!-- SLOT::name::BEGIN/END -->` (结构层)**: 用于包裹**主要的、独立的功能区块**（例如：整个筛选面板、整个股东信息卡片、整个弹窗）。\n    *   **`<!-- CHUNK::name::BEGIN/END -->` (原子层)**: 用于包裹SLOT内部**几乎所有可独立更新的、原子化的内容单元**。这**不仅仅**是表格行，更包括：**单个键值对信息项**（如\"注册资本：1000万\"）、**单个统计卡片**、**单个按钮**、列表项、标题等。**目标是让页面的每一个信息点都能被精准定位和修改。**\n2.  **禁止省略号**：禁止使用 `...` 或任何省略标记，必须输出完整代码。\n3.  **完整可运行**：生成的HTML页面可直接双击运行，无控制台错误。\n4.  **标准格式**：必须包含完整的 `<head>` 和 `<body>` 标签。\n5.  **锚点命名规范**：\n    *   **SLOT**: 语义化命名，使用kebab-case（短横线连接），如 `header`, `filters`, `company-overview-card`, `shareholder-modal`。\n    *   **CHUNK**: 采用 **\"父级SLOT名-具体内容\"** 的格式，确保唯一且表意清晰。例如 `overview-reg-time`, `overview-company-tags`, `shareholder-table-row`, `modal-change-record-row`。\n\n### [SYSTEM] 流程规范\n按 **PLAN → CODE → END** 三段输出：\n- **PLAN**: 先列出页面模块清单，并规划出所有 `SLOT`。然后，**对每个SLOT内部进行详细拆解，为每一个独立的、可被修改的信息点（如\"注册资本\"、\"法人代表\"等）规划对应的`CHUNK`锚点**。\n- **CODE**: 输出完整HTML代码，包含所有规划好的 `SLOT` 和 `CHUNK` 锚点标记。\n- **END**: 以 `<!-- END -->` 结束\n\n你的任务是：仔细分析下方的需求，**智能判断本次任务属于\"管理后台\"还是\"数据大屏\"**，然后调用对应的设计心法和代码范例，输出一个功能完整、代码优雅、视觉精致、**锚点极其完备**的**单文件HTML原型页面**。\n\n### **一、 设计心法库 (Design Philosophy Library)**\n\n#### **A. 通用设计哲学 (适用于所有页面)**\n* **结构至上:** 使用卡片(`Card`)或区块(`Section`)明确分离功能块。\n* **空间呼吸感:** 善用 `p-`、`gap-`、`space-y-` 创造秩序和焦点。\n* **信息层级:** 通过字号、字重、颜色（如 `text-gray-900` vs `text-gray-500`）区分信息主次。\n* **交互一致性:** 遵循已建立的模式（模态框、选项卡等）。\n* **细节是魔鬼:** 所有可点击元素必须有 `hover:` 效果和 `transition` 动画。恰当使用图标点缀。\n\n#### **B. 数据大屏专属心法 (当任务涉及\"看板\"、\"监控\"、\"分析大屏\"、\"可视化\"时，必须优先应用此心法)**\n* **布局模式:** 采用 `grid` 布局（如 `grid-cols-12`），实现模块化的\"小部件(Widget)\"式布局。\n* **深色模式优先:** 背景采用深色系（如 `bg-gray-900` 或 `bg-slate-900`），以突出高亮的图表和数据。\n* **数据为王:** 图表是页面的主角。使用鲜艳、对比度高的颜色（如霓虹色系的蓝、绿、紫）来绘制图表。\n* **关键指标突出:** 页面顶部或关键位置必须有大字号、加粗的\"数字翻牌器\"或统计卡片，用以展示最重要的核心指标（KPIs）。\n* **视觉氛围:** 善用渐变色边框、背景辉光、微妙的背景图案，营造科技感和沉浸式体验。\n\n### **二、 权威代码范例库 (Authoritative Code Example Library)**\n\n你必须将以下范例视为**绝对的圣经**。根据你对任务类型的判断，选择最匹配的范例进行模仿。其内部的JavaScript实现方式是保证所有功能正常的关键。\n\n#### **A. 管理后台类页面范例 (适用于增删改查、列表、表单等功能)**\n*   **风格核心:** 明亮、干净、功能导向、操作清晰。\n*   **背景:** `bg-gradient-to-br from-gray-50 to-blue-50`\n*   **卡片:** `bg-white rounded-lg shadow-md`\n*   **锚点示例:**\n    ```html\n    <!-- SLOT::header::BEGIN -->\n    <header class=\"bg-white shadow-sm border-b\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <h1 class=\"text-3xl font-bold text-gray-900\">页面标题</h1>\n        </div>\n    </header>\n    <!-- SLOT::header::END -->\n    \n    <!-- SLOT::filters::BEGIN -->\n    <div class=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 class=\"text-lg font-semibold text-gray-900 mb-4\">筛选条件</h2>\n        <!-- 筛选表单内容 -->\n    </div>\n    <!-- SLOT::filters::END -->\n    \n    <!-- SLOT::main-content::BEGIN -->\n    <div class=\"bg-white rounded-lg shadow-md\">\n        <!-- 主要内容区域 -->\n    </div>\n    <!-- SLOT::main-content::END -->\n    ```\n*   **参考代码:**\n    *   `[<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>政策级别管理</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#3b82f6',\n                        secondary: '#64748b'\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <div class=\"container mx-auto px-4 py-6\">\n        <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">政策级别管理</h1>\n\n        <!-- 主要内容区域 -->\n        <div class=\"flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]\">\n            <!-- 左侧主内容区 -->\n            <div class=\"flex-1 lg:w-4/5 flex flex-col space-y-3\">\n                <!-- 条件筛选区 -->\n                <div class=\"bg-white shadow-md rounded-lg p-3 mb-3\">\n                    <h2 class=\"text-lg font-medium text-gray-800 mb-1\">筛选条件</h2>\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                        <div>\n                            <label for=\"policyName\" class=\"block text-sm font-medium text-gray-700 mb-0.5\">政策名称</label>\n                            <input type=\"text\" id=\"policyName\" class=\"w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入政策名称\">\n                        </div>\n                        <div>\n                            <label for=\"documentNumber\" class=\"block text-sm font-medium text-gray-700 mb-0.5\">发文文号</label>\n                            <input type=\"text\" id=\"documentNumber\" class=\"w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入发文文号\">\n                        </div>\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-0.5\">政策级别</label>\n                            <div class=\"flex flex-wrap gap-1\">\n                                <button class=\"px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500\" onclick=\"toggleLevel(this)\">国家级</button>\n                                <button class=\"px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\" onclick=\"toggleLevel(this)\">省级</button>\n                                <button class=\"px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\" onclick=\"toggleLevel(this)\">市级</button>\n                                <button class=\"px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\" onclick=\"toggleLevel(this)\">区级</button>\n                            </div>\n                        </div>\n                        <div>\n                            <label for=\"status\" class=\"block text-sm font-medium text-gray-700 mb-0.5\">生效状态</label>\n                            <select id=\"status\" class=\"w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"\">全部</option>\n                                <option value=\"effective\">生效</option>\n                                <option value=\"ineffective\">失效</option>\n                            </select>\n                        </div>\n                        <div>\n                            <label for=\"effectiveDate\" class=\"block text-sm font-medium text-gray-700 mb-0.5\">生效日期</label>\n                            <div class=\"flex space-x-1\">\n                                <input type=\"date\" id=\"startDate\" class=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <span class=\"flex items-center text-gray-500 text-xs\">至</span>\n                                <input type=\"date\" id=\"endDate\" class=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            </div>\n                        </div>\n                        <div>\n                            <label for=\"department\" class=\"block text-sm font-medium text-gray-700 mb-0.5\">发文部门</label>\n                            <select id=\"department\" class=\"w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"\">全部</option>\n                                <option value=\"state-council\">国务院</option>\n                                <option value=\"ministry\">部委</option>\n                                <option value=\"provincial\">省政府</option>\n                                <option value=\"municipal\">市政府</option>\n                            </select>\n                        </div>\n                    </div>\n                    <div class=\"mt-1.5 flex justify-end space-x-3\">\n                        <button class=\"px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            重置\n                        </button>\n                        <button class=\"px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            查询\n                        </button>\n                    </div>\n                </div>\n\n\n                <!-- 主内容区域 -->\n                <div class=\"bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3\">\n                    <!-- 政策级别列表区 -->\n                    <div class=\"w-full\">\n                        <!-- 级别列表区 -->\n                        <div class=\"p-4 border-b border-gray-200\">\n                            <div class=\"flex justify-between items-center\">\n                                <h2 class=\"text-lg font-medium text-gray-800\">政策级别列表</h2>\n                                <button onclick=\"openEditModal()\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\" />\n                                    </svg>\n                                    批量变更级别\n                                </button>\n                            </div>\n                        </div>\n                        <div class=\"flex-1 overflow-auto\">\n                            <table class=\"min-w-full divide-y divide-gray-200\">\n                                <thead class=\"bg-gray-50 sticky top-0\">\n                                    <tr>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">政策名称</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">发文文号</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">当前级别</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">生效状态</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">引用次数</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">最后更新</th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                                    </tr>\n                                </thead>\n                                <tbody class=\"bg-white divide-y divide-gray-200\">\n                                    <tr class=\"hover:bg-gray-50\">\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <div class=\"text-sm font-medium text-gray-900\">关于促进科技创新发展的若干意见</div>\n                                            <div class=\"text-sm text-gray-500\">科技创新政策</div>\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">国发〔2024〕15号</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">国家级</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">生效</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">156</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-15 10:30</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                            <button onclick=\"openEditModal('1')\" class=\"text-indigo-600 hover:text-indigo-900\">变更级别</button>\n                                            <button class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                                            <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </td>\n                                    </tr>\n                                    <tr class=\"hover:bg-gray-50\">\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <div class=\"text-sm font-medium text-gray-900\">省级产业发展扶持政策实施细则</div>\n                                            <div class=\"text-sm text-gray-500\">产业发展政策</div>\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">浙政发〔2024〕8号</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">省级</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">生效</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">89</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-12 14:20</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                            <button onclick=\"openEditModal('2')\" class=\"text-indigo-600 hover:text-indigo-900\">变更级别</button>\n                                            <button class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                                            <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </td>\n                                    </tr>\n                                    <tr class=\"hover:bg-gray-50\">\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <div class=\"text-sm font-medium text-gray-900\">市级中小企业发展专项资金管理办法</div>\n                                            <div class=\"text-sm text-gray-500\">资金管理政策</div>\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">杭政办〔2024〕12号</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">市级</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">失效</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">23</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-08 09:15</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                            <button onclick=\"openEditModal('3')\" class=\"text-indigo-600 hover:text-indigo-900\">变更级别</button>\n                                            <button class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                                            <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </td>\n                                    </tr>\n                                    <tr class=\"hover:bg-gray-50\">\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <div class=\"text-sm font-medium text-gray-900\">区级人才引进激励政策</div>\n                                            <div class=\"text-sm text-gray-500\">人才政策</div>\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">西湖政发〔2024〕3号</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">区级</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">生效</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">67</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-05 16:45</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                            <button onclick=\"openEditModal('4')\" class=\"text-indigo-600 hover:text-indigo-900\">变更级别</button>\n                                            <button class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                                            <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </td>\n                                    </tr>\n                                    <tr class=\"hover:bg-gray-50\">\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                                            <div class=\"text-sm font-medium text-gray-900\">数字经济发展三年行动计划</div>\n                                            <div class=\"text-sm text-gray-500\">数字经济政策</div>\n                                        </td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">-</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">未分级</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">生效</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">12</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-03 11:20</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                            <button onclick=\"openEditModal('5')\" class=\"text-indigo-600 hover:text-indigo-900\">变更级别</button>\n                                            <button class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                                            <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- 右侧侧边栏 -->\n            <div class=\"w-full lg:w-1/5 lg:min-w-[280px] space-y-4\">\n                <!-- 统计与预警区 -->\n                <div class=\"bg-white shadow-md rounded-lg p-4\">\n                    <h3 class=\"text-lg font-medium text-gray-800 mb-4\">统计与预警</h3>\n                    <div class=\"space-y-6\">\n                        <!-- 政策级别分布 -->\n                        <div>\n                            <h4 class=\"text-sm font-medium text-gray-700 mb-3\">政策级别分布</h4>\n                            <div class=\"space-y-3\">\n                                <div class=\"flex justify-between items-center\">\n                                    <span class=\"text-sm text-gray-600\">国家级</span>\n                                    <div class=\"flex items-center space-x-2\">\n                                        <div class=\"w-24 bg-gray-200 rounded-full h-2\">\n                                            <div class=\"bg-red-500 h-2 rounded-full\" style=\"width: 85%\"></div>\n                                        </div>\n                                        <span class=\"text-sm font-medium text-gray-900\">1,245</span>\n                                    </div>\n                                </div>\n                                <div class=\"flex justify-between items-center\">\n                                    <span class=\"text-sm text-gray-600\">省级</span>\n                                    <div class=\"flex items-center space-x-2\">\n                                        <div class=\"w-24 bg-gray-200 rounded-full h-2\">\n                                            <div class=\"bg-orange-500 h-2 rounded-full\" style=\"width: 70%\"></div>\n                                        </div>\n                                        <span class=\"text-sm font-medium text-gray-900\">2,156</span>\n                                    </div>\n                                </div>\n                                <div class=\"flex justify-between items-center\">\n                                    <span class=\"text-sm text-gray-600\">市级</span>\n                                    <div class=\"flex items-center space-x-2\">\n                                        <div class=\"w-24 bg-gray-200 rounded-full h-2\">\n                                            <div class=\"bg-yellow-500 h-2 rounded-full\" style=\"width: 60%\"></div>\n                                        </div>\n                                        <span class=\"text-sm font-medium text-gray-900\">3,892</span>\n                                    </div>\n                                </div>\n                                <div class=\"flex justify-between items-center\">\n                                    <span class=\"text-sm text-gray-600\">区级</span>\n                                    <div class=\"flex items-center space-x-2\">\n                                        <div class=\"w-24 bg-gray-200 rounded-full h-2\">\n                                            <div class=\"bg-green-500 h-2 rounded-full\" style=\"width: 45%\"></div>\n                                        </div>\n                                        <span class=\"text-sm font-medium text-gray-900\">1,567</span>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n\n                        <!-- 近30天级别变更趋势 -->\n                        <div>\n                            <h4 class=\"text-sm font-medium text-gray-700 mb-3\">近30天级别变更趋势</h4>\n                            <div class=\"flex items-center justify-between mb-2\">\n                                <span class=\"text-2xl font-bold text-blue-600\">156</span>\n                                <span class=\"text-xs text-gray-500\">次变更</span>\n                            </div>\n                            <div class=\"h-16 flex items-end space-x-1\">\n                                <div class=\"w-3 bg-blue-200 rounded-t\" style=\"height: 30%\"></div>\n                                <div class=\"w-3 bg-blue-300 rounded-t\" style=\"height: 50%\"></div>\n                                <div class=\"w-3 bg-blue-400 rounded-t\" style=\"height: 40%\"></div>\n                                <div class=\"w-3 bg-blue-500 rounded-t\" style=\"height: 70%\"></div>\n                                <div class=\"w-3 bg-blue-600 rounded-t\" style=\"height: 100%\"></div>\n                                <div class=\"w-3 bg-blue-500 rounded-t\" style=\"height: 80%\"></div>\n                                <div class=\"w-3 bg-blue-400 rounded-t\" style=\"height: 60%\"></div>\n                            </div>\n                        </div>\n\n                        <!-- 预警信息 -->\n                        <div>\n                            <h4 class=\"text-sm font-medium text-gray-700 mb-3\">预警信息</h4>\n                            <div class=\"space-y-3\">\n                                <div class=\"bg-red-50 p-3 rounded-lg border-l-4 border-red-500\">\n                                    <div class=\"flex items-center\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 text-red-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                                        </svg>\n                                        <div>\n                                            <p class=\"text-xs font-medium text-red-800\">23个政策缺失级别标签</p>\n                                            <a href=\"#\" class=\"text-xs text-red-700 hover:text-red-900\">立即处理 →</a>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div class=\"bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500\">\n                                    <div class=\"flex items-center\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 text-yellow-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                        </svg>\n                                        <div>\n                                            <p class=\"text-xs font-medium text-yellow-800\">8个政策即将到期</p>\n                                            <a href=\"#\" class=\"text-xs text-yellow-700 hover:text-yellow-900\">查看详情 →</a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- 批量维护区（可折叠） -->\n                <div class=\"bg-white shadow-md rounded-lg\">\n                    <div class=\"p-4 border-b border-gray-200\">\n                        <button onclick=\"toggleBatchMaintenance()\" class=\"w-full flex items-center justify-between text-left\">\n                            <h3 class=\"text-lg font-medium text-gray-800\">批量维护</h3>\n                            <svg id=\"batch-toggle-icon\" xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-gray-500 transform transition-transform duration-200\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\" />\n                            </svg>\n                        </button>\n                    </div>\n                    <div id=\"batch-content\" class=\"p-4 space-y-4\">\n                        <div>\n                            <a href=\"#\" class=\"text-blue-600 hover:text-blue-800 text-sm flex items-center\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                                </svg>\n                                下载标准模板\n                            </a>\n                        </div>\n                        <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                            </svg>\n                            <div class=\"mt-4\">\n                                <label for=\"file-upload\" class=\"cursor-pointer\">\n                                    <span class=\"mt-2 block text-sm font-medium text-gray-900\">点击上传文件</span>\n                                    <span class=\"mt-1 block text-xs text-gray-500\">支持 .xlsx, .xls 格式</span>\n                                </label>\n                                <input id=\"file-upload\" name=\"file-upload\" type=\"file\" class=\"sr-only\" accept=\".xlsx,.xls\">\n                            </div>\n                        </div>\n                        <div class=\"hidden\" id=\"upload-progress\">\n                            <div class=\"flex items-center justify-between text-sm\">\n                                <span class=\"text-gray-600\">上传进度</span>\n                                <span class=\"text-gray-600\">75%</span>\n                            </div>\n                            <div class=\"mt-1 w-full bg-gray-200 rounded-full h-2\">\n                                <div class=\"bg-blue-600 h-2 rounded-full\" style=\"width: 75%\"></div>\n                            </div>\n                        </div>\n                        <button class=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            开始导入\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 级别编辑弹窗 -->\n    <div id=\"editModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">变更政策级别</h3>\n                    <button onclick=\"closeModal()\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                    </button>\n                </div>\n                <form class=\"space-y-4\">\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择新级别</label>\n                        <div class=\"space-y-2\">\n                            <label class=\"flex items-center\">\n                                <input type=\"radio\" name=\"level\" value=\"national\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">国家级</span>\n                            </label>\n                            <label class=\"flex items-center\">\n                                <input type=\"radio\" name=\"level\" value=\"provincial\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">省级</span>\n                            </label>\n                            <label class=\"flex items-center\">\n                                <input type=\"radio\" name=\"level\" value=\"municipal\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">市级</span>\n                            </label>\n                            <label class=\"flex items-center\">\n                                <input type=\"radio\" name=\"level\" value=\"district\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">区级</span>\n                            </label>\n                        </div>\n                    </div>\n                    <div>\n                        <label for=\"changeReason\" class=\"block text-sm font-medium text-gray-700 mb-1\">变更说明</label>\n                        <textarea id=\"changeReason\" rows=\"3\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"请输入变更原因和说明\"></textarea>\n                    </div>\n                    <div>\n                        <label for=\"effectiveDate\" class=\"block text-sm font-medium text-gray-700 mb-1\">生效时间</label>\n                        <input type=\"datetime-local\" id=\"effectiveDate\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                    </div>\n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <button type=\"button\" onclick=\"closeModal()\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            取消\n                        </button>\n                        <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                            保存变更\n                        </button>\n                    </div>\n                </form>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        function toggleLevel(button) {\n            if (button.classList.contains('border-blue-500')) {\n                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');\n                button.classList.add('border-gray-300', 'text-gray-700');\n            } else {\n                button.classList.remove('border-gray-300', 'text-gray-700');\n                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');\n            }\n        }\n\n        function openEditModal(id) {\n            document.getElementById('editModal').classList.remove('hidden');\n        }\n\n        function closeModal() {\n            document.getElementById('editModal').classList.add('hidden');\n        }\n\n        // 点击模态框外部关闭\n        document.getElementById('editModal').addEventListener('click', function(e) {\n            if (e.target === this) {\n                closeModal();\n            }\n        });\n\n        // 批量维护折叠功能\n        function toggleBatchMaintenance() {\n            const content = document.getElementById('batch-content');\n            const icon = document.getElementById('batch-toggle-icon');\n            \n            if (content.style.display === 'none') {\n                content.style.display = 'block';\n                icon.style.transform = 'rotate(0deg)';\n            } else {\n                content.style.display = 'none';\n                icon.style.transform = 'rotate(-90deg)';\n            }\n        }\n\n        // 文件上传处理\n        document.getElementById('file-upload').addEventListener('change', function(e) {\n            if (e.target.files.length > 0) {\n                document.getElementById('upload-progress').classList.remove('hidden');\n                // 模拟上传进度\n                let progress = 0;\n                const interval = setInterval(() => {\n                    progress += 10;\n                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';\n                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';\n                    if (progress >= 100) {\n                        clearInterval(interval);\n                        setTimeout(() => {\n                            document.getElementById('upload-progress').classList.add('hidden');\n                            alert('文件上传成功！');\n                        }, 500);\n                    }\n                }, 200);\n            }\n        });\n    </script>\n</body>\n</html>]`\n    *   `[<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>政策对象管理</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: {\n                            50: '#eff6ff',\n                            100: '#dbeafe',\n                            200: '#bfdbfe',\n                            300: '#93c5fd',\n                            400: '#60a5fa',\n                            500: '#3b82f6',\n                            600: '#2563eb',\n                            700: '#1d4ed8',\n                            800: '#1e40af',\n                            900: '#1e3a8a',\n                        }\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <div class=\"container mx-auto p-6\">\n        <!-- 页面标题 -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-3xl font-bold text-gray-900 mb-2\">政策对象管理</h1>\n            <p class=\"text-gray-600\">构建覆盖主体类型、规模层级与行业领域的统一对象分类体系，为创新主体在政策享受分析与精准匹配过程中提供可信的标签依据</p>\n        </div>\n\n        <div class=\"grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]\">\n            <!-- 左侧主要内容区 -->\n            <div class=\"xl:col-span-3 space-y-6\">\n                <!-- 条件筛选区 -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                        <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n                        </svg>\n                        条件筛选\n                    </h2>\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">主体类型</label>\n                            <div class=\"flex flex-wrap gap-2\">\n                                <label class=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                    企业\n                                </label>\n                                <label class=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                    高校\n                                </label>\n                                <label class=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                    科研院所\n                                </label>\n                                <label class=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                    个人\n                                </label>\n                            </div>\n                        </div>\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">规模区间</label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"\">全部规模</option>\n                                <option value=\"micro\">微型（10人以下）</option>\n                                <option value=\"small\">小型（10-99人）</option>\n                                <option value=\"medium\">中型（100-499人）</option>\n                                <option value=\"large\">大型（500人以上）</option>\n                            </select>\n                        </div>\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">行业类别</label>\n                            <div class=\"relative\">\n                                <button id=\"industryButton\" onclick=\"toggleIndustryTree()\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center\">\n                                    <span id=\"selectedIndustry\">请选择行业类别</span>\n                                    <svg class=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\n                                    </svg>\n                                </button>\n                                <div id=\"industryTree\" class=\"absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto\">\n                                    <ul class=\"space-y-2\">\n                                        <li>\n                                            <div class=\"flex items-center\">\n                                                <svg class=\"w-4 h-4 text-gray-500 cursor-pointer\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                                                </svg>\n                                                <label class=\"ml-2 flex items-center\">\n                                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                                    制造业\n                                                </label>\n                                            </div>\n                                            <ul class=\"pl-6 mt-2 space-y-2 hidden\">\n                                                <li>\n                                                    <label class=\"flex items-center\">\n                                                        <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                                        计算机、通信和其他电子设备制造业\n                                                    </label>\n                                                </li>\n                                                <li>\n                                                    <label class=\"flex items-center\">\n                                                        <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                                        医药制造业\n                                                    </label>\n                                                </li>\n                                            </ul>\n                                        </li>\n                                        <li>\n                                            <div class=\"flex items-center\">\n                                                <svg class=\"w-4 h-4 text-gray-500 cursor-pointer\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                                                </svg>\n                                                <label class=\"ml-2 flex items-center\">\n                                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                                    信息传输、软件和信息技术服务业\n                                                </label>\n                                            </div>\n                                        </li>\n                                        <li>\n                                            <div class=\"flex items-center\">\n                                                <svg class=\"w-4 h-4 text-gray-500 cursor-pointer\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                                                </svg>\n                                                <label class=\"ml-2 flex items-center\">\n                                                    <input type=\"checkbox\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2\">\n                                                    科学研究和技术服务业\n                                                </label>\n                                            </div>\n                                        </li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </div>\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">关键字搜索</label>\n                            <input type=\"text\" placeholder=\"输入对象编码或描述关键词\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">生效状态</label>\n                            <div class=\"flex items-center space-x-4 mt-2\">\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"radio\" name=\"status\" value=\"all\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\" checked>\n                                    <span class=\"ml-2 text-sm text-gray-700\">全部</span>\n                                </label>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"radio\" name=\"status\" value=\"active\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                    <span class=\"ml-2 text-sm text-gray-700\">已启用</span>\n                                </label>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"radio\" name=\"status\" value=\"inactive\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                    <span class=\"ml-2 text-sm text-gray-700\">已停用</span>\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"flex space-x-3 mt-4\">\n                        <button class=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\">\n                            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n                            </svg>\n                            查询\n                        </button>\n                        <button class=\"bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center\">\n                            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                            </svg>\n                            重置\n                        </button>\n                    </div>\n                </div>\n\n                <!-- 对象分类列表区 -->\n                <div class=\"bg-white rounded-lg shadow-md\">\n                    <div class=\"p-6 border-b border-gray-200\">\n                        <div class=\"flex justify-between items-center\">\n                            <h2 class=\"text-lg font-semibold text-gray-900 flex items-center\">\n                                <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"></path>\n                                </svg>\n                                对象分类列表\n                            </h2>\n                            <div class=\"flex space-x-2\">\n                                <div class=\"relative\" id=\"exportDropdown\">\n                                    <button onclick=\"toggleExportDropdown()\" class=\"bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center\">\n                                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12\"></path>\n                                        </svg>\n                                        导出\n                                        <svg class=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\n                                        </svg>\n                                    </button>\n                                    <div id=\"exportOptions\" class=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10\">\n                                        <div class=\"py-1\">\n                                            <a href=\"#\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">导出为Excel</a>\n                                            <a href=\"#\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">导出为CSV</a>\n                                        </div>\n                                    </div>\n                                </div>\n                                <button onclick=\"openObjectModal()\" class=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\">\n                                    <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                                    </svg>\n                                    新增分类\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"overflow-x-auto\">\n                        <table class=\"w-full\">\n                            <thead class=\"bg-gray-50\">\n                                <tr>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">对象编码</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">主体类型</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">规模级别</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">所属行业</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">生效状态</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">最后更新时间</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                                </tr>\n                            </thead>\n                            <tbody class=\"bg-white divide-y divide-gray-200\">\n                                <tr class=\"hover:bg-gray-50\">\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm font-medium text-gray-900\">ENT-SM-MFG-001</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">企业</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">小型</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">制造业-计算机、通信和其他电子设备制造业</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">已启用</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-06-20 14:30</td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                        <div class=\"flex space-x-2\">\n                                            <button onclick=\"viewObject('obj1')\" class=\"text-blue-600 hover:text-blue-900\">查看</button>\n                                            <button onclick=\"editObject('obj1')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                            <button onclick=\"disableObject('obj1')\" class=\"text-yellow-600 hover:text-yellow-900\">停用</button>\n                                            <button onclick=\"deleteObject('obj1')\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </div>\n                                    </td>\n                                </tr>\n                                <tr class=\"hover:bg-gray-50\">\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm font-medium text-gray-900\">UNI-LG-EDU-001</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">高校</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">大型</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">教育</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">已启用</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-05-15 09:45</td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                        <div class=\"flex space-x-2\">\n                                            <button onclick=\"viewObject('obj2')\" class=\"text-blue-600 hover:text-blue-900\">查看</button>\n                                            <button onclick=\"editObject('obj2')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                            <button onclick=\"disableObject('obj2')\" class=\"text-yellow-600 hover:text-yellow-900\">停用</button>\n                                            <button onclick=\"deleteObject('obj2')\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </div>\n                                    </td>\n                                </tr>\n                                <tr class=\"hover:bg-gray-50\">\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm font-medium text-gray-900\">INS-MD-RES-001</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">科研院所</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">中型</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">科学研究和技术服务业</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                                        <div class=\"text-sm text-gray-500\">已停用</div>\n                                    </td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-04-10 16:20</td>\n                                    <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                        <div class=\"flex space-x-2\">\n                                            <button onclick=\"viewObject('obj3')\" class=\"text-blue-600 hover:text-blue-900\">查看</button>\n                                            <button onclick=\"editObject('obj3')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                            <button onclick=\"enableObject('obj3')\" class=\"text-green-600 hover:text-green-900\">启用</button>\n                                            <button onclick=\"deleteObject('obj3')\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                                        </div>\n                                    </td>\n                                </tr>\n                            </tbody>\n                        </table>\n                    </div>\n                    <div class=\"px-6 py-4 border-t border-gray-200\">\n                        <div class=\"flex items-center justify-between\">\n                            <div class=\"text-sm text-gray-700\">\n                                显示 <span class=\"font-medium\">1</span> 到 <span class=\"font-medium\">3</span> 条，共 <span class=\"font-medium\">42</span> 条记录\n                            </div>\n                            <div class=\"flex space-x-2\">\n                                <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">上一页</button>\n                                <button class=\"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700\">1</button>\n                                <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">2</button>\n                                <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">3</button>\n                                <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">下一页</button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- 右侧统计与批量导入区 -->\n            <div class=\"xl:col-span-1 space-y-6\">\n                <!-- 统计信息卡片 -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                        <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                        </svg>\n                        分类统计\n                    </h2>\n                    <div class=\"space-y-4\">\n                        <div class=\"bg-blue-50 p-4 rounded-lg\">\n                            <div class=\"flex items-center justify-between\">\n                                <div class=\"text-sm text-gray-500\">已启用分类总数</div>\n                                <div class=\"text-2xl font-bold text-blue-600\">38</div>\n                            </div>\n                        </div>\n                        <div class=\"bg-green-50 p-4 rounded-lg\">\n                            <div class=\"flex items-center justify-between\">\n                                <div class=\"text-sm text-gray-500\">近一月新增</div>\n                                <div class=\"text-2xl font-bold text-green-600\">12</div>\n                            </div>\n                        </div>\n                        <div class=\"bg-yellow-50 p-4 rounded-lg\">\n                            <div class=\"flex items-center justify-between\">\n                                <div class=\"text-sm text-gray-500\">已停用分类</div>\n                                <div class=\"text-2xl font-bold text-yellow-600\">4</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- 批量导入区 -->\n                <div class=\"bg-white rounded-lg shadow-md\">\n                    <div class=\"p-6 border-b border-gray-200\">\n                        <div class=\"flex items-center justify-between mb-2\">\n                            <h2 class=\"text-lg font-semibold text-gray-900 flex items-center\">\n                                <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                                </svg>\n                                批量导入\n                            </h2>\n                            <button onclick=\"toggleImportPanel()\" class=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                                <span id=\"importPanelToggle\">展开</span>\n                            </button>\n                        </div>\n                        <p class=\"text-sm text-gray-500\">支持Excel批量导入对象分类信息</p>\n                    </div>\n                    <div id=\"importPanel\" class=\"hidden\">\n                        <div class=\"p-6 space-y-4\">\n                            <div class=\"flex items-center justify-between\">\n                                <a href=\"#\" class=\"text-sm text-blue-600 hover:text-blue-800 flex items-center\">\n                                    <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                                    </svg>\n                                    下载导入模板\n                                </a>\n                                <span class=\"text-xs text-gray-500\">支持.xlsx格式</span>\n                            </div>\n                            <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                                <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                                </svg>\n                                <div class=\"mt-2\">\n                                    <label for=\"file-upload\" class=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none\">\n                                        <span>点击上传文件</span>\n                                        <input id=\"file-upload\" name=\"file-upload\" type=\"file\" class=\"sr-only\">\n                                    </label>\n                                    <p class=\"text-xs text-gray-500\">或拖拽文件到此处</p>\n                                </div>\n                                <p class=\"text-xs text-gray-500 mt-1\">最大支持10MB</p>\n                            </div>\n                            <div id=\"uploadProgress\" class=\"hidden\">\n                                <div class=\"flex items-center justify-between mb-1\">\n                                    <span class=\"text-sm font-medium text-gray-700\">上传进度</span>\n                                    <span class=\"text-sm font-medium text-gray-700\">75%</span>\n                                </div>\n                                <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n                                    <div class=\"bg-blue-600 h-2.5 rounded-full\" style=\"width: 75%\"></div>\n                                </div>\n                            </div>\n                            <div id=\"validationResult\" class=\"hidden bg-yellow-50 p-4 rounded-lg\">\n                                <div class=\"flex\">\n                                    <div class=\"flex-shrink-0\">\n                                        <svg class=\"h-5 w-5 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"></path>\n                                        </svg>\n                                    </div>\n                                    <div class=\"ml-3\">\n                                        <p class=\"text-sm font-medium text-yellow-800\">发现2条数据存在问题</p>\n                                        <ul class=\"mt-2 text-xs text-yellow-700 list-disc list-inside\">\n                                            <li>第3行：对象编码已存在</li>\n                                            <li>第5行：主体类型为必填项</li>\n                                        </ul>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"flex justify-end\">\n                                <button class=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                    开始导入\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 分类编辑弹窗 -->\n    <div id=\"objectModal\" class=\"fixed inset-0 bg-black bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen p-4\">\n            <div class=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col\">\n                <div class=\"flex items-center justify-between p-6 border-b flex-shrink-0\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">对象分类编辑</h3>\n                    <button onclick=\"closeObjectModal()\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"p-6 overflow-y-auto flex-1\">\n                    <div class=\"space-y-6\">\n                        <!-- 基本信息 -->\n                        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-2\">对象编码 <span class=\"text-red-500\">*</span></label>\n                                <input type=\"text\" placeholder=\"请输入对象编码\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <p class=\"mt-1 text-xs text-gray-500\">建议使用主体类型-规模-行业的缩写组合</p>\n                            </div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-2\">主体类型 <span class=\"text-red-500\">*</span></label>\n                                <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                    <option value=\"\">请选择主体类型</option>\n                                    <option value=\"enterprise\">企业</option>\n                                    <option value=\"university\">高校</option>\n                                    <option value=\"institute\">科研院所</option>\n                                    <option value=\"individual\">个人</option>\n                                </select>\n                            </div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-2\">规模级别 <span class=\"text-red-500\">*</span></label>\n                                <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                    <option value=\"\">请选择规模级别</option>\n                                    <option value=\"micro\">微型（10人以下）</option>\n                                    <option value=\"small\">小型（10-99人）</option>\n                                    <option value=\"medium\">中型（100-499人）</option>\n                                    <option value=\"large\">大型（500人以上）</option>\n                                </select>\n                            </div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-2\">所属行业 <span class=\"text-red-500\">*</span></label>\n                                <div class=\"relative\">\n                                    <button id=\"modalIndustryButton\" onclick=\"toggleModalIndustrySelect()\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center\">\n                                        <span id=\"modalSelectedIndustry\">请选择所属行业</span>\n                                        <svg class=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\n                                        </svg>\n                                    </button>\n                                    <div id=\"modalIndustrySelect\" class=\"absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto\">\n                                        <div class=\"space-y-2\">\n                                            <div class=\"flex items-center\">\n                                                <input type=\"radio\" name=\"industry\" id=\"ind1\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                                <label for=\"ind1\" class=\"ml-2 text-sm text-gray-700\">制造业-计算机、通信和其他电子设备制造业</label>\n                                            </div>\n                                            <div class=\"flex items-center\">\n                                                <input type=\"radio\" name=\"industry\" id=\"ind2\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                                <label for=\"ind2\" class=\"ml-2 text-sm text-gray-700\">制造业-医药制造业</label>\n                                            </div>\n                                            <div class=\"flex items-center\">\n                                                <input type=\"radio\" name=\"industry\" id=\"ind3\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                                <label for=\"ind3\" class=\"ml-2 text-sm text-gray-700\">信息传输、软件和信息技术服务业</label>\n                                            </div>\n                                            <div class=\"flex items-center\">\n                                                <input type=\"radio\" name=\"industry\" id=\"ind4\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                                <label for=\"ind4\" class=\"ml-2 text-sm text-gray-700\">科学研究和技术服务业</label>\n                                            </div>\n                                            <div class=\"flex items-center\">\n                                                <input type=\"radio\" name=\"industry\" id=\"ind5\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500\">\n                                                <label for=\"ind5\" class=\"ml-2 text-sm text-gray-700\">教育</label>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n\n                        <!-- 分类描述 -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">分类描述</label>\n                            <textarea rows=\"3\" placeholder=\"请输入分类描述\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"></textarea>\n                        </div>\n\n                        <!-- 生效状态 -->\n                        <div>\n                            <label class=\"flex items-center\">\n                                <span class=\"mr-3 text-sm font-medium text-gray-700\">生效状态:</span>\n                                <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\n                                    <input type=\"checkbox\" id=\"statusToggle\" class=\"sr-only\">\n                                    <div class=\"block h-6 bg-gray-300 rounded-full w-12\"></div>\n                                    <div class=\"dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition\"></div>\n                                </div>\n                                <span id=\"statusText\" class=\"text-sm text-gray-700\">已停用</span>\n                            </label>\n                        </div>\n                    </div>\n                </div>\n                <div class=\"flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0\">\n                    <button onclick=\"closeObjectModal()\" class=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors\">\n                        取消\n                    </button>\n                    <button class=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\">\n                        保存\n                    </button>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 行业树形选择器\n        function toggleIndustryTree() {\n            const tree = document.getElementById('industryTree');\n            tree.classList.toggle('hidden');\n        }\n        \n        // 弹窗中的行业选择器\n        function toggleModalIndustrySelect() {\n            const select = document.getElementById('modalIndustrySelect');\n            select.classList.toggle('hidden');\n        }\n        \n        // 对象分类编辑弹窗\n        function openObjectModal() {\n            document.getElementById('objectModal').classList.remove('hidden');\n        }\n        \n        function closeObjectModal() {\n            document.getElementById('objectModal').classList.add('hidden');\n        }\n        \n        // 导出下拉菜单\n        function toggleExportDropdown() {\n            document.getElementById('exportOptions').classList.toggle('hidden');\n        }\n        \n        // 批量导入面板切换\n        function toggleImportPanel() {\n            const panel = document.getElementById('importPanel');\n            const toggle = document.getElementById('importPanelToggle');\n            \n            if (panel.classList.contains('hidden')) {\n                panel.classList.remove('hidden');\n                toggle.textContent = '收起';\n            } else {\n                panel.classList.add('hidden');\n                toggle.textContent = '展开';\n            }\n        }\n        \n        // 状态开关\n        document.getElementById('statusToggle').addEventListener('change', function() {\n            const statusText = document.getElementById('statusText');\n            if (this.checked) {\n                statusText.textContent = '已启用';\n                document.querySelector('.dot').classList.add('transform', 'translate-x-6');\n            } else {\n                statusText.textContent = '已停用';\n                document.querySelector('.dot').classList.remove('transform', 'translate-x-6');\n            }\n        });\n        \n        // 模拟文件上传进度\n        document.getElementById('file-upload').addEventListener('change', function() {\n            if (this.files.length > 0) {\n                document.getElementById('uploadProgress').classList.remove('hidden');\n                setTimeout(function() {\n                    document.getElementById('validationResult').classList.remove('hidden');\n                }, 2000);\n            }\n        });\n        \n        // 其他功能函数\n        function viewObject(objectId) {\n            console.log('查看对象:', objectId);\n        }\n        \n        function editObject(objectId) {\n            openObjectModal();\n            console.log('编辑对象:', objectId);\n        }\n        \n        function disableObject(objectId) {\n            if (confirm('确定要停用这个对象分类吗？')) {\n                console.log('停用对象:', objectId);\n            }\n        }\n        \n        function enableObject(objectId) {\n            if (confirm('确定要启用这个对象分类吗？')) {\n                console.log('启用对象:', objectId);\n            }\n        }\n        \n        function deleteObject(objectId) {\n            if (confirm('确定要删除这个对象分类吗？此操作不可恢复！')) {\n                console.log('删除对象:', objectId);\n            }\n        }\n        \n        // 点击模态框外部关闭\n        document.getElementById('objectModal').addEventListener('click', function(e) {\n            if (e.target === this) {\n                closeObjectModal();\n            }\n        });\n        \n        // 点击其他区域关闭下拉菜单\n        document.addEventListener('click', function(e) {\n            // 行业树形选择器\n            const industryButton = document.getElementById('industryButton');\n            const industryTree = document.getElementById('industryTree');\n            if (!industryButton.contains(e.target) && !industryTree.contains(e.target) && !industryTree.classList.contains('hidden')) {\n                industryTree.classList.add('hidden');\n            }\n            \n            // 弹窗中的行业选择器\n            const modalIndustryButton = document.getElementById('modalIndustryButton');\n            const modalIndustrySelect = document.getElementById('modalIndustrySelect');\n            if (modalIndustryButton && modalIndustrySelect && !modalIndustryButton.contains(e.target) && !modalIndustrySelect.contains(e.target) && !modalIndustrySelect.classList.contains('hidden')) {\n                modalIndustrySelect.classList.add('hidden');\n            }\n            \n            // 导出下拉菜单\n            const exportDropdown = document.getElementById('exportDropdown');\n            const exportOptions = document.getElementById('exportOptions');\n            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {\n                exportOptions.classList.add('hidden');\n            }\n        });\n        \n        // 自定义样式\n        document.addEventListener('DOMContentLoaded', function() {\n            // 状态开关样式\n            const style = document.createElement('style');\n            style.textContent = `\n                #statusToggle:checked + .block {\n                    background-color: #2563eb;\n                }\n                #statusToggle:checked ~ .dot {\n                    transform: translateX(100%);\n                }\n            `;\n            document.head.appendChild(style);\n        });\n    </script>\n</body>\n</html>]`\n\n#### **B. 数据大屏分析类页面范例 (适用于图表密集、数据展示、监控看板等功能)**\n*   **风格核心:** 深色背景、高对比度、信息密集、视觉冲击力强。\n*   **背景:** `bg-slate-900`\n*   **卡片:** `bg-slate-800/50 border border-slate-700 rounded-lg`\n*   **锚点示例:**\n    ```html\n    <!-- SLOT::header::BEGIN -->\n    <header class=\"bg-slate-800 border-b border-slate-700\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <h1 class=\"text-3xl font-bold text-white\">数据大屏标题</h1>\n        </div>\n    </header>\n    <!-- SLOT::header::END -->\n    \n    <!-- SLOT::kpi-cards::BEGIN -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <!-- KPI指标卡片 -->\n    </div>\n    <!-- SLOT::kpi-cards::END -->\n    \n    <!-- SLOT::charts::BEGIN -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        <!-- 图表区域 -->\n    </div>\n    <!-- SLOT::charts::END -->\n    ```\n*   **参考代码:**\n    *   `[<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>仪器设备综合分析</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js\"></script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <!-- 页面标题 -->\n    <div class=\"bg-white shadow-sm border-b\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div class=\"flex justify-between items-center py-6\">\n                <div>\n                    <h1 class=\"text-3xl font-bold text-gray-900\">仪器设备综合分析</h1>\n                    <p class=\"mt-2 text-sm text-gray-600\">对全市科研仪器设备进行多维度、可视化的综合统计和分布分析</p>\n                </div>\n                <div class=\"flex space-x-3\">\n                    <!-- 数据导出按钮 -->\n                    <button class=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                        </svg>\n                        数据导出\n                    </button>\n                    <!-- 刷新数据按钮 -->\n                    <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                        </svg>\n                        刷新数据\n                    </button>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 主内容区域 -->\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <!-- 数据概览卡片 -->\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0\">\n                        <div class=\"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center\">\n                            <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                            </svg>\n                        </div>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">设备总数</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">12,847</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0\">\n                        <div class=\"w-8 h-8 bg-green-100 rounded-md flex items-center justify-center\">\n                            <svg class=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                            </svg>\n                        </div>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">在用设备</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">10,234</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0\">\n                        <div class=\"w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center\">\n                            <svg class=\"w-5 h-5 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n                            </svg>\n                        </div>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">维修中</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">1,523</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0\">\n                        <div class=\"w-8 h-8 bg-red-100 rounded-md flex items-center justify-center\">\n                            <svg class=\"w-5 h-5 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                            </svg>\n                        </div>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">闲置设备</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">1,090</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 主要分析区域 -->\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n            <!-- 分类统计区 -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex justify-between items-center mb-6\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">设备分类统计</h3>\n                    <div class=\"flex space-x-2\">\n                        <button id=\"categoryChartType\" class=\"px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200\">柱状图</button>\n                        <button onclick=\"switchCategoryChart('pie')\" class=\"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200\">饼图</button>\n                    </div>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"categoryChart\"></canvas>\n                </div>\n                <div class=\"mt-4 grid grid-cols-2 gap-4 text-sm\">\n                    <div class=\"flex justify-between\">\n                        <span class=\"text-gray-600\">光谱仪器</span>\n                        <span class=\"font-medium\">3,245台</span>\n                    </div>\n                    <div class=\"flex justify-between\">\n                        <span class=\"text-gray-600\">色谱仪器</span>\n                        <span class=\"font-medium\">2,876台</span>\n                    </div>\n                    <div class=\"flex justify-between\">\n                        <span class=\"text-gray-600\">质谱仪器</span>\n                        <span class=\"font-medium\">1,987台</span>\n                    </div>\n                    <div class=\"flex justify-between\">\n                        <span class=\"text-gray-600\">电化学仪器</span>\n                        <span class=\"font-medium\">1,654台</span>\n                    </div>\n                </div>\n            </div>\n\n            <!-- 使用状态分布区 -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex justify-between items-center mb-6\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">使用状态分布</h3>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md\">全部</button>\n                        <button class=\"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200\">本月</button>\n                    </div>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"statusChart\"></canvas>\n                </div>\n                <div class=\"mt-4 space-y-2\">\n                    <div class=\"flex items-center justify-between\">\n                        <div class=\"flex items-center\">\n                            <div class=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                            <span class=\"text-sm text-gray-600\">正常使用</span>\n                        </div>\n                        <span class=\"text-sm font-medium\">79.6%</span>\n                    </div>\n                    <div class=\"flex items-center justify-between\">\n                        <div class=\"flex items-center\">\n                            <div class=\"w-3 h-3 bg-yellow-500 rounded-full mr-2\"></div>\n                            <span class=\"text-sm text-gray-600\">维修中</span>\n                        </div>\n                        <span class=\"text-sm font-medium\">11.9%</span>\n                    </div>\n                    <div class=\"flex items-center justify-between\">\n                        <div class=\"flex items-center\">\n                            <div class=\"w-3 h-3 bg-red-500 rounded-full mr-2\"></div>\n                            <span class=\"text-sm text-gray-600\">闲置</span>\n                        </div>\n                        <span class=\"text-sm font-medium\">8.5%</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 区域分布分析区 -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <div class=\"flex justify-between items-center mb-6\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">区域分布分析</h3>\n                <div class=\"flex space-x-2\">\n                    <select class=\"px-3 py-1 text-sm border border-gray-300 rounded-md\">\n                        <option>全市</option>\n                        <option>市辖区</option>\n                        <option>县级市</option>\n                    </select>\n                    <button class=\"px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md\">热力图</button>\n                </div>\n            </div>\n            <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                <!-- 地图区域 -->\n                <div class=\"lg:col-span-2\">\n                    <div class=\"h-96 bg-gray-100 rounded-lg flex items-center justify-center relative overflow-hidden\">\n                        <img src=\"https://source.unsplash.com/800x400?map,city\" alt=\"区域分布地图\" class=\"w-full h-full object-cover rounded-lg\">\n                        <div class=\"absolute inset-0 bg-blue-500 bg-opacity-20\"></div>\n                        <div class=\"absolute top-4 left-4 bg-white rounded-lg p-3 shadow-md\">\n                            <h4 class=\"text-sm font-medium text-gray-900 mb-2\">图例</h4>\n                            <div class=\"space-y-1 text-xs\">\n                                <div class=\"flex items-center\">\n                                    <div class=\"w-3 h-3 bg-red-500 rounded-full mr-2\"></div>\n                                    <span>高密度 (>500台)</span>\n                                </div>\n                                <div class=\"flex items-center\">\n                                    <div class=\"w-3 h-3 bg-yellow-500 rounded-full mr-2\"></div>\n                                    <span>中密度 (100-500台)</span>\n                                </div>\n                                <div class=\"flex items-center\">\n                                    <div class=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                                    <span>低密度 (<100台)</span>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- 模拟数据点 -->\n                        <div class=\"absolute top-1/4 left-1/3 w-4 h-4 bg-red-500 rounded-full animate-pulse\"></div>\n                        <div class=\"absolute top-1/2 left-1/2 w-3 h-3 bg-yellow-500 rounded-full animate-pulse\"></div>\n                        <div class=\"absolute top-3/4 right-1/3 w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                    </div>\n                </div>\n                <!-- 区域统计 -->\n                <div class=\"space-y-4\">\n                    <h4 class=\"text-md font-medium text-gray-900\">各区域设备数量</h4>\n                    <div class=\"space-y-3\">\n                        <div class=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                            <div>\n                                <p class=\"text-sm font-medium text-gray-900\">高新技术开发区</p>\n                                <p class=\"text-xs text-gray-500\">23个单位</p>\n                            </div>\n                            <div class=\"text-right\">\n                                <p class=\"text-sm font-semibold text-gray-900\">3,245台</p>\n                                <p class=\"text-xs text-gray-500\">25.3%</p>\n                            </div>\n                        </div>\n                        <div class=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                            <div>\n                                <p class=\"text-sm font-medium text-gray-900\">经济技术开发区</p>\n                                <p class=\"text-xs text-gray-500\">18个单位</p>\n                            </div>\n                            <div class=\"text-right\">\n                                <p class=\"text-sm font-semibold text-gray-900\">2,876台</p>\n                                <p class=\"text-xs text-gray-500\">22.4%</p>\n                            </div>\n                        </div>\n                        <div class=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                            <div>\n                                <p class=\"text-sm font-medium text-gray-900\">市中心区</p>\n                                <p class=\"text-xs text-gray-500\">31个单位</p>\n                            </div>\n                            <div class=\"text-right\">\n                                <p class=\"text-sm font-semibold text-gray-900\">2,134台</p>\n                                <p class=\"text-xs text-gray-500\">16.6%</p>\n                            </div>\n                        </div>\n                        <div class=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                            <div>\n                                <p class=\"text-sm font-medium text-gray-900\">其他区域</p>\n                                <p class=\"text-xs text-gray-500\">45个单位</p>\n                            </div>\n                            <div class=\"text-right\">\n                                <p class=\"text-sm font-semibold text-gray-900\">4,592台</p>\n                                <p class=\"text-xs text-gray-500\">35.7%</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 综合分析说明区 -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">综合分析说明</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div class=\"bg-blue-50 rounded-lg p-4\">\n                    <h4 class=\"text-md font-medium text-blue-900 mb-2\">统计逻辑说明</h4>\n                    <p class=\"text-sm text-blue-700\">数据统计基于全市科研院所、高等院校、企业研发中心等单位上报的仪器设备信息，按照国家标准分类体系进行归类统计。</p>\n                </div>\n                <div class=\"bg-green-50 rounded-lg p-4\">\n                    <h4 class=\"text-md font-medium text-green-900 mb-2\">数据更新周期</h4>\n                    <p class=\"text-sm text-green-700\">系统每日凌晨2点自动更新数据，设备状态信息实时同步。最后更新时间：2024年1月15日 02:00。</p>\n                </div>\n                <div class=\"bg-yellow-50 rounded-lg p-4\">\n                    <h4 class=\"text-md font-medium text-yellow-900 mb-2\">数据来源</h4>\n                    <p class=\"text-sm text-yellow-700\">数据来源包括科技管理系统、设备管理平台、第三方数据接口等多个渠道，确保数据的完整性和准确性。</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 数据明细联动区 -->\n        <div class=\"bg-white rounded-lg shadow-md\">\n            <div class=\"px-6 py-4 border-b border-gray-200\">\n                <div class=\"flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">设备明细列表</h3>\n                    <div class=\"flex space-x-2\">\n                        <input type=\"text\" placeholder=\"搜索设备名称或编号\" class=\"px-3 py-1 text-sm border border-gray-300 rounded-md\">\n                        <select class=\"px-3 py-1 text-sm border border-gray-300 rounded-md\">\n                            <option>全部分类</option>\n                            <option>光谱仪器</option>\n                            <option>色谱仪器</option>\n                            <option>质谱仪器</option>\n                        </select>\n                        <select class=\"px-3 py-1 text-sm border border-gray-300 rounded-md\">\n                            <option>全部状态</option>\n                            <option>正常使用</option>\n                            <option>维修中</option>\n                            <option>闲置</option>\n                        </select>\n                    </div>\n                </div>\n            </div>\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">设备名称</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">设备编号</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分类</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">归属单位</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">所在区域</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">当前状态</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">高分辨率质谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">MS-2024-001</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">质谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市科学院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高新区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500\">正常使用</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900\">\n                                <button class=\"hover:underline\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">液相色谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">LC-2024-002</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">色谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市第一医院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市中心区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500\">维修中</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900\">\n                                <button class=\"hover:underline\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">原子吸收光谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">AAS-2024-003</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">光谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市环保局</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">经开区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500\">正常使用</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900\">\n                                <button class=\"hover:underline\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">电化学工作站</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">EC-2024-004</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">电化学仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市大学</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高新区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500\">闲置</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900\">\n                                <button class=\"hover:underline\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">红外光谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">IR-2024-005</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">光谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市检测中心</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市中心区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500\">正常使用</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900\">\n                                <button class=\"hover:underline\">查看详情</button>\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-500\">\n                        显示第 1-5 条，共 12,847 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 text-sm bg-blue-600 text-white rounded-md\">1</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 分类统计图表\n        const categoryCtx = document.getElementById('categoryChart').getContext('2d');\n        let categoryChart = new Chart(categoryCtx, {\n            type: 'bar',\n            data: {\n                labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],\n                datasets: [{\n                    label: '设备数量',\n                    data: [3245, 2876, 1987, 1654, 1432, 1653],\n                    backgroundColor: [\n                        'rgba(59, 130, 246, 0.8)',\n                        'rgba(16, 185, 129, 0.8)',\n                        'rgba(245, 158, 11, 0.8)',\n                        'rgba(239, 68, 68, 0.8)',\n                        'rgba(139, 92, 246, 0.8)',\n                        'rgba(107, 114, 128, 0.8)'\n                    ],\n                    borderColor: [\n                        'rgba(59, 130, 246, 1)',\n                        'rgba(16, 185, 129, 1)',\n                        'rgba(245, 158, 11, 1)',\n                        'rgba(239, 68, 68, 1)',\n                        'rgba(139, 92, 246, 1)',\n                        'rgba(107, 114, 128, 1)'\n                    ],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                    legend: {\n                        display: false\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                }\n            }\n        });\n\n        // 使用状态图表\n        const statusCtx = document.getElementById('statusChart').getContext('2d');\n        const statusChart = new Chart(statusCtx, {\n            type: 'doughnut',\n            data: {\n                labels: ['正常使用', '维修中', '闲置'],\n                datasets: [{\n                    data: [10234, 1523, 1090],\n                    backgroundColor: [\n                        'rgba(16, 185, 129, 0.8)',\n                        'rgba(245, 158, 11, 0.8)',\n                        'rgba(239, 68, 68, 0.8)'\n                    ],\n                    borderColor: [\n                        'rgba(16, 185, 129, 1)',\n                        'rgba(245, 158, 11, 1)',\n                        'rgba(239, 68, 68, 1)'\n                    ],\n                    borderWidth: 2\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                    legend: {\n                        display: false\n                    }\n                }\n            }\n        });\n\n        // 切换分类图表类型\n        function switchCategoryChart(type) {\n            categoryChart.destroy();\n            \n            const config = {\n                type: type,\n                data: {\n                    labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],\n                    datasets: [{\n                        label: '设备数量',\n                        data: [3245, 2876, 1987, 1654, 1432, 1653],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(239, 68, 68, 0.8)',\n                            'rgba(139, 92, 246, 0.8)',\n                            'rgba(107, 114, 128, 0.8)'\n                        ],\n                        borderColor: [\n                            'rgba(59, 130, 246, 1)',\n                            'rgba(16, 185, 129, 1)',\n                            'rgba(245, 158, 11, 1)',\n                            'rgba(239, 68, 68, 1)',\n                            'rgba(139, 92, 246, 1)',\n                            'rgba(107, 114, 128, 1)'\n                        ],\n                        borderWidth: type === 'pie' ? 2 : 1\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: type === 'pie'\n                        }\n                    },\n                    scales: type === 'bar' ? {\n                        y: {\n                            beginAtZero: true\n                        }\n                    } : {}\n                }\n            };\n            \n            categoryChart = new Chart(categoryCtx, config);\n            \n            // 更新按钮状态\n            document.querySelectorAll('#categoryChartType, [onclick*=\"switchCategoryChart\"]').forEach(btn => {\n                btn.className = 'px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200';\n            });\n            \n            if (type === 'bar') {\n                document.getElementById('categoryChartType').className = 'px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200';\n                document.getElementById('categoryChartType').textContent = '柱状图';\n            } else {\n                event.target.className = 'px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200';\n            }\n        }\n\n        // 设置默认柱状图按钮状态\n        document.getElementById('categoryChartType').onclick = () => switchCategoryChart('bar');\n    </script>\n</body>\n</html>]`\n    *   `[<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>仪器信息</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <!-- 页面标题 -->\n    <div class=\"bg-white shadow-sm border-b\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div class=\"flex justify-between items-center py-6\">\n                <div>\n                    <h1 class=\"text-3xl font-bold text-gray-900\">仪器信息</h1>\n                    <p class=\"mt-2 text-sm text-gray-600\">为科研管理与创新主体用户提供全方位、结构化的仪器设备信息展示和检索服务</p>\n                </div>\n                <div class=\"flex space-x-3\">\n                    <button class=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                        </svg>\n                        导出数据\n                    </button>\n                    <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                        </svg>\n                        刷新数据\n                    </button>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 主内容区域 -->\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <!-- 数据总览区 -->\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8\">\n            <!-- 设备类型分布 -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">设备类型分布</h3>\n                <div class=\"h-64\">\n                    <canvas id=\"typeChart\"></canvas>\n                </div>\n            </div>\n            \n            <!-- 领域分布 -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">领域分布</h3>\n                <div class=\"h-64\">\n                    <canvas id=\"fieldChart\"></canvas>\n                </div>\n            </div>\n            \n            <!-- 区域分布 -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">区域分布</h3>\n                <div class=\"h-64\">\n                    <canvas id=\"regionChart\"></canvas>\n                </div>\n            </div>\n        </div>\n\n        <!-- 设备原值分布 -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">设备原值分布</h3>\n            <div class=\"h-80\">\n                <canvas id=\"valueChart\"></canvas>\n            </div>\n        </div>\n\n        <!-- 查询与筛选区 -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">查询与筛选</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\">\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">仪器设备名称</label>\n                    <input type=\"text\" placeholder=\"请输入设备名称\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">分类</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                        <option>全部分类</option>\n                        <option>光谱仪器</option>\n                        <option>色谱仪器</option>\n                        <option>质谱仪器</option>\n                        <option>电化学仪器</option>\n                    </select>\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">生产制造商</label>\n                    <input type=\"text\" placeholder=\"请输入制造商\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">规格型号</label>\n                    <input type=\"text\" placeholder=\"请输入规格型号\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n            </div>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\">\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">所属单位</label>\n                    <input type=\"text\" placeholder=\"请输入所属单位\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">启用时间（开始）</label>\n                    <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">启用时间（结束）</label>\n                    <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">共享状态</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                        <option>全部状态</option>\n                        <option>对外共享</option>\n                        <option>内部使用</option>\n                        <option>暂停共享</option>\n                    </select>\n                </div>\n            </div>\n            <div class=\"flex space-x-3\">\n                <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\">\n                    <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n                    </svg>\n                    查询\n                </button>\n                <button class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                    <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                    </svg>\n                    重置筛选\n                </button>\n            </div>\n        </div>\n\n        <!-- 仪器设备列表区 -->\n        <div class=\"bg-white rounded-lg shadow-md\">\n            <div class=\"px-6 py-4 border-b border-gray-200\">\n                <div class=\"flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">仪器设备列表</h3>\n                    <div class=\"text-sm text-gray-500\">\n                        共找到 <span class=\"font-medium text-gray-900\">2,847</span> 台设备\n                    </div>\n                </div>\n            </div>\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">仪器名称</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">原值（万元）</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">制造商</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">规格型号</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">产地国别</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分类</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">所在地</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">启用时间</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">所属单位</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">共享状态</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">高分辨率质谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">285.6</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">赛默飞世尔</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">Q Exactive HF-X</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">美国</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">质谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高新区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-03-15</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市科学院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">对外共享</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm space-x-2\">\n                                <button onclick=\"showDetail('1')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">液相色谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">156.8</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">安捷伦</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">1290 Infinity II</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">美国</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">色谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市中心区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2022-11-20</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市第一医院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800\">内部使用</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm space-x-2\">\n                                <button onclick=\"showDetail('2')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">原子吸收光谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">89.5</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">岛津</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">AA-7000</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">日本</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">光谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">经开区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-07-08</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市环保局</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">对外共享</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm space-x-2\">\n                                <button onclick=\"showDetail('3')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">电化学工作站</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">45.2</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">普林斯顿</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">VersaSTAT 4</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">美国</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">电化学仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高新区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-01-12</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市大学</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">暂停共享</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm space-x-2\">\n                                <button onclick=\"showDetail('4')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">红外光谱仪</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">67.3</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">布鲁克</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">VERTEX 80v</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">德国</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">光谱仪器</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市中心区</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2022-09-25</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市检测中心</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">对外共享</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm space-x-2\">\n                                <button onclick=\"showDetail('5')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-500\">\n                        显示第 1-5 条，共 2,847 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 text-sm bg-blue-600 text-white rounded-md\">1</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 仪器详情弹窗 -->\n    <div id=\"detailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen px-4\">\n            <div class=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n                <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">仪器详情</h3>\n                    <button onclick=\"hideDetail()\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"p-6\">\n                    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                        <!-- 基础信息 -->\n                        <div class=\"lg:col-span-2\">\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">基础信息</h4>\n                                <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                                    <div>\n                                        <span class=\"text-gray-500\">仪器名称：</span>\n                                        <span class=\"font-medium text-gray-900\" id=\"detail-name\">高分辨率质谱仪</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">设备编号：</span>\n                                        <span class=\"font-medium text-gray-900\">MS-2024-001</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">原值：</span>\n                                        <span class=\"font-medium text-gray-900\">285.6万元</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">制造商：</span>\n                                        <span class=\"font-medium text-gray-900\">赛默飞世尔</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">规格型号：</span>\n                                        <span class=\"font-medium text-gray-900\">Q Exactive HF-X</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">产地国别：</span>\n                                        <span class=\"font-medium text-gray-900\">美国</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">分类：</span>\n                                        <span class=\"font-medium text-gray-900\">质谱仪器</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">启用时间：</span>\n                                        <span class=\"font-medium text-gray-900\">2023-03-15</span>\n                                    </div>\n                                </div>\n                            </div>\n                            \n                            <!-- 功能和技术指标 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">功能和技术指标</h4>\n                                <div class=\"text-sm text-gray-700 space-y-2\">\n                                    <p><span class=\"font-medium\">分辨率：</span>240,000 FWHM (m/z 200)</p>\n                                    <p><span class=\"font-medium\">质量范围：</span>50-6,000 m/z</p>\n                                    <p><span class=\"font-medium\">质量精度：</span>&lt;1 ppm RMS</p>\n                                    <p><span class=\"font-medium\">扫描速度：</span>最高18 Hz</p>\n                                    <p><span class=\"font-medium\">离子源：</span>HESI-II、APCI、ESI</p>\n                                </div>\n                            </div>\n                            \n                            <!-- 主要学科领域 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">主要学科领域</h4>\n                                <div class=\"flex flex-wrap gap-2\">\n                                    <span class=\"px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">生物医学</span>\n                                    <span class=\"px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">药物分析</span>\n                                    <span class=\"px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">环境科学</span>\n                                    <span class=\"px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">食品安全</span>\n                                    <span class=\"px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">代谢组学</span>\n                                </div>\n                            </div>\n                            \n                            <!-- 服务内容 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">服务内容</h4>\n                                <div class=\"text-sm text-gray-700 space-y-2\">\n                                    <p>• 小分子化合物定性定量分析</p>\n                                    <p>• 蛋白质组学分析</p>\n                                    <p>• 代谢组学研究</p>\n                                    <p>• 药物代谢产物分析</p>\n                                    <p>• 环境污染物检测</p>\n                                    <p>• 食品添加剂及农药残留检测</p>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <!-- 右侧信息 -->\n                        <div>\n                            <!-- 仪器图片 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">仪器图片</h4>\n                                <div class=\"space-y-4\">\n                                    <img src=\"https://source.unsplash.com/400x300?laboratory,equipment\" alt=\"仪器图片\" class=\"w-full rounded-lg\">\n                                    <div class=\"grid grid-cols-2 gap-2\">\n                                        <img src=\"https://source.unsplash.com/200x150?scientific,instrument\" alt=\"仪器图片\" class=\"w-full rounded-lg\">\n                                        <img src=\"https://source.unsplash.com/200x150?mass,spectrometer\" alt=\"仪器图片\" class=\"w-full rounded-lg\">\n                                    </div>\n                                    <button class=\"w-full px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">\n                                        查看更多图片\n                                    </button>\n                                </div>\n                            </div>\n                            \n                            <!-- 使用状态与预约管理 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">使用状态与预约管理</h4>\n                                <div class=\"text-sm space-y-3\">\n                                    <div>\n                                        <span class=\"text-gray-500\">当前状态：</span>\n                                        <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">空闲可用</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">当前使用人：</span>\n                                        <span class=\"font-medium text-gray-900\">无</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">下次预约：</span>\n                                        <span class=\"font-medium text-gray-900\">2024-01-20 09:00</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">预约用户：</span>\n                                        <span class=\"font-medium text-gray-900\">张研究员</span>\n                                    </div>\n                                    <button class=\"w-full mt-3 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700\">\n                                        查看预约日历\n                                    </button>\n                                </div>\n                            </div>\n                            \n                            <!-- 仪器提供方信息 -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">仪器提供方信息</h4>\n                                <div class=\"text-sm space-y-3\">\n                                    <div>\n                                        <span class=\"text-gray-500\">所属单位：</span>\n                                        <span class=\"font-medium text-gray-900\">市科学院</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">所在地：</span>\n                                        <span class=\"font-medium text-gray-900\">高新区科技大道123号</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">联系人：</span>\n                                        <span class=\"font-medium text-gray-900\">李主任</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">联系电话：</span>\n                                        <span class=\"font-medium text-gray-900\">0571-88888888</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">共享服务范围：</span>\n                                        <span class=\"font-medium text-gray-900\">全市范围</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">对外可见：</span>\n                                        <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">是</span>\n                                    </div>\n                                    <button class=\"w-full mt-3 px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50\">\n                                        查看单位详情\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 图表初始化\n        document.addEventListener('DOMContentLoaded', function() {\n            // 设备类型分布图表\n            const typeCtx = document.getElementById('typeChart').getContext('2d');\n            new Chart(typeCtx, {\n                type: 'doughnut',\n                data: {\n                    labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],\n                    datasets: [{\n                        data: [856, 642, 523, 387, 298, 141],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(239, 68, 68, 0.8)',\n                            'rgba(139, 92, 246, 0.8)',\n                            'rgba(107, 114, 128, 0.8)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            position: 'bottom'\n                        }\n                    }\n                }\n            });\n\n            // 领域分布图表\n            const fieldCtx = document.getElementById('fieldChart').getContext('2d');\n            new Chart(fieldCtx, {\n                type: 'bar',\n                data: {\n                    labels: ['生物医学', '材料科学', '环境科学', '化学分析', '物理研究'],\n                    datasets: [{\n                        label: '设备数量',\n                        data: [1245, 987, 756, 623, 236],\n                        backgroundColor: 'rgba(59, 130, 246, 0.8)'\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n\n            // 区域分布图表\n            const regionCtx = document.getElementById('regionChart').getContext('2d');\n            new Chart(regionCtx, {\n                type: 'pie',\n                data: {\n                    labels: ['高新区', '市中心区', '经开区', '其他区域'],\n                    datasets: [{\n                        data: [1156, 823, 567, 301],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(107, 114, 128, 0.8)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            position: 'bottom'\n                        }\n                    }\n                }\n            });\n\n            // 设备原值分布图表\n            const valueCtx = document.getElementById('valueChart').getContext('2d');\n            new Chart(valueCtx, {\n                type: 'bar',\n                data: {\n                    labels: ['10万以下', '10-50万', '50-100万', '100-200万', '200-500万', '500万以上'],\n                    datasets: [{\n                        label: '设备数量',\n                        data: [1245, 856, 423, 234, 67, 22],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(239, 68, 68, 0.8)',\n                            'rgba(139, 92, 246, 0.8)',\n                            'rgba(107, 114, 128, 0.8)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n        });\n\n        // 显示详情弹窗\n        function showDetail(id) {\n            document.getElementById('detailModal').classList.remove('hidden');\n            document.body.style.overflow = 'hidden';\n        }\n\n        // 隐藏详情弹窗\n        function hideDetail() {\n            document.getElementById('detailModal').classList.add('hidden');\n            document.body.style.overflow = 'auto';\n        }\n\n        // 点击弹窗外部关闭\n        document.getElementById('detailModal').addEventListener('click', function(e) {\n            if (e.target === this) {\n                hideDetail();\n            }\n        });\n    </script>\n</body>\n</html>]`\n\n#### **C. 核心交互组件：JS实现黄金标准 (必须严格遵循此结构)**\n* **说明:** 为保证所有弹窗功能正常，同时图表又能美观地呈现，**必须**使用下面的JavaScript代码结构。这个结构是修复弹窗无法打开问题的关键。\n* **JS代码结构模板:**\n    ```html\n    <script>\n        // --- 1. 全局UI控制函数 ---\n        // 用于被 HTML 中的 onclick 调用，必须定义在全局作用域。\n        function openModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden'; // 防止背景滚动\n            }\n        }\n\n        function closeModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto'; // 恢复背景滚动\n            }\n        }\n\n        // --- 2. DOM加载完毕后执行的初始化代码 ---\n        // 确保所有HTML元素都已加载，再进行事件绑定和图表绘制。\n        document.addEventListener('DOMContentLoaded', function() {\n\n            // ========= 初始化所有图表 =========\n            // 示例：初始化ID为 'revenueChart' 的图表\n            const revenueCtx = document.getElementById('revenueChart');\n            if (revenueCtx) {\n                const ctx = revenueCtx.getContext('2d');\n                const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.clientHeight);\n                gradient.addColorStop(0, 'rgba(59, 130, 246, 0.5)');\n                gradient.addColorStop(1, 'rgba(59, 130, 246, 0)');\n\n                new Chart(ctx, {\n                    type: 'line',\n                    data: {\n                        labels: ['一月', '二月', '三月', '四月', '五月', '六月'],\n                        datasets: [{\n                            label: '示例数据',\n                            data: [12, 19, 3, 5, 2, 3],\n                            borderColor: '#3B82F6',\n                            tension: 0.4,\n                            fill: true,\n                            backgroundColor: gradient\n                        }]\n                    },\n                    options: {\n                        responsive: true, maintainAspectRatio: false,\n                        plugins: { legend: { labels: { color: '#9CA3AF' } } },\n                        scales: { x: { ticks: { color: '#9CA3AF' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } }, y: { ticks: { color: '#9CA3AF' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } } }\n                    }\n                });\n            }\n            // ... 可在此处按需初始化更多图表 (确保每个canvas有唯一ID) ...\n\n\n            // ========= 为所有弹窗绑定\"点击外部关闭\"事件 =========\n            // 给所有弹窗的根元素添加 'modal-overlay' 类，即可自动生效。\n            const modals = document.querySelectorAll('.modal-overlay');\n            modals.forEach(modal => {\n                modal.addEventListener('click', function(event) {\n                    // 确保点击的是蒙层本身，而不是弹窗内容\n                    if (event.target === modal) {\n                        closeModal(modal.id);\n                    }\n                });\n\n                // 同时绑定\"ESC键关闭\"\n                document.addEventListener('keydown', function (e) {\n                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {\n                        closeModal(modal.id);\n                    }\n                });\n            });\n            \n            // ========= 表单提交处理（用于原型） =========\n            // 找到所有弹窗内的表单\n            document.querySelectorAll('.modal-overlay form').forEach(form => {\n                form.addEventListener('submit', function(e) {\n                    e.preventDefault(); // 阻止页面刷新\n                    const parentModalId = form.closest('.modal-overlay').id;\n                    alert('表单已提交 (原型演示)');\n                    closeModal(parentModalId);\n                });\n            });\n        });\n    </script>\n    ```\n\n### **三、 通用技术与输出规范 (General Tech & Output Specs)**\n* **最终产物:** **一个独立的、完整的HTML文件。**\n* **JavaScript规范:** **必须**遵循上文`核心交互组件：JS实现黄金标准`中定义的结构。所有`<script>`标签应放置在`</body>`标签之前。如果包含图表，**必须**在`<head>`中引入 Chart.js 的 CDN: `<script src=\"https://cdn.jsdelivr.net/npm/chart.js\" defer></script>`。\n* **文件命名:** 基于 `[功能名]` 翻译为英文kebab-case。\n* **样式:** **绝对禁止** `<style>` 或外部CSS。所有样式**必须**通过 Tailwind CSS 类名实现。\n* **图标:** **必须**使用 Heroicons (`https://heroicons.com/`) 的SVG代码直接嵌入HTML。\n* **图片占位符:** **禁止**外部链接。使用自包含的SVG占位符。\n* **语言:** 所有文本内容、注释**必须使用简体中文**。\n* **模拟数据:** 所有占位数据**必须具有\"宁波市\"的地域特色**（如`宁波市XX科技`）。\n### **四、原子化锚点规划指南 (Atomic Anchor Planning Guide)**\n\n#### **锚点设计原则:**\n1.  **`SLOT` (结构层 - \"大盒子\")**: 将每个独立的、有标题的功能卡片、区域、**弹窗**视为一个 `SLOT`。它是一个完整的功能容器。\n2.  **`CHUNK` (原子层 - \"可替换零件\")**: 在 `SLOT` 内部，**秉持\"万物皆可CHUNK\"的原则**。将任何一个未来可能需要单独修改的最小信息单元（一个数据项、一个标签、一个按钮）都用`CHUNK`包裹起来。**这是实现终极灵活性的关键。**\n3.  **【新增】语义化命名规则**: 所有锚点名称必须具有高可读性和描述性，这是后续自然语言映射的基础。具体要求：\n    -   **SLOT**: 使用kebab-case（短横线连接），如 `header`、`filters`、`company-overview`、`rd-personnel`、`shareholder-modal`\n    -   **CHUNK**: 采用 **\"父级SLOT名-具体内容\"** 的格式，如 `overview-reg-capital`、`rd-personnel-table`、`header-title`、`filters-date-range`\n    -   **禁用无意义命名**: 避免使用 `chunk-1`、`item-a`、`section-x` 等无描述性名称\n    -   **优先功能描述**: 根据实际功能命名，如表格用 `table`、统计用 `stats`、按钮用 `button`、数据项用具体字段名\n\n#### **规划示例 - 以\"企业概况\"模块为例（重要）**\n\n- **PLAN 阶段的思考过程（原子化思维）:**\n    1.  \"企业概况\"卡片是一个独立功能区，适合用 `SLOT` 包裹，命名为 `company-overview`。\n    2.  卡片内的 Logo 和公司名，是独立的视觉元素，应该被包裹。命名 `CHUNK::overview-logo-and-name`。\n    3.  下面的每一个键值对信息项（企业类型、注册时间、注册资本等），都可能被单独更新。**必须为每一个键值对包裹一个`CHUNK`**。命名 `CHUNK::overview-reg-type`, `CHUNK::overview-reg-time`, `CHUNK::overview-reg-capital` 等。\n    4.  企业标签列表，整个列表可以是一个 `CHUNK`，或者更精细地，每个标签都是一个`CHUNK`。我们选择包裹整个列表：`CHUNK::overview-tags`。\n    5.  \"下载营业执照\"按钮，也可能需要修改文字或链接，它也应该是一个`CHUNK`。命名 `CHUNK::overview-download-button`。\n\n- **最终代码结构（你必须严格遵循此范例的精细度）:**\n```html\n<!-- SLOT::company-overview::BEGIN -->\n<div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">企业概况</h2>\n    <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div>\n            <!-- CHUNK::overview-logo-and-name::BEGIN -->\n            <div class=\"flex items-center space-x-4\">\n                <img ... alt=\"企业logo\">\n                <div>\n                    <h3 class=\"text-lg font-medium text-gray-900\">宁波市XX科技有限公司</h3>\n                    <p class=\"text-sm text-gray-500\">统一社会信用代码: 91330201MA2XXXXXX</p>\n                </div>\n            </div>\n            <!-- CHUNK::overview-logo-and-name::END -->\n            <div class=\"mt-4 space-y-2\">\n                <!-- CHUNK::overview-reg-type::BEGIN -->\n                <div class=\"flex\">\n                    <span class=\"text-sm text-gray-500 w-24\">企业类型:</span>\n                    <span class=\"text-sm text-gray-900\">有限责任公司</span>\n                </div>\n                <!-- CHUNK::overview-reg-type::END -->\n                <!-- CHUNK::overview-reg-time::BEGIN -->\n                <div class=\"flex\">\n                    <span class=\"text-sm text-gray-500 w-24\">注册时间:</span>\n                    <span class=\"text-sm text-gray-900\">2018-05-15</span>\n                </div>\n                <!-- CHUNK::overview-reg-time::END -->\n                <!-- CHUNK::overview-reg-capital::BEGIN -->\n                <div class=\"flex\">\n                    <span class=\"text-sm text-gray-500 w-24\">注册资本:</span>\n                    <span class=\"text-sm text-gray-900\">1000万元</span>\n                </div>\n                <!-- CHUNK::overview-reg-capital::END -->\n            </div>\n        </div>\n        <!-- ... 其他列也应遵循此原则进行原子化 CHUNK 标记 ... -->\n    </div>\n</div>\n<!-- SLOT::company-overview::END -->\n```\n通过这种方式，你可以发出这样的指令：\"请将`CHUNK::overview-reg-capital`中的文本'1000万元'修改为'2000万元人民币'\" ，从而实现前所未有的修改精度。\n\n#### **弹窗锚点规则**\n所有弹窗（Modal）本身也应被一个`SLOT`包裹，例如 `<!-- SLOT::shareholder-detail-modal::BEGIN -->`。弹窗内部的各个信息区域和数据行，也应遵循上述原则，使用`CHUNK`进行标记。\n\n---\n\n### **--- 本次开发任务 ---**\n\n**一、功能名:**\n`研发人员详情展示`\n\n**二、功能设计需求:**\n`**一、功能说明**\n\n研发人员详情展示功能面向科技创新与管理业务需求，全面聚合并直观展现单个人才的全生命周期信息，涵盖基础身份、成果产出、奖励荣誉、教育与工作经历、称号认定、科研项目、合作网络及政策享受等多维内容。通过结构化信息与可视化组件有机结合，系统支持对研发人员个人实力、科研能力、合作生态及政策享受情况的全景洞察，助力管理决策、人才评价、科技服务及资源精准对接。\n\n**二、界面结构与要素说明**\n\n基础信息区位于详情页首屏，集中展示研发人员的姓名、性别、年龄、出生日期、学历、学位、毕业院校、工作单位、技术职称及技术领域等核心身份与专业信息，并配有人才头像、联系方式等可选字段，整体布局简洁突出关键信息。\n\n成果信息区以分组列表或标签卡片方式呈现该研发人员作为发明人申请的全部专利、已发表的主要论文及其他科技成果，支持按类型、时间、影响力等多维筛选与快速检索，条目内可展示成果名称、类别、发表或授权时间、合作单位、成果状态等详细信息。\n\n奖励信息区通过时间轴或列表形式罗列人才获得的各类科技奖励，区分国、省、市、区级层次，展示奖励名称、获奖年份、授奖单位、奖励类别及证书下载等要素，支持按级别筛选和排序。\n\n教育与工作经历区以纵向分栏展示研发人员完整的教育背景和职业履历，覆盖毕业院校、学位、学习起止时间、任职单位、岗位职务、任职起止时间等信息，支持展开/收起切换与时序排序，便于追溯人才成长轨迹。\n\n称号认定信息区专列人才已获得的各类称号认定，列明认定名称、认定级别、认定年度、认定机构等信息，支持批量查看及证书下载。\n\n参与/负责项目区展示研发人员作为项目负责人或参与者的科研项目清单，以表格形式列示项目名称、项目类型、项目起止时间、承担单位、项目角色、项目状态等，支持多条件筛选与导出。\n\n合作关系网络区集成可交互的合作网络图，直观展现研发人员与其他人员、单位的合作关联，支持节点点击查看合作者详情及合作项目，辅助梳理人才学术和产业网络。\n\n政策享受信息区以列表或卡片方式展示研发人员享受的人才政策、补助类型、资助金额、受理年度及办理状态，支持补助明细展开、证书下载等功能。\n\n详情页整体支持按板块折叠/展开、内容检索、导出个人简历及报告等操作，提升查阅效率和个性化管理能力。\n\n**三、业务流程**\n\n系统按接口实时或周期性同步各类数据源，归集研发人员的基础身份、成果、奖励、教育、工作、称号、项目、合作及政策等全量信息，自动建立完整的人才档案。\n\n用户在研发人员总览、人才清册或各类关联业务模块中点击某研发人员条目，系统调取并渲染其全量详情页面，依次加载所有分区数据与可视化组件。\n\n用户可在详情页内切换浏览各板块信息，对成果、奖励、项目等列表内容进行筛选、排序与导出操作，或点击合作网络节点追溯相关合作者与项目明细。\n\n系统支持在每个信息区根据业务场景动态刷新相关数据，并记录用户的主要浏览与操作行为，便于后续数据分析与服务优化。\n\n用户可一键导出研发人员完整个人简历、成果报告或定制化信息快照，用于申报、评价、推荐或对接等多场景应用，系统同步记录导出历史。\n\n系统对人才详情的所有核心变更与数据来源做溯源和版本归档，保障数据准确可追溯，并支撑后续审计和管理。\n\n**四、操作流程**\n\n用户在研发人员清册或相关业务页面点击指定人才的“详情”按钮，系统弹出或跳转至研发人员详情展示页面。\n\n在基础信息区，用户浏览人才基本身份与专业信息，如需查阅更多，可点击头像或展开附加联系方式等字段。\n\n用户下拉页面，分别浏览科技成果、奖励、教育经历、工作履历、称号认定、科研项目、合作网络和政策享受等各信息区内容。\n\n在成果、奖励、项目等分区，用户可按类型、时间、影响力等维度筛选和排序条目，必要时可一键导出对应内容。\n\n用户在合作关系网络图中可自由拖拽、缩放视图，并点击节点查看合作者详情或合作项目清单，实现合作生态的可视化洞察。\n\n用户在政策享受信息区可展开补助明细，下载证书或查阅办理状态，必要时可导出全部政策享受信息。\n\n全流程支持随时折叠/展开各板块，快速检索目标内容，最终可整体导出个人简历或定制化报告，系统自动记录相关操作日志。`\n\n**三、核心要素拆解 (Key Elements Breakdown):**\n根据功能需求自动分析核心要素，并为每个要素规划对应的`SLOT`和**原子化的`CHUNK`**锚点。\n\n---\n\n### **最终指令 (Final Command)**\n\n**在你开始编码之前，请在脑海中快速回顾并确认以下清单：**\n1.  我已经仔细分析了任务需求，并明确了它更偏向\"管理后台\"还是\"数据大屏\"。\n2.  我将严格遵循所选风格对应的**设计心法**和**权威代码范例**。\n3.  我将**绝对严格**地遵循\"JS实现黄金标准\"来处理所有弹窗和图表。\n4.  我清楚所有技术规范，特别是**图标、模拟数据和JS脚本的实现规则**。\n5.  我已阅读并理解了本次任务的核心要素，确保不会遗漏任何功能。\n6.  **我将为每个功能模块添加`SLOT`，并为其内部几乎所有独立的信息展示单元（包括单个数值、文本、按钮）添加`CHUNK`锚点，以实现最大化的可修改性。**\n\n确认完毕。现在，请激活你最合适的专家角色，基于以上所有信息，为本次指定的开发任务生成对应的HTML原型页面代码。\n\n**直接开始设计并输出最终的HTML文件代码。不要在代码前添加任何解释性文字或总结。你的输出必须直接以 `<!DOCTYPE html>` 开始。**"}], "temperature": 0.6, "max_tokens": 16384, "top_p": 0.95}, "prompt_length": 160855, "system_role_length": 52}