<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技项目管理流程</text>

  <!-- 阶段一：项目入口 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：项目入口</text>
  
  <!-- 入口节点 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目入口选择</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">查询/录入/导入</text>
  </g>

  <!-- 阶段二：核心操作 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：核心操作</text>

  <!-- 查询操作 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="90" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选条件</text>
    <text x="100" y="80" text-anchor="middle" font-size="12" fill="#555">展示匹配数据</text>
  </g>

  <!-- 录入操作 -->
  <g transform="translate(500, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="90" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目录入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写项目信息</text>
    <text x="100" y="80" text-anchor="middle" font-size="12" fill="#555">上传相关资料</text>
  </g>

  <!-- 导入操作 -->
  <g transform="translate(800, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="90" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">下载模板</text>
    <text x="100" y="80" text-anchor="middle" font-size="12" fill="#555">上传校验</text>
  </g>

  <!-- 连接线 入口 -> 操作 -->
  <path d="M 600 200 Q 600 250 600 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：项目详情 -->
  <text x="600" y="450" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：项目详情</text>

  <!-- 详情查看 -->
  <g transform="translate(350, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">关键信息/成果/附件</text>
  </g>

  <!-- 变更记录 -->
  <g transform="translate(650, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">历史日志/变更明细</text>
  </g>

  <!-- 连接线 操作 -> 详情 -->
  <path d="M 300 390 C 300 430, 350 470, 350 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 390 C 500 430, 450 470, 450 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 390 C 700 430, 650 470, 650 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：批量操作 -->
  <text x="600" y="630" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：批量操作</text>

  <!-- 批量操作 -->
  <g transform="translate(350, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成导出文件</text>
  </g>

  <!-- 批量删除 -->
  <g transform="translate(650, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量删除</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">删除选中项目</text>
  </g>

  <!-- 连接线 详情 -> 批量 -->
  <path d="M 350 570 C 350 620, 350 670, 350 680" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 570 C 650 620, 650 670, 650 680" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：成果关联 -->
  <text x="600" y="800" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：成果关联</text>

  <!-- 成果关联 -->
  <g transform="translate(500, 850)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果关联</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">项目与成果灵活关联</text>
  </g>

  <!-- 连接线 批量 -> 成果 -->
  <path d="M 450 750 C 450 800, 500 830, 500 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 750 C 750 800, 700 830, 700 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>