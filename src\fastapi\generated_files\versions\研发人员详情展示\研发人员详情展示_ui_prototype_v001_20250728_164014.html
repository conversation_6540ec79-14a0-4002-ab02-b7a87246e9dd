<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员详情展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- SLOT::header::BEGIN -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">研发人员详情</h1>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出简历
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        编辑信息
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- SLOT::header::END -->

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- SLOT::basic-info::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row">
                <!-- CHUNK::basic-info-avatar::BEGIN -->
                <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-6">
                    <img src="https://source.unsplash.com/150x150/?portrait" alt="头像" class="w-32 h-32 rounded-full object-cover border-4 border-blue-100">
                </div>
                <!-- CHUNK::basic-info-avatar::END -->

                <div class="flex-1">
                    <!-- CHUNK::basic-info-name::BEGIN -->
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">张明远</h2>
                    <!-- CHUNK::basic-info-name::END -->

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- CHUNK::basic-info-gender::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">性别：</span>
                            <span class="text-sm font-medium text-gray-900">男</span>
                        </div>
                        <!-- CHUNK::basic-info-gender::END -->

                        <!-- CHUNK::basic-info-age::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">年龄：</span>
                            <span class="text-sm font-medium text-gray-900">38岁</span>
                        </div>
                        <!-- CHUNK::basic-info-age::END -->

                        <!-- CHUNK::basic-info-birthday::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">出生日期：</span>
                            <span class="text-sm font-medium text-gray-900">1985-05-12</span>
                        </div>
                        <!-- CHUNK::basic-info-birthday::END -->

                        <!-- CHUNK::basic-info-education::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">学历：</span>
                            <span class="text-sm font-medium text-gray-900">博士</span>
                        </div>
                        <!-- CHUNK::basic-info-education::END -->

                        <!-- CHUNK::basic-info-degree::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">学位：</span>
                            <span class="text-sm font-medium text-gray-900">工学博士</span>
                        </div>
                        <!-- CHUNK::basic-info-degree::END -->

                        <!-- CHUNK::basic-info-school::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">毕业院校：</span>
                            <span class="text-sm font-medium text-gray-900">浙江大学</span>
                        </div>
                        <!-- CHUNK::basic-info-school::END -->

                        <!-- CHUNK::basic-info-company::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">工作单位：</span>
                            <span class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</span>
                        </div>
                        <!-- CHUNK::basic-info-company::END -->

                        <!-- CHUNK::basic-info-title::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">技术职称：</span>
                            <span class="text-sm font-medium text-gray-900">高级工程师</span>
                        </div>
                        <!-- CHUNK::basic-info-title::END -->

                        <!-- CHUNK::basic-info-field::BEGIN -->
                        <div>
                            <span class="text-sm text-gray-500">技术领域：</span>
                            <span class="text-sm font-medium text-gray-900">人工智能、大数据分析</span>
                        </div>
                        <!-- CHUNK::basic-info-field::END -->
                    </div>

                    <!-- CHUNK::basic-info-contact::BEGIN -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500 mb-2">联系方式</h3>
                        <div class="flex flex-wrap gap-4">
                            <div>
                                <span class="text-sm text-gray-500">手机：</span>
                                <span class="text-sm font-medium text-gray-900">138****1234</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">邮箱：</span>
                                <span class="text-sm font-medium text-gray-900"><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::basic-info-contact::END -->
                </div>
            </div>
        </div>
        <!-- SLOT::basic-info::END -->

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2 space-y-6">
                <!-- SLOT::achievements::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">科技成果</h2>
                        <div class="flex space-x-2">
                            <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                                <option>全部类型</option>
                                <option>专利</option>
                                <option>论文</option>
                                <option>其他</option>
                            </select>
                            <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                筛选
                            </button>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- CHUNK::achievements-patent-1::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-md font-medium text-blue-600">一种基于深度学习的图像识别方法及系统</h3>
                                <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">发明专利</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-500">
                                <span>专利号：ZL202010123456.7</span>
                                <span class="mx-2">|</span>
                                <span>授权日期：2022-03-15</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-700">
                                本发明公开了一种基于深度学习的图像识别方法及系统，通过改进的卷积神经网络结构，提高了图像识别的准确率和效率...
                            </div>
                            <div class="mt-2 flex justify-between items-center">
                                <span class="text-sm text-gray-500">第一发明人</span>
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                            </div>
                        </div>
                        <!-- CHUNK::achievements-patent-1::END -->

                        <!-- CHUNK::achievements-paper-1::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-md font-medium text-blue-600">基于Transformer的大规模预训练语言模型研究</h3>
                                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">SCI论文</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-500">
                                <span>期刊：计算机学报</span>
                                <span class="mx-2">|</span>
                                <span>发表时间：2021-08-20</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-700">
                                本文提出了一种改进的Transformer结构，在大规模中文语料上进行了预训练，实验表明该模型在多个NLP任务上达到了SOTA...
                            </div>
                            <div class="mt-2 flex justify-between items-center">
                                <span class="text-sm text-gray-500">通讯作者</span>
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                            </div>
                        </div>
                        <!-- CHUNK::achievements-paper-1::END -->
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出全部科技成果
                        </button>
                    </div>
                </div>
                <!-- SLOT::achievements::END -->

                <!-- SLOT::awards::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">奖励荣誉</h2>
                    
                    <div class="space-y-4">
                        <!-- CHUNK::awards-national::BEGIN -->
                        <div class="border-l-4 border-blue-500 pl-4 py-2">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-md font-medium text-gray-900">国家科学技术进步奖</h3>
                                    <div class="mt-1 text-sm text-gray-500">
                                        <span>二等奖</span>
                                        <span class="mx-2">|</span>
                                        <span>2020年度</span>
                                    </div>
                                </div>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">国家级</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-700">
                                项目名称：基于人工智能的智能制造关键技术研究与应用
                            </div>
                            <div class="mt-2">
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看证书</button>
                            </div>
                        </div>
                        <!-- CHUNK::awards-national::END -->

                        <!-- CHUNK::awards-provincial::BEGIN -->
                        <div class="border-l-4 border-green-500 pl-4 py-2">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-md font-medium text-gray-900">浙江省科学技术奖</h3>
                                    <div class="mt-1 text-sm text-gray-500">
                                        <span>一等奖</span>
                                        <span class="mx-2">|</span>
                                        <span>2018年度</span>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">省级</span>
                            </div>
                            <div class="mt-2 text-sm text-gray-700">
                                项目名称：大数据分析与挖掘关键技术研究
                            </div>
                            <div class="mt-2">
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看证书</button>
                            </div>
                        </div>
                        <!-- CHUNK::awards-provincial::END -->
                    </div>
                </div>
                <!-- SLOT::awards::END -->

                <!-- SLOT::experience::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">教育与工作经历</h2>
                    
                    <div class="space-y-6">
                        <!-- CHUNK::experience-education::BEGIN -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">教育经历</h3>
                            <div class="space-y-4">
                                <div class="pl-4 border-l-2 border-blue-200">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-900">浙江大学</span>
                                        <span class="text-sm text-gray-500">2003-2007</span>
                                    </div>
                                    <div class="text-sm text-gray-500">计算机科学与技术 / 本科</div>
                                </div>
                                <div class="pl-4 border-l-2 border-blue-200">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-900">浙江大学</span>
                                        <span class="text-sm text-gray-500">2007-2012</span>
                                    </div>
                                    <div class="text-sm text-gray-500">计算机应用技术 / 博士</div>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::experience-education::END -->

                        <!-- CHUNK::experience-work::BEGIN -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">工作经历</h3>
                            <div class="space-y-4">
                                <div class="pl-4 border-l-2 border-green-200">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</span>
                                        <span class="text-sm text-gray-500">2012-至今</span>
                                    </div>
                                    <div class="text-sm text-gray-500">首席技术官</div>
                                </div>
                                <div class="pl-4 border-l-2 border-green-200">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-900">阿里巴巴集团</span>
                                        <span class="text-sm text-gray-500">2010-2012</span>
                                    </div>
                                    <div class="text-sm text-gray-500">算法工程师（实习）</div>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::experience-work::END -->
                    </div>
                </div>
                <!-- SLOT::experience::END -->
            </div>

            <div class="space-y-6">
                <!-- SLOT::titles::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">称号认定</h2>
                    
                    <div class="space-y-3">
                        <!-- CHUNK::titles-national::BEGIN -->
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900">国家"万人计划"科技创新领军人才</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">国家级</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">2019年 / 中组部</div>
                        </div>
                        <!-- CHUNK::titles-national::END -->

                        <!-- CHUNK::titles-provincial::BEGIN -->
                        <div class="p-3 bg-green-50 rounded-lg">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900">浙江省"151人才工程"第一层次</span>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">省级</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">2017年 / 浙江省人社厅</div>
                        </div>
                        <!-- CHUNK::titles-provincial::END -->
                    </div>
                </div>
                <!-- SLOT::titles::END -->

                <!-- SLOT::projects::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">科研项目</h2>
                    
                    <div class="space-y-4">
                        <!-- CHUNK::projects-national::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-sm font-medium text-gray-900">国家重点研发计划项目</h3>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">国家级</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                <span>项目编号：2020YFB1000001</span>
                                <span class="mx-2">|</span>
                                <span>2019-2023</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-700">
                                项目名称：新一代人工智能关键技术研究与应用示范
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                角色：项目负责人
                            </div>
                        </div>
                        <!-- CHUNK::projects-national::END -->

                        <!-- CHUNK::projects-provincial::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-sm font-medium text-gray-900">浙江省重点研发计划项目</h3>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">省级</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                <span>项目编号：2020C01001</span>
                                <span class="mx-2">|</span>
                                <span>2018-2021</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-700">
                                项目名称：大数据分析与挖掘关键技术研究
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                角色：技术负责人
                            </div>
                        </div>
                        <!-- CHUNK::projects-provincial::END -->
                    </div>
                </div>
                <!-- SLOT::projects::END -->

                <!-- SLOT::network::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">合作关系网络</h2>
                    
                    <div class="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                        <div class="text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            <p class="mt-2 text-sm text-gray-500">合作关系网络图</p>
                        </div>
                    </div>
                </div>
                <!-- SLOT::network::END -->

                <!-- SLOT::policy::BEGIN -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">政策享受</h2>
                    
                    <div class="space-y-3">
                        <!-- CHUNK::policy-subsidy-1::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-sm font-medium text-gray-900">宁波市高层次人才安家补助</h3>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已发放</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                <span>金额：80万元</span>
                                <span class="mx-2">|</span>
                                <span>2020年</span>
                            </div>
                            <div class="mt-1">
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                            </div>
                        </div>
                        <!-- CHUNK::policy-subsidy-1::END -->

                        <!-- CHUNK::policy-subsidy-2::BEGIN -->
                        <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                            <div class="flex justify-between">
                                <h3 class="text-sm font-medium text-gray-900">浙江省"151人才工程"科研资助</h3>
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">审核中</span>
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                                <span>金额：50万元</span>
                                <span class="mx-2">|</span>
                                <span>2023年</span>
                            </div>
                            <div class="mt-1">
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                            </div>
                        </div>
                        <!-- CHUNK::policy-subsidy-2::END -->
                    </div>
                </div>
                <!-- SLOT::policy::END -->
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化合作关系网络图
            const networkCtx = document.createElement('canvas');
            networkCtx.id = 'networkChart';
            document.querySelector('#network .bg-gray-100').appendChild(networkCtx);
            
            const ctx = networkCtx.getContext('2d');
            new Chart(ctx, {
                type: 'graph',
                data: {
                    nodes: [
                        {id: '张明远', label: '张明远', color: 'rgba(59, 130, 246, 0.8)'},
                        {id: '李研究员', label: '李研究员', color: 'rgba(16, 185, 129, 0.8)'},
                        {id: '王教授', label: '王教授', color: 'rgba(245, 158, 11, 0.8)'},
                        {id: '宁波大学', label: '宁波大学', color: 'rgba(139, 92, 246, 0.8)'},
                        {id: '浙江大学', label: '浙江大学', color: 'rgba(239, 68, 68, 0.8)'}
                    ],
                    edges: [
                        {from: '张明远', to: '李研究员', label: '合作5次'},
                        {from: '张明远', to: '王教授', label: '合作3次'},
                        {from: '张明远', to: '宁波大学', label: '项目合作'},
                        {from: '张明远', to: '浙江大学', label: '项目合作'}
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        hierarchical: false
                    }
                }
            });

            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
        });
    </script>
</body>
</html>