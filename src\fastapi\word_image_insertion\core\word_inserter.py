"""
Word插图写入模块

实现向Word文档指定位置插入图片，支持格式控制和尺寸适配。
核心功能：在命中的段落 run.add_picture()，自动计算尺寸和格式。
"""

import logging
import shutil
from pathlib import Path
from typing import Optional, Dict, Any, Union
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from PIL import Image

from .document_parser import InsertionPoint

logger = logging.getLogger(__name__)


class WordInserter:
    """Word插图写入器"""
    
    def __init__(self, config=None):
        self.config = config
        self.default_dpi = 300
        self.max_width_inches = 6.5  # Word页面默认最大宽度
        self.min_width_inches = 2.0  # 最小宽度
        self.auto_scale = True  # 启用自动缩放
        
    def insert(self, doc_path: str, insertion_point: InsertionPoint, 
              image_path: str, output_path: Optional[str] = None) -> str:
        """
        插入图片到Word文档
        
        Args:
            doc_path: 源文档路径
            insertion_point: 插入点信息
            image_path: 图片路径
            output_path: 输出文档路径
            
        Returns:
            str: 输出文档路径
        """
        doc_path = Path(doc_path)
        image_path = Path(image_path)
        
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        if not image_path.exists():
            raise FileNotFoundError(f"Image not found: {image_path}")
        
        logger.info(f"Inserting image {image_path} into document {doc_path}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        else:
            output_path = Path(output_path)
        
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 加载文档
            doc = Document(doc_path)
            
            # 获取目标段落
            target_paragraph = doc.paragraphs[insertion_point.paragraph_index]
            
            # 计算图片尺寸
            image_width = self._calculate_image_width(image_path)
            
            # 在目标段落后插入新段落来放置图片
            self._insert_image_after_target_paragraph(doc, insertion_point.paragraph_index, image_path, image_width)
            
            # 保存文档
            doc.save(output_path)
            
            logger.info(f"Image inserted successfully, saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to insert image: {e}")
            raise
    
    def insert_multiple(self, doc_path: str, insertions: list, 
                       output_path: Optional[str] = None) -> str:
        """
        批量插入图片
        
        Args:
            doc_path: 源文档路径
            insertions: 插入配置列表，每个元素包含 (insertion_point, image_path)
            output_path: 输出文档路径
            
        Returns:
            str: 输出文档路径
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Batch inserting {len(insertions)} images into document {doc_path}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        else:
            output_path = Path(output_path)
        
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 加载文档
            doc = Document(doc_path)
            
            # 按段落索引排序，从后往前插入（避免索引偏移）
            sorted_insertions = sorted(insertions, key=lambda x: x[0].paragraph_index, reverse=True)
            
            for insertion_point, image_path in sorted_insertions:
                image_path = Path(image_path)
                if not image_path.exists():
                    logger.warning(f"Image not found, skipping: {image_path}")
                    continue
                
                # 计算图片尺寸
                image_width = self._calculate_image_width(image_path)
                
                # 插入图片
                self._insert_image_after_target_paragraph(doc, insertion_point.paragraph_index, image_path, image_width)
                
                logger.debug(f"Inserted image: {image_path} at paragraph {insertion_point.paragraph_index}")
            
            # 保存文档
            doc.save(output_path)
            
            logger.info(f"Batch insertion completed, saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to batch insert images: {e}")
            raise
    
    def _generate_output_path(self, doc_path: Path) -> Path:
        """生成输出路径"""
        stem = doc_path.stem
        suffix = doc_path.suffix
        parent = doc_path.parent
        
        # 添加时间戳避免冲突
        import time
        timestamp = int(time.time())
        
        return parent / f"{stem}_with_images_{timestamp}{suffix}"
    
    def _calculate_image_width(self, image_path: Path, target_width_inches: float = 5.0) -> float:
        """
        计算图片插入宽度
        
        Args:
            image_path: 图片路径
            target_width_inches: 目标宽度（英寸）
            
        Returns:
            float: 计算后的宽度（英寸）
        """
        try:
            with Image.open(image_path) as img:
                width_px, height_px = img.size
                
                # 获取DPI信息
                dpi = img.info.get('dpi', (self.default_dpi, self.default_dpi))
                if isinstance(dpi, tuple):
                    dpi_x = dpi[0]
                else:
                    dpi_x = dpi
                
                # 计算原始尺寸（英寸）
                original_width_inches = width_px / dpi_x
                
                # 如果启用自动缩放
                if self.auto_scale:
                    # 使用目标宽度，但不超过最大宽度
                    calculated_width = min(target_width_inches, self.max_width_inches)
                    # 不小于最小宽度
                    calculated_width = max(calculated_width, self.min_width_inches)
                    
                    logger.debug(f"Image {image_path.name}: {width_px}x{height_px}px, "
                               f"original: {original_width_inches:.2f}in, "
                               f"calculated: {calculated_width:.2f}in")
                    
                    return calculated_width
                else:
                    # 使用原始尺寸，但限制在合理范围内
                    return max(self.min_width_inches, min(original_width_inches, self.max_width_inches))
                    
        except Exception as e:
            logger.warning(f"Failed to calculate image size for {image_path}: {e}")
            return target_width_inches
    
    def _insert_image_after_target_paragraph(self, doc: Document, target_index: int, 
                                           image_path: Path, image_width: float):
        """
        在目标段落后插入图片
        
        Args:
            doc: Word文档对象
            target_index: 目标段落索引
            image_path: 图片路径
            image_width: 图片宽度（英寸）
        """
        try:
            # 在目标段落后添加新段落
            target_paragraph = doc.paragraphs[target_index]
            
            # 创建新段落用于放置图片
            new_paragraph = self._insert_paragraph_after(doc, target_index)
            
            # 设置段落居中对齐
            new_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加图片
            run = new_paragraph.runs[0] if new_paragraph.runs else new_paragraph.add_run()
            run.add_picture(str(image_path), width=Inches(image_width))
            
            # 添加空行分隔
            doc.add_paragraph()
            
            logger.debug(f"Image inserted after paragraph {target_index} with width {image_width:.2f}in")
            
        except Exception as e:
            logger.error(f"Failed to insert image after paragraph {target_index}: {e}")
            raise
    
    def _insert_paragraph_after(self, doc: Document, target_index: int):
        """
        在指定段落后插入新段落
        
        Args:
            doc: Word文档对象
            target_index: 目标段落索引
            
        Returns:
            新插入的段落对象
        """
        # 获取目标段落
        target_paragraph = doc.paragraphs[target_index]
        
        # 在目标段落后插入新段落
        new_paragraph = doc.add_paragraph()
        
        # 移动新段落到正确位置
        # 这是一个复杂的操作，需要操作底层XML
        target_element = target_paragraph._element
        new_element = new_paragraph._element
        
        # 将新段落移动到目标段落后面
        parent = target_element.getparent()
        parent.insert(parent.index(target_element) + 1, new_element)
        
        return new_paragraph
    
    def get_insertion_preview(self, doc_path: str, insertion_point: InsertionPoint) -> Dict[str, Any]:
        """
        获取插入预览信息
        
        Args:
            doc_path: 文档路径
            insertion_point: 插入点
            
        Returns:
            Dict: 预览信息
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        doc = Document(doc_path)
        
        # 获取目标段落及其上下文
        target_index = insertion_point.paragraph_index
        paragraphs = doc.paragraphs
        
        context = {
            'target_paragraph': {
                'index': target_index,
                'text': paragraphs[target_index].text,
                'style': paragraphs[target_index].style.name if paragraphs[target_index].style else 'Normal'
            },
            'before_paragraph': None,
            'after_paragraph': None
        }
        
        # 前一个段落
        if target_index > 0:
            context['before_paragraph'] = {
                'index': target_index - 1,
                'text': paragraphs[target_index - 1].text[:100] + ('...' if len(paragraphs[target_index - 1].text) > 100 else ''),
                'style': paragraphs[target_index - 1].style.name if paragraphs[target_index - 1].style else 'Normal'
            }
        
        # 后一个段落
        if target_index < len(paragraphs) - 1:
            context['after_paragraph'] = {
                'index': target_index + 1,
                'text': paragraphs[target_index + 1].text[:100] + ('...' if len(paragraphs[target_index + 1].text) > 100 else ''),
                'style': paragraphs[target_index + 1].style.name if paragraphs[target_index + 1].style else 'Normal'
            }
        
        return {
            'insertion_point': {
                'paragraph_index': insertion_point.paragraph_index,
                'match_type': insertion_point.match_type,
                'match_content': insertion_point.match_content,
                'text': insertion_point.text
            },
            'context': context,
            'document_info': {
                'path': str(doc_path),
                'total_paragraphs': len(paragraphs)
            }
        }
