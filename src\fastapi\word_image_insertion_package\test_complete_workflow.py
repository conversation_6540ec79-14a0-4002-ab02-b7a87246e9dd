#!/usr/bin/env python3
"""
完整的SVG和HTML插入测试工作流
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

from word_image_insertion import WordImageInserter, Config

async def test_complete_workflow():
    """测试完整的工作流：SVG和HTML插入到Word"""
    
    print("🎭 完整的Word精准插图测试")
    print("=" * 60)
    print("测试内容：")
    print("  1. SVG图片插入到'三、业务流程'章节后")
    print("  2. HTML图片插入到'四、操作流程'章节后")
    print("  3. 生成最终完整文档")
    print("=" * 60)
    
    # 文件路径
    files_dir = Path('word_image_insertion/files')
    output_dir = Path('complete_test_output')
    output_dir.mkdir(exist_ok=True)

    # 自动查找文件
    word_files = list(files_dir.glob("*.docx"))
    svg_files = list(files_dir.glob("*.svg"))
    html_files = list(files_dir.glob("*.html"))

    if not word_files:
        print(f"❌ 未找到Word文档: {files_dir}/*.docx")
        return False

    if not svg_files:
        print(f"❌ 未找到SVG文件: {files_dir}/*.svg")
        return False

    if not html_files:
        print(f"❌ 未找到HTML文件: {files_dir}/*.html")
        return False

    # 使用第一个找到的文件
    word_doc = word_files[0]
    svg_file = svg_files[0]
    html_file = html_files[0]

    print(f"📄 使用Word文档: {word_doc.name}")
    print(f"🎨 使用SVG文件: {svg_file.name}")
    print(f"🌐 使用HTML文件: {html_file.name}")
    
    # 创建插图器
    config = Config()
    inserter = WordImageInserter(config)
    
    try:
        print("\n📍 步骤1: 定位插入点")
        
        # 定位"三、业务流程"
        business_targets = inserter.parse_document(str(word_doc), keyword_pattern='三、业务流程')
        print(f"  ✅ 找到'三、业务流程' {len(business_targets)} 个位置")
        if business_targets:
            print(f"     插入位置: 第{business_targets[0].paragraph_index}段")
        
        # 定位"四、操作流程"
        operation_targets = inserter.parse_document(str(word_doc), keyword_pattern='四、操作流程')
        print(f"  ✅ 找到'四、操作流程' {len(operation_targets)} 个位置")
        if operation_targets:
            print(f"     插入位置: 第{operation_targets[0].paragraph_index}段")
        
        if not business_targets or not operation_targets:
            print("❌ 未找到必要的插入点")
            return False
        
        print("\n🎨 步骤2: 生成高质量图片")
        
        # 读取SVG内容
        with open(svg_file, 'r', encoding='utf-8') as f:
            svg_content = f.read()
        
        # 生成业务流程图（SVG）
        print("  🎨 生成业务流程图（SVG转PNG）...")
        business_image = await inserter.image_generator.generate_async(
            svg_content,
            output_path=str(output_dir / 'business_flow_from_svg.png'),
            viewport_width=1800,
            viewport_height=1200,
            device_scale_factor=2
        )
        print(f"  ✅ 业务流程图生成: {Path(business_image).name}")
        
        # 生成操作界面截图（HTML）
        print("  📸 生成操作界面截图（HTML转PNG）...")
        html_url = f"file:///{html_file.absolute().as_posix()}"
        operation_image = await inserter.image_generator.generate_async(
            html_url,
            output_path=str(output_dir / 'operation_ui_from_html.png'),
            viewport_size={'width': 1600, 'height': 1200},
            full_page=True,
            device_scale_factor=2
        )
        print(f"  ✅ 操作界面截图生成: {Path(operation_image).name}")
        
        # 显示图片信息
        from PIL import Image
        with Image.open(business_image) as img:
            size_mb = Path(business_image).stat().st_size / (1024 * 1024)
            print(f"    - 业务流程图: {img.size[0]}x{img.size[1]} 像素, {size_mb:.2f} MB")
        with Image.open(operation_image) as img:
            size_mb = Path(operation_image).stat().st_size / (1024 * 1024)
            print(f"    - 操作界面图: {img.size[0]}x{img.size[1]} 像素, {size_mb:.2f} MB")
        
        print("\n📝 步骤3: 精准插图到章节后")
        
        current_doc = str(word_doc)
        
        # 插入业务流程图到"三、业务流程"章节后
        print("  📝 插入业务流程图到'三、业务流程'章节后...")
        temp_doc1 = inserter.insert_image(
            current_doc,
            business_targets[0],
            business_image,
            str(output_dir / 'temp_with_business.docx')
        )
        print("  ✅ 业务流程图已插入")
        
        # 重新解析文档（因为段落索引可能已改变）
        print("  🔄 重新解析文档...")
        operation_targets_new = inserter.parse_document(temp_doc1, keyword_pattern='四、操作流程')
        if not operation_targets_new:
            print("  ⚠️  重新解析失败，使用原索引")
            operation_target = operation_targets[0]
        else:
            operation_target = operation_targets_new[0]
            print(f"  ✅ 重新定位操作流程: 第{operation_target.paragraph_index}段")
        
        # 插入操作界面图到"四、操作流程"章节后
        print("  📝 插入操作界面图到'四、操作流程'章节后...")
        final_doc = inserter.insert_image(
            temp_doc1,
            operation_target,
            operation_image,
            str(output_dir / 'final_complete_document.docx')
        )
        print("  ✅ 操作界面图已插入")
        
        # 清理临时文件
        Path(temp_doc1).unlink()
        
        print(f"\n🎉 完整文档生成成功!")
        print(f"📁 生成的文件:")
        print(f"  📄 最终文档: {final_doc}")
        print(f"  🎨 业务流程图: {business_image}")
        print(f"  📸 操作界面图: {operation_image}")
        
        return final_doc
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理浏览器资源
        try:
            await inserter.image_generator._close_browser()
        except:
            pass

def verify_final_document(doc_path):
    """验证最终文档的结构"""
    
    print(f"\n🔍 验证最终文档结构: {doc_path}")
    print("-" * 40)
    
    try:
        from docx import Document
        doc = Document(doc_path)
        
        business_found = False
        operation_found = False
        image_count = 0
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查章节标题
            if "三、业务流程" in text:
                business_found = True
                print(f"  📍 第{i}段: 三、业务流程")
            elif "四、操作流程" in text:
                operation_found = True
                print(f"  📍 第{i}段: 四、操作流程")
            
            # 检查图片
            has_image = False
            for run in paragraph.runs:
                if run._element.xpath('.//a:blip'):
                    has_image = True
                    break
            
            if has_image:
                image_count += 1
                print(f"  📸 第{i}段: 图片 #{image_count}")
        
        print(f"\n📊 验证结果:")
        print(f"  - 找到'三、业务流程': {'✅' if business_found else '❌'}")
        print(f"  - 找到'四、操作流程': {'✅' if operation_found else '❌'}")
        print(f"  - 插入的图片数量: {image_count}")
        print(f"  - 预期图片数量: 2")
        print(f"  - 图片插入状态: {'✅ 正确' if image_count == 2 else '❌ 异常'}")
        
        return image_count == 2
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

async def main():
    """主函数"""
    
    # 执行完整测试
    final_doc = await test_complete_workflow()
    
    if final_doc:
        # 验证最终文档
        verify_final_document(final_doc)
        
        print(f"\n💡 下一步测试建议:")
        print(f"  1. 打开文档查看效果: {final_doc}")
        print(f"  2. 运行预览功能测试: python test_preview_function.py")
        print(f"  3. 检查图片位置是否正确")
        
        return True
    else:
        print(f"\n❌ 测试失败")
        return False

if __name__ == '__main__':
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
