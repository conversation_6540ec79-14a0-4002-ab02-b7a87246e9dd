"""
格式回写与预览模块

集成LibreOffice转换和PDF预览功能，实现文档格式回写和即时预览。
核心功能：LibreOffice headless转PDF，浏览器预览，支持书签和跳转。
"""

import logging
import os
import subprocess
import tempfile
import webbrowser
from pathlib import Path
from typing import Optional, Dict, Any, List
import shutil
import json

logger = logging.getLogger(__name__)


class PreviewManager:
    """预览管理器"""
    
    def __init__(self, config=None):
        self.config = config
        self.libreoffice_path = self._find_libreoffice()
        self.temp_dir = Path(tempfile.gettempdir()) / 'word_image_insertion_preview'
        self.temp_dir.mkdir(exist_ok=True)
        
    def generate_preview(self, doc_path: str, output_format: str = 'pdf') -> str:
        """
        生成文档预览
        
        Args:
            doc_path: 文档路径
            output_format: 输出格式（pdf/html）
            
        Returns:
            str: 预览文件路径
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Generating {output_format.upper()} preview for: {doc_path}")
        
        if output_format.lower() == 'pdf':
            return self._convert_to_pdf(doc_path)
        elif output_format.lower() == 'html':
            return self._convert_to_html(doc_path)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def _convert_to_pdf(self, doc_path: Path) -> str:
        """转换为PDF"""
        if not self.libreoffice_path:
            raise RuntimeError("LibreOffice not found. Please install LibreOffice.")
        
        output_dir = self.temp_dir / 'pdf'
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / f"{doc_path.stem}.pdf"
        
        try:
            # LibreOffice命令行转换 - 增强版参数
            cmd = [
                str(self.libreoffice_path),
                '--headless',           # 无头模式
                '--invisible',          # 不可见模式
                '--nodefault',          # 不加载默认文档
                '--nolockcheck',        # 不检查文件锁
                '--nologo',             # 不显示启动画面
                '--norestore',          # 不恢复前次会话
                '--convert-to',
                'pdf:writer_pdf_Export:SelectPdfVersion=1;ExportBookmarks=1',
                '--outdir',
                str(output_dir),
                str(doc_path)
            ]

            logger.debug(f"Running LibreOffice command: {' '.join(cmd[:3])} ... {doc_path.name}")

            # Windows系统使用特殊标志避免弹窗
            creation_flags = 0
            if os.name == 'nt':
                creation_flags = subprocess.CREATE_NO_WINDOW

            # 使用DEVNULL避免控制台输出
            with open(os.devnull, 'w') as devnull:
                result = subprocess.run(
                    cmd,
                    stdout=devnull,
                    stderr=devnull,
                    timeout=120,  # 增加超时时间
                    creationflags=creation_flags
                )
            
            if result.returncode != 0:
                logger.error(f"LibreOffice conversion failed with return code: {result.returncode}")
                raise RuntimeError(f"PDF conversion failed with return code: {result.returncode}")
            
            if not output_path.exists():
                raise RuntimeError(f"PDF file was not created: {output_path}")
            
            logger.info(f"PDF generated successfully: {output_path}")
            return str(output_path)
            
        except subprocess.TimeoutExpired:
            logger.error("LibreOffice conversion timed out")
            raise RuntimeError("PDF conversion timed out")
        except Exception as e:
            logger.error(f"PDF conversion failed: {e}")
            raise RuntimeError(f"PDF conversion failed: {e}")
    
    def _convert_to_html(self, doc_path: Path) -> str:
        """转换为HTML"""
        if not self.libreoffice_path:
            raise RuntimeError("LibreOffice not found. Please install LibreOffice.")
        
        output_dir = self.temp_dir / 'html'
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / f"{doc_path.stem}.html"
        
        try:
            # LibreOffice命令行转换为HTML
            cmd = [
                str(self.libreoffice_path),
                '--headless',
                '--invisible',
                '--nodefault',
                '--nolockcheck',
                '--nologo',
                '--norestore',
                '--convert-to',
                'html:HTML:EmbedImages=1',
                '--outdir',
                str(output_dir),
                str(doc_path)
            ]

            logger.debug(f"Running LibreOffice HTML conversion: {doc_path.name}")

            # Windows系统使用特殊标志避免弹窗
            creation_flags = 0
            if os.name == 'nt':
                creation_flags = subprocess.CREATE_NO_WINDOW

            with open(os.devnull, 'w') as devnull:
                result = subprocess.run(
                    cmd,
                    stdout=devnull,
                    stderr=devnull,
                    timeout=120,
                    creationflags=creation_flags
                )
            
            if result.returncode != 0:
                logger.error(f"LibreOffice HTML conversion failed with return code: {result.returncode}")
                raise RuntimeError(f"HTML conversion failed with return code: {result.returncode}")
            
            if not output_path.exists():
                raise RuntimeError(f"HTML file was not created: {output_path}")
            
            logger.info(f"HTML generated successfully: {output_path}")
            return str(output_path)
            
        except subprocess.TimeoutExpired:
            logger.error("LibreOffice HTML conversion timed out")
            raise RuntimeError("HTML conversion timed out")
        except Exception as e:
            logger.error(f"HTML conversion failed: {e}")
            raise RuntimeError(f"HTML conversion failed: {e}")
    
    def open_preview(self, preview_path: str) -> bool:
        """
        在浏览器中打开预览
        
        Args:
            preview_path: 预览文件路径
            
        Returns:
            bool: 是否成功打开
        """
        preview_path = Path(preview_path)
        if not preview_path.exists():
            logger.error(f"Preview file not found: {preview_path}")
            return False
        
        try:
            # 转换为file:// URL
            file_url = preview_path.as_uri()
            
            logger.info(f"Opening preview in browser: {file_url}")
            webbrowser.open(file_url)
            return True
            
        except Exception as e:
            logger.error(f"Failed to open preview: {e}")
            return False
    
    def _find_libreoffice(self) -> Optional[Path]:
        """查找LibreOffice安装路径"""
        possible_paths = []
        
        if os.name == 'nt':  # Windows
            possible_paths = [
                Path(r"C:\Program Files\LibreOffice\program\soffice.exe"),
                Path(r"C:\Program Files (x86)\LibreOffice\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.0\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.1\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.2\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.3\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.4\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.5\program\soffice.exe"),
                Path(r"C:\Program Files\LibreOffice 7.6\program\soffice.exe"),
            ]
        else:  # Linux/macOS
            possible_paths = [
                Path("/usr/bin/libreoffice"),
                Path("/usr/local/bin/libreoffice"),
                Path("/opt/libreoffice/program/soffice"),
                Path("/Applications/LibreOffice.app/Contents/MacOS/soffice"),
            ]
        
        # 检查PATH中的命令
        try:
            result = subprocess.run(['which', 'libreoffice'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                path_from_which = Path(result.stdout.strip())
                if path_from_which.exists():
                    possible_paths.insert(0, path_from_which)
        except:
            pass
        
        # 查找第一个存在的路径
        for path in possible_paths:
            if path.exists():
                logger.debug(f"Found LibreOffice at: {path}")
                return path
        
        logger.warning("LibreOffice not found in standard locations")
        return None
    
    def get_preview_info(self, doc_path: str) -> Dict[str, Any]:
        """
        获取预览信息
        
        Args:
            doc_path: 文档路径
            
        Returns:
            Dict: 预览信息
        """
        doc_path = Path(doc_path)
        
        info = {
            'document_path': str(doc_path),
            'document_exists': doc_path.exists(),
            'libreoffice_available': self.libreoffice_path is not None,
            'libreoffice_path': str(self.libreoffice_path) if self.libreoffice_path else None,
            'temp_dir': str(self.temp_dir),
            'supported_formats': ['pdf', 'html']
        }
        
        if doc_path.exists():
            info.update({
                'document_size': doc_path.stat().st_size,
                'document_name': doc_path.name,
                'document_stem': doc_path.stem
            })
        
        return info
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            int: 清理的文件数量
        """
        import time
        
        if not self.temp_dir.exists():
            return 0
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        try:
            for file_path in self.temp_dir.rglob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(f"Cleaned temp file: {file_path}")
            
            # 清理空目录
            for dir_path in self.temp_dir.rglob('*'):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    dir_path.rmdir()
                    logger.debug(f"Cleaned empty dir: {dir_path}")
            
            logger.info(f"Cleaned {cleaned_count} temporary files")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
            return 0
