#!/usr/bin/env python3
"""
Word图像插入功能安装脚本

安装必要的依赖包并配置Playwright浏览器
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} - 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败")
        print(f"错误: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"❌ {description} - 命令未找到")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_dependencies():
    """安装Python依赖包"""
    print("\n📦 安装Python依赖包...")
    
    # 安装基础依赖
    if not run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      "安装requirements.txt中的依赖"):
        return False
    
    return True

def setup_playwright():
    """设置Playwright浏览器"""
    print("\n🌐 设置Playwright浏览器...")
    
    # 安装Playwright浏览器
    if not run_command([sys.executable, "-m", "playwright", "install", "chromium"], 
                      "安装Chromium浏览器"):
        return False
    
    # 安装系统依赖（Linux）
    if sys.platform.startswith('linux'):
        if not run_command([sys.executable, "-m", "playwright", "install-deps"], 
                          "安装系统依赖"):
            print("⚠️ 系统依赖安装失败，可能需要手动安装")
    
    return True

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    try:
        # 测试导入
        import playwright
        import docx
        from PIL import Image
        
        print("✅ 所有依赖包导入成功")
        
        # 测试Word图像插入模块
        sys.path.insert(0, str(Path.cwd()))
        from word_image_insertion import WordImageInserter, Config
        
        config = Config()
        inserter = WordImageInserter(config)
        
        print("✅ Word图像插入模块加载成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_test_files():
    """创建测试文件"""
    print("\n📝 创建测试文件...")
    
    try:
        from word_image_insertion.utils.helpers import create_sample_document, create_sample_svg, create_sample_html
        
        # 创建测试目录
        test_dir = Path("test_files")
        test_dir.mkdir(exist_ok=True)
        
        # 创建示例文档
        doc_path = create_sample_document(str(test_dir / "sample_document.docx"), include_images=True)
        svg_path = create_sample_svg(str(test_dir / "sample_flowchart.svg"))
        html_path = create_sample_html(str(test_dir / "sample_ui.html"))
        
        print(f"✅ 测试文件创建成功:")
        print(f"  - 文档: {doc_path}")
        print(f"  - SVG: {svg_path}")
        print(f"  - HTML: {html_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试文件创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Word图像插入功能安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        sys.exit(1)
    
    # 设置Playwright
    if not setup_playwright():
        print("\n❌ Playwright设置失败")
        sys.exit(1)
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 安装验证失败")
        sys.exit(1)
    
    # 创建测试文件
    if not create_test_files():
        print("\n⚠️ 测试文件创建失败，但不影响主要功能")
    
    print("\n🎉 Word图像插入功能安装完成！")
    print("\n📋 后续步骤:")
    print("1. 确保LibreOffice已安装（用于预览功能）")
    print("2. 运行FastAPI服务器: python -m uvicorn app.main:app --reload")
    print("3. 测试Word图像插入功能")
    
    print("\n💡 提示:")
    print("- Windows: 从 https://www.libreoffice.org/download/ 下载安装LibreOffice")
    print("- Linux: sudo apt-get install libreoffice")
    print("- macOS: brew install --cask libreoffice")

if __name__ == "__main__":
    main()
