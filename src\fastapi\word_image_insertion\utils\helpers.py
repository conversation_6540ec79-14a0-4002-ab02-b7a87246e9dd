"""
辅助函数模块

提供日志设置、依赖验证、示例文档创建等辅助功能。
"""

import logging
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from docx import Document
from docx.shared import Inches
from docx.enum.style import WD_STYLE_TYPE


def setup_logging(level: int = logging.INFO, 
                 format_string: Optional[str] = None,
                 file_path: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        format_string: 日志格式字符串
        file_path: 日志文件路径
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置基本配置
    handlers = []
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(format_string))
    handlers.append(console_handler)
    
    # 文件处理器（如果指定）
    if file_path:
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(file_path, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(format_string))
            handlers.append(file_handler)
        except Exception as e:
            print(f"Warning: Failed to setup file logging: {e}")
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        format=format_string,
        handlers=handlers,
        force=True
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('playwright').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)
    
    logger = logging.getLogger('word_image_insertion')
    logger.info(f"Logging setup completed, level: {logging.getLevelName(level)}")
    
    return logger


def validate_dependencies() -> Dict[str, Any]:
    """
    验证项目依赖
    
    Returns:
        Dict: 验证结果
    """
    results = {
        'all_ok': True,
        'python_packages': {},
        'system_tools': {},
        'errors': [],
        'warnings': []
    }
    
    # 检查Python包
    required_packages = [
        ('playwright', 'playwright'),
        ('docx', 'python-docx'),
        ('PIL', 'Pillow'),
    ]
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            results['python_packages'][package_name] = {'status': 'ok', 'version': None}
            
            # 尝试获取版本信息
            try:
                if import_name == 'playwright':
                    import playwright
                    results['python_packages'][package_name]['version'] = playwright.__version__
                elif import_name == 'docx':
                    import docx
                    results['python_packages'][package_name]['version'] = getattr(docx, '__version__', 'unknown')
                elif import_name == 'PIL':
                    from PIL import Image
                    results['python_packages'][package_name]['version'] = getattr(Image, '__version__', 'unknown')
            except:
                pass
                
        except ImportError as e:
            results['python_packages'][package_name] = {'status': 'missing', 'error': str(e)}
            results['errors'].append(f"Missing Python package: {package_name}")
            results['all_ok'] = False
    
    # 检查系统工具
    system_tools = [
        ('libreoffice', ['libreoffice', '--version']),
        ('chromium', ['chromium', '--version']),
    ]
    
    for tool_name, check_cmd in system_tools:
        try:
            result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version_info = result.stdout.strip().split('\n')[0] if result.stdout else 'unknown'
                results['system_tools'][tool_name] = {'status': 'ok', 'version': version_info}
            else:
                results['system_tools'][tool_name] = {'status': 'error', 'error': result.stderr}
                results['warnings'].append(f"System tool {tool_name} check failed")
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            results['system_tools'][tool_name] = {'status': 'missing', 'error': str(e)}
            results['warnings'].append(f"System tool not found: {tool_name}")
    
    return results


def create_sample_document(output_path: str, include_images: bool = False) -> str:
    """
    创建示例文档
    
    Args:
        output_path: 输出路径
        include_images: 是否包含图片占位符
        
    Returns:
        str: 创建的文档路径
    """
    doc = Document()
    
    # 添加标题
    title = doc.add_heading('招标文档示例', 0)
    
    # 添加章节
    sections = [
        ('一、项目概述', '本项目旨在开发一套完整的管理系统，包含多个功能模块。'),
        ('二、技术方案', '采用现代化的技术架构，确保系统的稳定性和可扩展性。'),
        ('三、业务流程', '系统的核心业务流程包括用户管理、数据处理、报表生成等。'),
        ('四、操作流程', '用户通过直观的界面进行操作，系统提供完整的操作指导。'),
        ('五、实施计划', '项目将分阶段实施，确保每个阶段的质量和进度。')
    ]
    
    for section_title, section_content in sections:
        # 添加章节标题
        doc.add_heading(section_title, level=1)
        
        # 添加章节内容
        doc.add_paragraph(section_content)
        
        # 如果需要，添加图片占位符
        if include_images and ('业务流程' in section_title or '操作流程' in section_title):
            placeholder_para = doc.add_paragraph()
            placeholder_para.add_run('[此处将插入流程图]').italic = True
            doc.add_paragraph()  # 空行
    
    # 保存文档
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    doc.save(output_path)
    
    return str(output_path)


def create_sample_svg(output_path: str, title: str = "示例流程图") -> str:
    """
    创建示例SVG文件
    
    Args:
        output_path: 输出路径
        title: 图表标题
        
    Returns:
        str: 创建的SVG文件路径
    """
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title {{ font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }}
            .box {{ fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }}
            .text {{ font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }}
            .arrow {{ stroke: #0277bd; stroke-width: 2; marker-end: url(#arrowhead); }}
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#0277bd" />
        </marker>
    </defs>
    
    <!-- 标题 -->
    <text x="400" y="40" class="title">{title}</text>
    
    <!-- 流程框 -->
    <rect x="150" y="80" width="120" height="60" class="box" rx="5"/>
    <text x="210" y="115" class="text">开始</text>
    
    <rect x="150" y="180" width="120" height="60" class="box" rx="5"/>
    <text x="210" y="215" class="text">数据输入</text>
    
    <rect x="150" y="280" width="120" height="60" class="box" rx="5"/>
    <text x="210" y="315" class="text">数据处理</text>
    
    <rect x="150" y="380" width="120" height="60" class="box" rx="5"/>
    <text x="210" y="415" class="text">结果输出</text>
    
    <rect x="150" y="480" width="120" height="60" class="box" rx="5"/>
    <text x="210" y="515" class="text">结束</text>
    
    <!-- 箭头 -->
    <line x1="210" y1="140" x2="210" y2="180" class="arrow"/>
    <line x1="210" y1="240" x2="210" y2="280" class="arrow"/>
    <line x1="210" y1="340" x2="210" y2="380" class="arrow"/>
    <line x1="210" y1="440" x2="210" y2="480" class="arrow"/>
    
    <!-- 分支流程 -->
    <rect x="350" y="280" width="120" height="60" class="box" rx="5"/>
    <text x="410" y="315" class="text">异常处理</text>
    
    <line x1="270" y1="310" x2="350" y2="310" class="arrow"/>
    <line x1="410" y1="340" x2="410" y2="400" class="arrow"/>
    <line x1="350" y1="400" x2="210" y2="400" class="arrow"/>
    
    <!-- 说明文字 -->
    <text x="500" y="150" class="text">正常流程</text>
    <text x="500" y="320" class="text">异常分支</text>
</svg>'''
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    return str(output_path)


def create_sample_html(output_path: str, title: str = "示例UI原型") -> str:
    """
    创建示例HTML文件
    
    Args:
        output_path: 输出路径
        title: 页面标题
        
    Returns:
        str: 创建的HTML文件路径
    """
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        .content {{
            padding: 30px;
        }}
        .form-group {{
            margin-bottom: 20px;
        }}
        .form-group label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }}
        .form-group input, .form-group select, .form-group textarea {{
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }}
        .btn {{
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }}
        .btn:hover {{
            background: #5a6fd8;
        }}
        .btn-secondary {{
            background: #6c757d;
        }}
        .btn-secondary:hover {{
            background: #5a6268;
        }}
        .table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        .table th, .table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .table th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .status-active {{
            color: #28a745;
            font-weight: bold;
        }}
        .status-inactive {{
            color: #dc3545;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <p>现代化管理系统界面设计</p>
        </div>
        
        <div class="content">
            <h2>用户信息管理</h2>
            
            <form>
                <div class="form-group">
                    <label for="username">用户名：</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名">
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱：</label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-group">
                    <label for="role">角色：</label>
                    <select id="role" name="role">
                        <option value="">请选择角色</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                        <option value="guest">访客</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="description">描述：</label>
                    <textarea id="description" name="description" rows="4" placeholder="请输入用户描述"></textarea>
                </div>
                
                <button type="submit" class="btn">保存</button>
                <button type="reset" class="btn btn-secondary">重置</button>
            </form>
            
            <h3>用户列表</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td><EMAIL></td>
                        <td>管理员</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>2024-01-01 10:00:00</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>user001</td>
                        <td><EMAIL></td>
                        <td>普通用户</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>2024-01-02 14:30:00</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>guest</td>
                        <td><EMAIL></td>
                        <td>访客</td>
                        <td><span class="status-inactive">禁用</span></td>
                        <td>2024-01-03 09:15:00</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>'''
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return str(output_path)
