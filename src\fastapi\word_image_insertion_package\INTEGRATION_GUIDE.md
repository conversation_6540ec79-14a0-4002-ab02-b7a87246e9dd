# Word精准插图系统 - 集成文件清单

## 📦 核心功能包（必需文件）

### 1. 主要功能模块
```
word_image_insertion/
├── __init__.py                    # 包初始化文件
├── config.py                      # 核心配置类
├── config_template.py             # 配置模板（用户自定义）
├── core/
│   ├── __init__.py               # 核心模块初始化
│   ├── document_parser.py        # 文档解析器（章节识别）
│   ├── image_generator.py        # 图片生成器（SVG/HTML转PNG）
│   ├── word_inserter.py          # Word插图器（精准插入）
│   ├── preview_manager.py        # 标准预览管理器
│   └── enhanced_preview.py       # 增强预览管理器（PDF+HTML双视图）
└── files/                        # 输入文件目录（用户文件）
    ├── *.docx                    # Word文档
    ├── *.svg                     # SVG图片
    └── *.html                    # HTML文件
```

### 2. 演示和测试脚本
```
demo_without_preview.py           # 主要演示程序（推荐）
test_complete_workflow.py         # 完整工作流测试
test_preview_function.py          # 预览功能测试（标准+增强）
test_enhanced_preview.py          # 增强预览专项测试
run_all_tests.py                  # 一键测试脚本
```

### 3. 依赖文件
```
requirements.txt                  # Python依赖包
package.json                      # Node.js依赖（如果需要）
```

## 🔧 依赖环境

### Python依赖包
```
python-docx>=0.8.11              # Word文档处理
playwright>=1.40.0               # 浏览器自动化（图片生成）
Pillow>=10.0.0                   # 图片处理
pathlib                          # 路径处理（内置）
logging                          # 日志（内置）
subprocess                       # 进程管理（内置）
tempfile                         # 临时文件（内置）
webbrowser                       # 浏览器打开（内置）
```

### 系统依赖
```
LibreOffice                       # PDF/HTML转换（必需）
Chromium/Chrome                   # Playwright浏览器引擎
```

## 🎯 核心功能特性

### 1. 图片回写到Word功能
- ✅ 智能章节识别（支持多模块文档）
- ✅ 精准插入位置定位
- ✅ SVG → 高清PNG转换
- ✅ HTML → 高清截图
- ✅ 自动文件检测
- ✅ 通用配置支持

### 2. 增强预览功能
- ✅ PDF+HTML双视图同步
- ✅ 带书签PDF导出
- ✅ 带锚点HTML生成
- ✅ 双向同步滚动
- ✅ 现代化交互界面
- ✅ 响应式布局设计

### 3. 标准预览功能
- ✅ Word → PDF转换
- ✅ Word → HTML转换
- ✅ 浏览器预览
- ✅ 批量转换支持

## 📋 集成步骤

### 步骤1: 复制核心文件
```bash
# 复制整个word_image_insertion包到目标项目
cp -r word_image_insertion/ /path/to/your/project/

# 复制演示脚本（可选）
cp demo_without_preview.py /path/to/your/project/
cp test_complete_workflow.py /path/to/your/project/
cp test_preview_function.py /path/to/your/project/
```

### 步骤2: 安装依赖
```bash
# 安装Python依赖
pip install python-docx playwright Pillow

# 安装Playwright浏览器
playwright install chromium

# 安装LibreOffice（系统级）
# Windows: https://www.libreoffice.org/download/
# Linux: sudo apt-get install libreoffice
# macOS: brew install --cask libreoffice
```

### 步骤3: 配置文件路径
```python
# 在你的项目中使用
from word_image_insertion import WordImageInserter, Config

# 创建配置
config = Config()
config.input_dir = "your/input/directory"
config.output_dir = "your/output/directory"

# 创建插图器
inserter = WordImageInserter(config)
```

## 🔨 API使用示例

### 基础使用
```python
from word_image_insertion import WordImageInserter, Config

# 创建插图器
config = Config()
inserter = WordImageInserter(config)

# 解析文档，查找插入点
targets = inserter.parse_document("document.docx", keyword_pattern="业务流程")

# 生成图片
image_path = await inserter.image_generator.generate_async(
    "flowchart.svg",
    output_path="output.png"
)

# 插入图片到Word
final_doc = inserter.insert_image(
    "document.docx",
    targets[0],
    image_path,
    "output_document.docx"
)
```

### 预览功能
```python
from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager

# 创建增强预览
preview_manager = EnhancedPreviewManager()

# 生成双视图预览
viewer_path = preview_manager.create_dual_view_preview("document.docx")

# 在浏览器中打开
preview_manager.open_preview(viewer_path)
```

## 🎨 自定义配置

### 创建自定义配置
```python
# 复制config_template.py为config_custom.py
# 修改章节匹配规则
SECTION_PATTERNS = {
    'business_flow': {
        'keywords': ['业务流程', '流程说明'],
        'image_types': ['svg', 'png'],
        'image_width': 5.0,
    },
    'operation_flow': {
        'keywords': ['操作流程', '操作说明'],
        'image_types': ['html', 'png'],
        'image_width': 5.5,
    }
}
```

## 🚀 快速开始

### 最小集成示例
```python
#!/usr/bin/env python3
"""
最小集成示例
"""
import asyncio
from pathlib import Path
from word_image_insertion import WordImageInserter, Config

async def main():
    # 创建配置
    config = Config()
    inserter = WordImageInserter(config)
    
    # 文件路径
    word_doc = "input/document.docx"
    svg_file = "input/flowchart.svg"
    
    # 查找插入点
    targets = inserter.parse_document(word_doc, keyword_pattern="业务流程")
    
    if targets:
        # 生成图片
        image_path = await inserter.image_generator.generate_async(
            svg_file,
            output_path="output/flowchart.png"
        )
        
        # 插入图片
        final_doc = inserter.insert_image(
            word_doc,
            targets[0],
            image_path,
            "output/final_document.docx"
        )
        
        print(f"✅ 图片已插入: {final_doc}")
    else:
        print("❌ 未找到插入点")

if __name__ == "__main__":
    asyncio.run(main())
```

## 📁 目录结构建议

### 在目标项目中的建议结构
```
your_project/
├── word_image_insertion/          # 核心功能包
├── input/                         # 输入文件目录
│   ├── documents/                # Word文档
│   ├── images/                   # 图片文件
│   └── templates/                # 模板文件
├── output/                        # 输出文件目录
├── config/                        # 配置文件
│   └── word_insertion_config.py  # 自定义配置
└── scripts/                       # 脚本文件
    ├── insert_images.py          # 图片插入脚本
    └── preview_documents.py      # 预览脚本
```

## ⚠️ 注意事项

### 1. 系统要求
- Python 3.8+
- LibreOffice 7.0+
- 足够的磁盘空间（临时文件）

### 2. 性能考虑
- 大文档处理可能需要较长时间
- 高分辨率图片生成占用内存较多
- 建议在服务器环境中使用headless模式

### 3. 安全考虑
- 临时文件会自动清理
- 不要在生产环境中使用测试脚本
- 建议对输入文件进行验证

## 🔧 故障排除

### 常见问题
1. **LibreOffice未找到**: 检查安装路径和环境变量
2. **Playwright浏览器缺失**: 运行`playwright install chromium`
3. **权限问题**: 确保有读写临时目录的权限
4. **内存不足**: 调整图片生成参数或增加系统内存

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用调试配置
config = Config()
config.debug = True
```
