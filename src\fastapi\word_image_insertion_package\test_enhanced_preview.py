#!/usr/bin/env python3
"""
增强预览功能测试

测试PDF+Markdown双视图同步预览功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager
from word_image_insertion import setup_logging

def test_enhanced_preview():
    """测试增强预览功能"""
    
    print("🎭 增强预览功能测试")
    print("=" * 60)
    print("功能特性：")
    print("  📄 PDF视图（带书签导航）")
    print("  📝 HTML视图（带锚点跳转）")
    print("  🔄 双视图同步滚动")
    print("  🎨 现代化交互界面")
    print("  📱 响应式布局")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 查找测试文档
    test_docs = [
        "complete_test_output/final_complete_document.docx",
        "word_image_insertion/files/投标文件-0613.docx",
        "test_operation_flow_output.docx"
    ]
    
    test_doc = None
    for doc_path in test_docs:
        if Path(doc_path).exists():
            test_doc = doc_path
            break
    
    if not test_doc:
        print("❌ 未找到测试文档，请先运行以下任一测试:")
        print("  - python test_complete_workflow.py")
        print("  - python run_all_tests.py")
        return False
    
    print(f"📄 使用测试文档: {test_doc}")
    
    try:
        # 创建增强预览管理器
        preview_manager = EnhancedPreviewManager()
        
        print(f"\n🔍 检查LibreOffice...")
        if not preview_manager.libreoffice_path:
            print("❌ LibreOffice未安装或未找到")
            print("📋 安装指南:")
            print("  1. 访问: https://www.libreoffice.org/download/")
            print("  2. 下载并安装LibreOffice")
            print("  3. 重新运行此测试")
            return False
        
        print(f"✅ LibreOffice已找到: {preview_manager.libreoffice_path}")
        
        print(f"\n🎨 步骤1: 生成PDF（带书签）...")
        pdf_path = preview_manager._generate_pdf_with_bookmarks(Path(test_doc))
        print(f"✅ PDF生成成功: {Path(pdf_path).name}")
        
        print(f"\n📝 步骤2: 生成HTML（带锚点）...")
        html_path = preview_manager._generate_markdown_html(Path(test_doc))
        print(f"✅ HTML生成成功: {Path(html_path).name}")
        
        print(f"\n🔗 步骤3: 提取书签映射...")
        bookmark_mapping = preview_manager._extract_bookmark_mapping(pdf_path, html_path)
        print(f"✅ 提取到 {len(bookmark_mapping)} 个书签映射")
        if bookmark_mapping:
            print("📋 书签映射示例:")
            for i, (title, slug) in enumerate(list(bookmark_mapping.items())[:3]):
                print(f"  {i+1}. '{title}' → '{slug}'")
            if len(bookmark_mapping) > 3:
                print(f"  ... 还有 {len(bookmark_mapping) - 3} 个")
        
        print(f"\n🎭 步骤4: 创建双视图同步预览...")
        viewer_path = preview_manager.create_dual_view_preview(test_doc)
        print(f"✅ 双视图预览创建成功: {Path(viewer_path).name}")
        
        # 显示文件信息
        print(f"\n📊 生成的文件:")
        for file_path in [pdf_path, html_path, viewer_path]:
            path = Path(file_path)
            size_mb = path.stat().st_size / (1024 * 1024)
            print(f"  📁 {path.name}: {size_mb:.2f} MB")
        
        print(f"\n🚀 步骤5: 在浏览器中打开预览...")
        success = preview_manager.open_preview(viewer_path)
        
        if success:
            print("✅ 预览已在浏览器中打开")
            print(f"\n💡 使用指南:")
            print(f"  🖱️  点击PDF书签或滚动页面，HTML视图会自动同步")
            print(f"  📝 点击HTML标题或滚动，PDF视图会自动同步")
            print(f"  🔄 使用'切换同步'按钮控制同步开关")
            print(f"  📏 拖拽中间分隔条调整视图大小")
            print(f"  ⌨️  快捷键: Ctrl+S(切换同步), Ctrl+R(重置布局), Ctrl+P(打印)")
            
            print(f"\n🎯 测试建议:")
            print(f"  1. 在PDF视图中点击不同的书签")
            print(f"  2. 滚动PDF页面观察HTML同步")
            print(f"  3. 在HTML视图中点击标题")
            print(f"  4. 滚动HTML页面观察PDF同步")
            print(f"  5. 尝试调整视图大小")
            
        else:
            print("❌ 预览打开失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_standard_preview_comparison():
    """对比标准预览和增强预览"""
    
    print(f"\n" + "=" * 60)
    print("📊 标准预览 vs 增强预览对比")
    print("=" * 60)
    
    comparison = [
        ("功能", "标准预览", "增强预览"),
        ("PDF生成", "✅ 基础PDF", "✅ 带书签PDF"),
        ("HTML生成", "✅ 基础HTML", "✅ 带锚点HTML"),
        ("视图布局", "❌ 单一视图", "✅ 双视图并排"),
        ("同步滚动", "❌ 不支持", "✅ 双向同步"),
        ("书签导航", "❌ 不支持", "✅ 点击跳转"),
        ("交互体验", "⚠️  基础", "✅ 现代化UI"),
        ("响应式设计", "❌ 不支持", "✅ 移动端适配"),
        ("快捷键", "❌ 不支持", "✅ 多种快捷键"),
        ("布局调整", "❌ 不支持", "✅ 拖拽调整"),
    ]
    
    for feature, standard, enhanced in comparison:
        print(f"  {feature:<12} | {standard:<15} | {enhanced}")
    
    print(f"\n💡 增强预览的优势:")
    print(f"  🎯 提供更好的文档浏览体验")
    print(f"  🔄 支持PDF和HTML之间的智能同步")
    print(f"  🎨 现代化的用户界面设计")
    print(f"  📱 支持桌面和移动设备")
    print(f"  ⚡ 高性能的渲染和交互")

def main():
    """主函数"""
    
    success = test_enhanced_preview()
    
    if success:
        test_standard_preview_comparison()
        
        print(f"\n🎉 增强预览功能测试完成！")
        print(f"📁 临时文件位置: {Path.home() / 'AppData/Local/Temp/word_image_insertion_enhanced_preview'}")
        print(f"🧹 提示: 临时文件会在24小时后自动清理")
        
    else:
        print(f"\n❌ 增强预览功能测试失败")
        print(f"💡 请检查:")
        print(f"  1. LibreOffice是否正确安装")
        print(f"  2. 测试文档是否存在")
        print(f"  3. 系统权限是否足够")
    
    input(f"\n按回车键退出...")

if __name__ == "__main__":
    main()
