{"timestamp": "2025-07-28 15:28:19", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Mon, 28 Jul 2025 07:28:19 GMT", "content-type": "application/json; charset=utf-8", "content-length": "9344", "x-client-request-id": "unknown-20250728152633-eYwglDqh", "x-envoy-upstream-service-time": "106829", "x-request-id": "0217536875933876931e96c88190a95fb8b4afdd092a7594d5433"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Here's the SVG flowchart based on your requirements:\n\n```svg\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" font-family=\"'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif\">\n  <!-- 定义 -->\n  <defs>\n    <!-- 箭头样式 -->\n    <marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">\n      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#333\" />\n    </marker>\n    <!-- 阴影滤镜 -->\n    <filter id=\"soft-shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.1\" />\n    </filter>\n  </defs>\n\n  <!-- 背景 -->\n  <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n\n  <!-- 主标题 -->\n  <text x=\"600\" y=\"50\" font-size=\"28\" text-anchor=\"middle\" font-weight=\"600\" fill=\"#333\">科技人才数据管理系统流程</text>\n\n  <!-- 阶段一：数据汇聚 -->\n  <text x=\"600\" y=\"100\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段一：数据汇聚与展示</text>\n  \n  <!-- 节点1: 数据汇聚 -->\n  <g transform=\"translate(500, 130)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据自动汇聚</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">实时刷新概览卡片和分布大屏</text>\n  </g>\n\n  <!-- 阶段二：交互筛选 -->\n  <text x=\"600\" y=\"250\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段二：交互筛选与联动</text>\n\n  <!-- 节点2: 类型筛选 -->\n  <g transform=\"translate(350, 280)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才类别筛选</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">用户选择感兴趣的人才类别</text>\n  </g>\n\n  <!-- 节点3: 数据联动 -->\n  <g transform=\"translate(650, 280)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">全局数据联动</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">刷新概览卡片及分布数据</text>\n  </g>\n\n  <!-- 连接线 1 -> 2/3 (曲线) -->\n  <path d=\"M 550 200 C 550 220, 450 250, 450 280\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 650 200 C 650 220, 750 250, 750 280\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段三：详情查看 -->\n  <text x=\"600\" y=\"400\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段三：人才详情查看</text>\n\n  <!-- 节点4: 人才清册 -->\n  <g transform=\"translate(350, 430)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才清册展示</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">点击卡片或区块查看人员列表</text>\n  </g>\n\n  <!-- 节点5: 人才详情 -->\n  <g transform=\"translate(650, 430)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才详情界面</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">科研背景、项目、成果等详细信息</text>\n  </g>\n\n  <!-- 连接线 2/3 -> 4 (曲线) -->\n  <path d=\"M 450 350 C 450 380, 350 400, 350 430\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 750 350 C 750 380, 850 400, 850 430\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 连接线 4 -> 5 (直线) -->\n  <path d=\"M 550 465 C 600 465, 650 465, 650 465\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段四：数据操作 -->\n  <text x=\"600\" y=\"550\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段四：数据操作与管理</text>\n\n  <!-- 节点6: 数据筛选 -->\n  <g transform=\"translate(300, 580)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">精细筛选</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">按需调整筛选条件</text>\n  </g>\n\n  <!-- 节点7: 数据导出 -->\n  <g transform=\"translate(700, 580)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据导出</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">导出选定人员信息</text>\n  </g>\n\n  <!-- 节点8: 操作日志 -->\n  <g transform=\"translate(500, 700)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">操作日志记录</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">全程记录以备合规审计</text>\n  </g>\n\n  <!-- 连接线 4 -> 6/7 (曲线) -->\n  <path d=\"M 450 500 C 450 540, 350 560, 300 580\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 650 500 C 650 540, 750 560, 700 580\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 连接线 6/7 -> 8 (曲线) -->\n  <path d=\"M 400 650 C 450 670, 550 670, 500 700\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 600 650 C 550 670, 450 670, 500 700\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n</svg>\n```\n\nThis SVG flowchart follows all your design requirements:\n\n1. **Four clear stages** with distinct section headers\n2. **Color-coded nodes**:\n   - Stage 1 (Data Aggregation): Light blue\n   - Stage 2 (Interactive Filtering): Light green\n   - Stage 3 (Detail View): Light purple\n   - Stage 4 (Data Operations): Light yellow\n\n3. **Visual elements**:\n   - Rounded rectangles with soft shadows\n   - Two-line text structure in each node (title + description)\n   - Smooth Bezier curves for connections\n   - Clear arrow markers\n   - Modern sans-serif font\n\n4. **Logical flow** showing the complete process from data aggregation to detailed viewing and operations.\n\nThe layout provides ample white space and maintains a professional, clean appearance while clearly illustrating the workflow.", "role": "assistant"}}], "created": 1753687700, "id": "0217536875933876931e96c88190a95fb8b4afdd092a7594d5433", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 2341, "prompt_tokens": 2306, "total_tokens": 4647, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}