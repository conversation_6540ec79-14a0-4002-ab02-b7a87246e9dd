{"timestamp": "2025-07-26 20:54:50", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Sat, 26 Jul 2025 12:55:01 GMT", "content-type": "application/json; charset=utf-8", "content-length": "7383", "x-client-request-id": "unknown-20250726205357-YvAWmheV", "x-envoy-upstream-service-time": "64483", "x-request-id": "021753534437350b70496ab48cdcee516e625f531bc0553f4860a"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Here's the SVG flowchart based on your requirements:\n\n```svg\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" font-family=\"'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif\">\n  <!-- 定义 -->\n  <defs>\n    <!-- 箭头样式 -->\n    <marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">\n      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#333\" />\n    </marker>\n    <!-- 阴影滤镜 -->\n    <filter id=\"soft-shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.1\" />\n    </filter>\n  </defs>\n\n  <!-- 背景 -->\n  <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n\n  <!-- 主标题 -->\n  <text x=\"600\" y=\"50\" font-size=\"28\" text-anchor=\"middle\" font-weight=\"600\" fill=\"#333\">科技人才数据管理系统流程</text>\n\n  <!-- 阶段一：数据汇聚 -->\n  <text x=\"600\" y=\"100\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段一：数据汇聚与展示</text>\n  \n  <!-- 节点1: 数据汇聚 -->\n  <g transform=\"translate(500, 130)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据汇聚</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">自动汇聚全市科技人才基础数据</text>\n  </g>\n\n  <!-- 节点2: 数据展示 -->\n  <g transform=\"translate(500, 250)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"90\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">实时展示</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">刷新数量概览卡片和分布大屏</text>\n    <text x=\"100\" y=\"80\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">支持类型切换联动</text>\n  </g>\n  \n  <!-- 连接线 1 -> 2 -->\n  <path d=\"M 600 200 Q 600 225 600 250\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段二：交互筛选 -->\n  <text x=\"600\" y=\"380\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段二：交互筛选与详情查看</text>\n\n  <!-- 节点3: 人才清册 -->\n  <g transform=\"translate(300, 430)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才清册</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">展示符合筛选条件的人员信息</text>\n  </g>\n\n  <!-- 节点4: 详情查看 -->\n  <g transform=\"translate(700, 430)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才详情</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">科研背景、项目、成果等详细信息</text>\n  </g>\n\n  <!-- 连接线 展示 -> 清册 (曲线) -->\n  <path d=\"M 500 340 C 450 370, 400 400, 400 430\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <text x=\"450\" y=\"390\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">点击卡片/区块</text>\n\n  <!-- 连接线 清册 -> 详情 (曲线) -->\n  <path d=\"M 500 465 C 550 465, 650 465, 700 465\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <text x=\"600\" y=\"455\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">点击人员</text>\n\n  <!-- 阶段三：数据操作 -->\n  <text x=\"600\" y=\"580\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段三：数据操作与管理</text>\n  \n  <!-- 节点5: 数据操作 -->\n  <g transform=\"translate(400, 620)\" filter=\"url(#soft-shadow)\">\n      <rect width=\"400\" height=\"80\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\"/>\n      <text x=\"200\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"600\" fill=\"#333\">数据筛选与导出</text>\n      <text x=\"200\" y=\"60\" text-anchor=\"middle\" font-size=\"13\" fill=\"#555\">\n        <tspan dx=\"-120\">精细筛查</tspan>\n        <tspan dx=\"80\">数据导出</tspan>\n        <tspan dx=\"80\">操作日志记录</tspan>\n      </text>\n  </g>\n\n  <!-- 连接线 清册 -> 数据操作 (曲线) -->\n  <path d=\"M 400 500 C 400 550, 450 580, 500 620\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 700 500 C 700 550, 650 580, 600 620\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n</svg>\n```\n\nThis SVG flowchart follows all your design requirements:\n\n1. **Layout**: Divided into three clear stages with proper grouping and spacing\n2. **Color Scheme**: Uses the specified soft color palette for different stages\n3. **Node Style**: Rounded rectangles with shadow effects and two-level text structure\n4. **Connectors**: Smooth Bezier curves with arrowheads and labels where needed\n5. **Typography**: Uses the specified font family with proper sizing and weights\n\nThe flow clearly shows:\n- Stage 1: Data collection and initial display\n- Stage 2: Interactive filtering and detail viewing\n- Stage 3: Data operations and management\n\nEach node includes a bold title and descriptive subtitle, with appropriate connections showing the workflow progression.", "role": "assistant"}}], "created": 1753534501, "id": "021753534437350b70496ab48cdcee516e625f531bc0553f4860a", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 1754, "prompt_tokens": 2306, "total_tokens": 4060, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}