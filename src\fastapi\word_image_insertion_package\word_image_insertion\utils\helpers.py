"""
辅助函数模块

提供日志设置、依赖验证、示例文档创建等辅助功能。
"""

import logging
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from docx import Document
from docx.shared import Inches
from docx.enum.style import WD_STYLE_TYPE


def setup_logging(level: int = logging.INFO, 
                 format_string: Optional[str] = None,
                 file_path: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        format_string: 日志格式字符串
        file_path: 日志文件路径
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置基本配置
    handlers = []
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(format_string))
    handlers.append(console_handler)
    
    # 文件处理器（如果指定）
    if file_path:
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(file_path, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(format_string))
            handlers.append(file_handler)
        except Exception as e:
            print(f"Warning: Failed to setup file logging: {e}")
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        format=format_string,
        handlers=handlers,
        force=True
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('playwright').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)
    
    logger = logging.getLogger('word_image_insertion')
    logger.info(f"Logging setup completed, level: {logging.getLevelName(level)}")
    
    return logger


def validate_dependencies() -> Dict[str, Any]:
    """
    验证项目依赖
    
    Returns:
        Dict: 验证结果
    """
    results = {
        'all_ok': True,
        'python_packages': {},
        'system_tools': {},
        'errors': [],
        'warnings': []
    }
    
    # 检查Python包
    required_packages = [
        ('playwright', 'playwright'),
        ('docx', 'python-docx'),
        ('PIL', 'Pillow'),
        ('requests', 'requests')
    ]
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            results['python_packages'][package_name] = {'status': 'ok', 'version': None}
            
            # 尝试获取版本信息
            try:
                if import_name == 'playwright':
                    import playwright
                    results['python_packages'][package_name]['version'] = playwright.__version__
                elif import_name == 'docx':
                    import docx
                    results['python_packages'][package_name]['version'] = getattr(docx, '__version__', 'unknown')
                elif import_name == 'PIL':
                    from PIL import Image
                    results['python_packages'][package_name]['version'] = getattr(Image, '__version__', 'unknown')
                elif import_name == 'requests':
                    import requests
                    results['python_packages'][package_name]['version'] = getattr(requests, '__version__', 'unknown')
            except:
                pass
                
        except ImportError as e:
            results['python_packages'][package_name] = {'status': 'missing', 'error': str(e)}
            results['errors'].append(f"Missing Python package: {package_name}")
            results['all_ok'] = False
    
    # 检查系统工具
    system_tools = [
        ('libreoffice', ['libreoffice', '--version']),
        ('soffice', ['soffice', '--version']),
    ]
    
    libreoffice_found = False
    for tool_name, cmd in system_tools:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                results['system_tools'][tool_name] = {
                    'status': 'ok', 
                    'version': result.stdout.strip().split('\n')[0] if result.stdout else 'unknown'
                }
                libreoffice_found = True
                break
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            results['system_tools'][tool_name] = {'status': 'not_found'}
    
    if not libreoffice_found:
        results['warnings'].append("LibreOffice not found - PDF preview will not work")
    
    # 检查Playwright浏览器 - 修复异步问题
    try:
        # 检查playwright是否已安装浏览器
        result = subprocess.run([
            sys.executable, '-m', 'playwright', 'install', '--dry-run', 'chromium'
        ], capture_output=True, text=True, timeout=10)

        if 'chromium' in result.stdout.lower() or result.returncode == 0:
            results['system_tools']['chromium'] = {'status': 'ok'}
        else:
            results['system_tools']['chromium'] = {'status': 'not_installed'}
            results['warnings'].append("Chromium browser not available - run 'playwright install chromium'")

    except subprocess.TimeoutExpired:
        results['system_tools']['chromium'] = {'status': 'timeout'}
        results['warnings'].append("Playwright browser check timed out")
    except Exception as e:
        results['system_tools']['chromium'] = {'status': 'error', 'error': str(e)}
        results['warnings'].append("Could not check Playwright browsers - this is normal")
    
    return results


def create_sample_document(output_path: str, include_images: bool = False) -> str:
    """
    创建示例Word文档
    
    Args:
        output_path: 输出路径
        include_images: 是否包含示例图片
        
    Returns:
        str: 创建的文档路径
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建文档
    doc = Document()
    
    # 添加标题
    title = doc.add_heading('Word精准插图技术验证文档', 0)
    
    # 添加简介段落
    intro = doc.add_paragraph('本文档用于测试Word精准插图功能。文档包含多个层级的标题和段落，'
                             '用于验证基于标题样式和关键词的精准定位功能。')
    
    # 添加各级标题和内容
    sections = [
        {
            'level': 1,
            'title': '项目概述',
            'content': '这是一个基于Playwright的Word文档精准插图项目，实现无占位符场景下的自动化图片插入。'
        },
        {
            'level': 2,
            'title': '技术架构',
            'content': '项目采用模块化设计，包含文档解析、图片生成、插图写入和预览管理等核心模块。'
        },
        {
            'level': 3,
            'title': '核心功能',
            'content': '支持多种图片源，包括网页截图、SVG转换和本地图片处理。'
        },
        {
            'level': 4,
            'title': '图片生成',
            'content': '集成Playwright截图功能，支持高分辨率图片生成和尺寸自适应。'
        },
        {
            'level': 5,
            'title': '流程描述',
            'content': '这里是流程描述段落，用于测试关键词匹配功能。系统会在此处自动插入流程图。'
        },
        {
            'level': 2,
            'title': '使用方法',
            'content': '提供简单易用的API接口，支持同步和异步调用方式。'
        },
        {
            'level': 3,
            'title': '基本用法',
            'content': '通过WordImageInserter类可以快速实现图片插入功能。'
        },
        {
            'level': 4,
            'title': '高级配置',
            'content': '支持丰富的配置选项，可以根据需要调整图片质量、尺寸等参数。'
        },
        {
            'level': 5,
            'title': '配置示例',
            'content': '这里展示配置示例，系统可以在此处插入配置截图。'
        },
        {
            'level': 1,
            'title': '测试结果',
            'content': '经过充分测试，系统能够准确定位插入点并生成高质量图片。'
        }
    ]
    
    for section in sections:
        # 添加标题
        heading = doc.add_heading(section['title'], section['level'])
        
        # 添加内容段落
        content_para = doc.add_paragraph(section['content'])
        
        # 为特定标题添加额外内容
        if '流程描述' in section['title']:
            doc.add_paragraph('详细的流程步骤如下：')
            doc.add_paragraph('1. 解析Word文档结构', style='List Number')
            doc.add_paragraph('2. 定位目标插入点', style='List Number')
            doc.add_paragraph('3. 生成高质量图片', style='List Number')
            doc.add_paragraph('4. 插入图片到文档', style='List Number')
            doc.add_paragraph('5. 生成预览文件', style='List Number')
        
        elif '配置示例' in section['title']:
            doc.add_paragraph('主要配置参数包括：')
            doc.add_paragraph('• 图片DPI设置', style='List Bullet')
            doc.add_paragraph('• 最大宽度限制', style='List Bullet')
            doc.add_paragraph('• 设备缩放因子', style='List Bullet')
            doc.add_paragraph('• 输出格式选择', style='List Bullet')
    
    # 添加表格示例
    doc.add_heading('功能对比表', 2)
    table = doc.add_table(rows=1, cols=3)
    table.style = 'Table Grid'
    
    # 表头
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = '功能'
    hdr_cells[1].text = '传统方法'
    hdr_cells[2].text = '本项目'
    
    # 添加数据行
    features = [
        ('定位方式', '手动占位符', '智能定位'),
        ('图片质量', '固定分辨率', '自适应高清'),
        ('批量处理', '不支持', '完全支持'),
        ('预览功能', '需要额外工具', '内置预览')
    ]
    
    for feature, traditional, current in features:
        row_cells = table.add_row().cells
        row_cells[0].text = feature
        row_cells[1].text = traditional
        row_cells[2].text = current
    
    # 添加结论
    doc.add_heading('总结', 1)
    conclusion = doc.add_paragraph('本项目成功实现了Word文档的精准插图功能，'
                                 '为技术文档自动化提供了强有力的支持。'
                                 '通过智能定位和高质量图片生成，'
                                 '大大提高了文档制作效率。')
    
    # 保存文档
    doc.save(output_path)
    
    return str(output_path)


def print_dependency_report(results: Dict[str, Any]):
    """
    打印依赖检查报告
    
    Args:
        results: validate_dependencies()的返回结果
    """
    print("\n" + "="*60)
    print("Word Image Insertion - 依赖检查报告")
    print("="*60)
    
    # 总体状态
    status = "✅ 所有依赖正常" if results['all_ok'] else "❌ 存在依赖问题"
    print(f"\n总体状态: {status}\n")
    
    # Python包状态
    print("Python包:")
    print("-" * 40)
    for package, info in results['python_packages'].items():
        if info['status'] == 'ok':
            version = f" (v{info['version']})" if info['version'] else ""
            print(f"  ✅ {package}{version}")
        else:
            print(f"  ❌ {package} - {info.get('error', 'Not found')}")
    
    # 系统工具状态
    print("\n系统工具:")
    print("-" * 40)
    for tool, info in results['system_tools'].items():
        if info['status'] == 'ok':
            version = f" ({info['version']})" if info.get('version') else ""
            print(f"  ✅ {tool}{version}")
        elif info['status'] == 'not_found':
            print(f"  ⚠️  {tool} - 未找到")
        else:
            print(f"  ❌ {tool} - {info.get('error', 'Error')}")
    
    # 错误信息
    if results['errors']:
        print("\n错误:")
        print("-" * 40)
        for error in results['errors']:
            print(f"  ❌ {error}")
    
    # 警告信息
    if results['warnings']:
        print("\n警告:")
        print("-" * 40)
        for warning in results['warnings']:
            print(f"  ⚠️  {warning}")
    
    # 安装建议
    if not results['all_ok']:
        print("\n安装建议:")
        print("-" * 40)
        
        missing_packages = [pkg for pkg, info in results['python_packages'].items() 
                          if info['status'] != 'ok']
        if missing_packages:
            print(f"  安装Python包: pip install {' '.join(missing_packages)}")
        
        if 'chromium' in results['system_tools'] and results['system_tools']['chromium']['status'] != 'ok':
            print("  安装Playwright浏览器: playwright install chromium")
        
        if not any(info['status'] == 'ok' for info in results['system_tools'].values() 
                  if 'office' in str(info)):
            print("  安装LibreOffice: https://www.libreoffice.org/download/")
    
    print("\n" + "="*60)


def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        Dict: 系统信息
    """
    import platform
    import sys
    
    info = {
        'platform': platform.platform(),
        'system': platform.system(),
        'machine': platform.machine(),
        'python_version': sys.version,
        'python_executable': sys.executable,
    }
    
    try:
        import psutil
        info['memory_total'] = psutil.virtual_memory().total
        info['memory_available'] = psutil.virtual_memory().available
        info['cpu_count'] = psutil.cpu_count()
    except ImportError:
        pass
    
    return info
