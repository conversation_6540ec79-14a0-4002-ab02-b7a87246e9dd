"""
增强预览系统 - PDF与Markdown双视图同步

基于用户提供的架构实现：
1. Word → PDF (带书签导出)
2. Word → Markdown → HTML (带锚点)
3. 双视图同步滚动和导航
4. 高质量渲染和交互体验
"""

import logging
import os
import subprocess
import tempfile
import webbrowser
import json
import re
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
import shutil

logger = logging.getLogger(__name__)


class EnhancedPreviewManager:
    """增强预览管理器 - 支持PDF+Markdown双视图同步"""
    
    def __init__(self, config=None):
        self.config = config
        self.libreoffice_path = self._find_libreoffice()
        self.temp_dir = Path(tempfile.gettempdir()) / 'word_image_insertion_enhanced_preview'
        self.temp_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.temp_dir / 'pdf').mkdir(exist_ok=True)
        (self.temp_dir / 'html').mkdir(exist_ok=True)
        (self.temp_dir / 'markdown').mkdir(exist_ok=True)
        (self.temp_dir / 'viewer').mkdir(exist_ok=True)
    
    def create_dual_view_preview(self, docx_path: str) -> str:
        """
        创建PDF+Markdown双视图预览
        
        Args:
            docx_path: Word文档路径
            
        Returns:
            str: 双视图预览页面路径
        """
        docx_path = Path(docx_path)
        if not docx_path.exists():
            raise FileNotFoundError(f"Document not found: {docx_path}")
        
        logger.info(f"Creating dual-view preview for: {docx_path}")
        
        try:
            # 步骤1: 生成带书签的PDF
            pdf_path = self._generate_pdf_with_bookmarks(docx_path)
            
            # 步骤2: 生成Markdown并转换为HTML
            html_path = self._generate_markdown_html(docx_path)
            
            # 步骤3: 提取书签和锚点映射
            bookmark_mapping = self._extract_bookmark_mapping(pdf_path, html_path)
            
            # 步骤4: 创建双视图同步页面
            viewer_path = self._create_sync_viewer(pdf_path, html_path, bookmark_mapping, docx_path.stem)
            
            logger.info(f"Dual-view preview created: {viewer_path}")
            return str(viewer_path)
            
        except Exception as e:
            logger.error(f"Failed to create dual-view preview: {e}")
            raise
    
    def _generate_pdf_with_bookmarks(self, docx_path: Path) -> str:
        """生成带书签的PDF"""
        if not self.libreoffice_path:
            raise RuntimeError("LibreOffice not found. Please install LibreOffice.")
        
        output_dir = self.temp_dir / 'pdf'
        pdf_path = output_dir / f"{docx_path.stem}.pdf"
        
        try:
            # 使用高级PDF导出选项，包含书签和大纲
            filter_options = (
                "writer_pdf_Export:"
                "ExportOutlines=1;"           # 导出大纲为书签
                "ExportBookmarks=1;"          # 导出书签
                "ExportNotes=0;"              # 不导出注释
                "SelectPdfVersion=1;"         # PDF 1.4版本
                "UseTaggedPDF=1;"             # 使用标记PDF
                "ExportFormFields=0;"         # 不导出表单字段
                "FormsType=0;"                # 表单类型
                "AllowDuplicateFieldNames=0;" # 不允许重复字段名
            )
            
            cmd = [
                str(self.libreoffice_path),
                '--headless',
                '--invisible',
                '--nodefault',
                '--nolockcheck',
                '--nologo',
                '--norestore',
                '--convert-to', f'pdf:{filter_options}',
                '--outdir', str(output_dir),
                str(docx_path)
            ]
            
            logger.debug(f"LibreOffice PDF command: {' '.join(cmd[:8])} ... {docx_path.name}")
            
            # Windows系统特殊处理
            creation_flags = 0
            if os.name == 'nt':
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            with open(os.devnull, 'w') as devnull:
                result = subprocess.run(
                    cmd,
                    stdout=devnull,
                    stderr=subprocess.PIPE,
                    timeout=120,
                    creationflags=creation_flags,
                    text=True
                )
            
            if result.returncode != 0:
                logger.warning(f"PDF with bookmarks failed, trying standard conversion: {result.stderr}")
                # 回退到标准PDF转换
                return self._generate_standard_pdf(docx_path)
            
            if not pdf_path.exists():
                raise RuntimeError("PDF file was not created")
            
            logger.info(f"PDF with bookmarks generated: {pdf_path}")
            return str(pdf_path)
            
        except subprocess.TimeoutExpired:
            logger.error("PDF conversion timed out")
            raise RuntimeError("PDF conversion timed out")
        except Exception as e:
            logger.error(f"Failed to generate PDF with bookmarks: {e}")
            # 回退到标准PDF转换
            return self._generate_standard_pdf(docx_path)
    
    def _generate_standard_pdf(self, docx_path: Path) -> str:
        """生成标准PDF（回退方案）"""
        output_dir = self.temp_dir / 'pdf'
        pdf_path = output_dir / f"{docx_path.stem}.pdf"
        
        cmd = [
            str(self.libreoffice_path),
            '--headless',
            '--convert-to', 'pdf',
            '--outdir', str(output_dir),
            str(docx_path)
        ]
        
        creation_flags = 0
        if os.name == 'nt':
            creation_flags = subprocess.CREATE_NO_WINDOW
        
        with open(os.devnull, 'w') as devnull:
            result = subprocess.run(
                cmd,
                stdout=devnull,
                stderr=devnull,
                timeout=120,
                creationflags=creation_flags
            )
        
        if result.returncode != 0 or not pdf_path.exists():
            raise RuntimeError("Standard PDF conversion also failed")
        
        logger.info(f"Standard PDF generated: {pdf_path}")
        return str(pdf_path)
    
    def _generate_markdown_html(self, docx_path: Path) -> str:
        """生成Markdown并转换为HTML"""
        output_dir = self.temp_dir / 'html'
        html_path = output_dir / f"{docx_path.stem}.html"
        
        try:
            # 首先尝试使用LibreOffice转换为HTML
            cmd = [
                str(self.libreoffice_path),
                '--headless',
                '--convert-to', 'html:HTML:EmbedImages',
                '--outdir', str(output_dir),
                str(docx_path)
            ]
            
            creation_flags = 0
            if os.name == 'nt':
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            with open(os.devnull, 'w') as devnull:
                result = subprocess.run(
                    cmd,
                    stdout=devnull,
                    stderr=devnull,
                    timeout=120,
                    creationflags=creation_flags
                )
            
            if result.returncode == 0 and html_path.exists():
                # 处理HTML，添加锚点
                self._add_anchors_to_html(html_path)
                logger.info(f"Markdown HTML generated: {html_path}")
                return str(html_path)
            else:
                raise RuntimeError("LibreOffice HTML conversion failed")
                
        except Exception as e:
            logger.error(f"Failed to generate Markdown HTML: {e}")
            # 创建一个简单的HTML页面作为回退
            return self._create_fallback_html(docx_path, html_path)
    
    def _add_anchors_to_html(self, html_path: Path):
        """为HTML标题添加锚点"""
        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式为标题添加id属性
            def add_id_to_heading(match):
                tag = match.group(1)
                text = match.group(2)
                # 生成slug id
                slug_id = self._generate_slug(text)
                return f'<{tag} id="{slug_id}">{text}</{tag}>'
            
            # 匹配h1-h6标题
            content = re.sub(
                r'<(h[1-6])>(.*?)</h[1-6]>',
                add_id_to_heading,
                content,
                flags=re.IGNORECASE | re.DOTALL
            )
            
            # 添加CSS样式
            css_style = """
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1, h2, h3, h4, h5, h6 { color: #333; margin-top: 20px; }
                h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }
                h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }
                .anchor-link { text-decoration: none; color: inherit; }
                .anchor-link:hover { color: #007acc; }
            </style>
            """
            
            # 插入CSS到head标签中
            content = content.replace('</head>', f'{css_style}</head>')
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            logger.error(f"Failed to add anchors to HTML: {e}")
    
    def _generate_slug(self, text: str) -> str:
        """生成URL友好的slug"""
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', text)
        # 替换空格为连字符
        text = re.sub(r'\s+', '-', text.strip())
        # 移除多余的连字符
        text = re.sub(r'-+', '-', text)
        return text.strip('-')
    
    def _create_fallback_html(self, docx_path: Path, html_path: Path) -> str:
        """创建回退HTML页面"""
        content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>{docx_path.stem}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .error {{ color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>{docx_path.stem}</h1>
            <div class="error">
                <p>无法转换文档为HTML格式。请检查LibreOffice安装或文档格式。</p>
                <p>文档路径: {docx_path}</p>
            </div>
        </body>
        </html>
        """
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(html_path)
    
    def _extract_bookmark_mapping(self, pdf_path: str, html_path: str) -> Dict[str, str]:
        """提取书签和锚点映射关系"""
        # 这里简化处理，实际可以使用PyPDF2等库提取PDF书签
        # 目前返回一个基本的映射
        mapping = {}
        
        try:
            # 从HTML中提取所有带id的标题
            with open(html_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有带id的标题
            matches = re.findall(r'<h[1-6][^>]*id="([^"]*)"[^>]*>(.*?)</h[1-6]>', content, re.IGNORECASE | re.DOTALL)
            
            for slug_id, title_text in matches:
                # 清理标题文本
                clean_title = re.sub(r'<[^>]+>', '', title_text).strip()
                mapping[clean_title] = slug_id
            
            logger.debug(f"Extracted {len(mapping)} bookmark mappings")
            
        except Exception as e:
            logger.error(f"Failed to extract bookmark mapping: {e}")
        
        return mapping

    def _create_sync_viewer(self, pdf_path: str, html_path: str, bookmark_mapping: Dict[str, str], doc_name: str) -> str:
        """创建双视图同步查看器"""
        viewer_dir = self.temp_dir / 'viewer'
        viewer_html = viewer_dir / f"{doc_name}_dual_view.html"

        # 复制文件到查看器目录
        pdf_copy = viewer_dir / Path(pdf_path).name
        html_copy = viewer_dir / Path(html_path).name

        shutil.copy2(pdf_path, pdf_copy)
        shutil.copy2(html_path, html_copy)

        # 创建双视图HTML页面
        html_content = self._generate_dual_view_html(
            pdf_copy.name, html_copy.name, bookmark_mapping, doc_name
        )

        with open(viewer_html, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Dual-view sync viewer created: {viewer_html}")
        return str(viewer_html)

    def _generate_dual_view_html(self, pdf_name: str, html_name: str, bookmark_mapping: Dict[str, str], doc_name: str) -> str:
        """生成双视图HTML内容"""

        # 将映射转换为JavaScript对象
        mapping_js = json.dumps(bookmark_mapping, ensure_ascii=False)

        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双视图预览 - {doc_name}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }}

        .title {{
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }}

        .title::before {{
            content: "📄";
            font-size: 20px;
        }}

        .controls {{
            display: flex;
            gap: 10px;
            align-items: center;
        }}

        .btn {{
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }}

        .btn:hover {{
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }}

        .sync-indicator {{
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            opacity: 0.8;
        }}

        .sync-dot {{
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }}

        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.5; }}
            100% {{ opacity: 1; }}
        }}

        .container {{
            display: flex;
            height: calc(100vh - 70px);
            gap: 2px;
        }}

        .pane {{
            flex: 1;
            background: white;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            position: relative;
        }}

        .pane-header {{
            background: #34495e;
            color: white;
            padding: 10px 15px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}

        .pane-content {{
            height: calc(100% - 45px);
            overflow: hidden;
        }}

        .pdf-frame, .html-frame {{
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }}

        .resize-handle {{
            width: 4px;
            background: #bdc3c7;
            cursor: col-resize;
            transition: background 0.3s ease;
            position: relative;
        }}

        .resize-handle:hover {{
            background: #3498db;
        }}

        .resize-handle::after {{
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 40px;
            background: rgba(52, 152, 219, 0.3);
            border-radius: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }}

        .resize-handle:hover::after {{
            opacity: 1;
        }}

        .status-bar {{
            background: #ecf0f1;
            padding: 8px 15px;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}

        .loading {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #7f8c8d;
        }}

        .spinner {{
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }}

        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}

        .error {{
            color: #e74c3c;
            background: #fdf2f2;
            padding: 15px;
            border-radius: 5px;
            margin: 20px;
            border-left: 4px solid #e74c3c;
        }}

        @media (max-width: 768px) {{
            .container {{
                flex-direction: column;
            }}

            .resize-handle {{
                width: 100%;
                height: 4px;
                cursor: row-resize;
            }}

            .controls {{
                flex-wrap: wrap;
                gap: 5px;
            }}

            .btn {{
                padding: 6px 12px;
                font-size: 12px;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="title">双视图预览 - {doc_name}</div>
        <div class="controls">
            <div class="sync-indicator">
                <div class="sync-dot"></div>
                <span>同步已启用</span>
            </div>
            <button class="btn" onclick="toggleSync()">切换同步</button>
            <button class="btn" onclick="resetLayout()">重置布局</button>
            <button class="btn" onclick="downloadFiles()">下载文件</button>
            <button class="btn" onclick="window.print()">打印</button>
        </div>
    </div>

    <div class="container">
        <div class="pane" id="pdf-pane">
            <div class="pane-header">
                <span>📄 PDF视图</span>
                <span id="pdf-status">加载中...</span>
            </div>
            <div class="pane-content">
                <div class="loading" id="pdf-loading">
                    <div class="spinner"></div>
                    <div>正在加载PDF...</div>
                </div>
                <iframe class="pdf-frame" id="pdf-frame" src="{pdf_name}#toolbar=1&navpanes=1&scrollbar=1"
                        onload="onPdfLoad()" onerror="onPdfError()"></iframe>
            </div>
        </div>

        <div class="resize-handle" id="resize-handle"></div>

        <div class="pane" id="html-pane">
            <div class="pane-header">
                <span>📝 HTML视图</span>
                <span id="html-status">加载中...</span>
            </div>
            <div class="pane-content">
                <div class="loading" id="html-loading">
                    <div class="spinner"></div>
                    <div>正在加载HTML...</div>
                </div>
                <iframe class="html-frame" id="html-frame" src="{html_name}"
                        onload="onHtmlLoad()" onerror="onHtmlError()"></iframe>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <div>
            <span>📊 状态: </span>
            <span id="sync-status">同步已启用</span>
            <span> | </span>
            <span>🕒 生成时间: {self._get_current_time()}</span>
        </div>
        <div>
            <span>💡 提示: 点击PDF书签或滚动页面时，HTML视图会自动同步</span>
        </div>
    </div>

    <script>
        // 全局变量
        let syncEnabled = true;
        let bookmarkMapping = {mapping_js};
        let isResizing = false;
        let lastScrollTime = 0;
        const SCROLL_THROTTLE = 100; // 滚动节流时间(ms)

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializeResizer();
            setupMessageListeners();
            console.log('双视图预览初始化完成');
            console.log('书签映射:', bookmarkMapping);
        }});

        // 拖拽调整大小
        function initializeResizer() {{
            const resizeHandle = document.getElementById('resize-handle');
            const container = document.querySelector('.container');
            const pdfPane = document.getElementById('pdf-pane');
            const htmlPane = document.getElementById('html-pane');

            resizeHandle.addEventListener('mousedown', function(e) {{
                isResizing = true;
                document.addEventListener('mousemove', handleResize);
                document.addEventListener('mouseup', stopResize);
                e.preventDefault();
            }});

            function handleResize(e) {{
                if (!isResizing) return;

                const containerRect = container.getBoundingClientRect();
                const percentage = ((e.clientX - containerRect.left) / containerRect.width) * 100;

                if (percentage > 20 && percentage < 80) {{
                    pdfPane.style.flex = `0 0 ${{percentage}}%`;
                    htmlPane.style.flex = `0 0 ${{100 - percentage}}%`;
                }}
            }}

            function stopResize() {{
                isResizing = false;
                document.removeEventListener('mousemove', handleResize);
                document.removeEventListener('mouseup', stopResize);
            }}
        }}

        // 消息监听器设置
        function setupMessageListeners() {{
            window.addEventListener('message', function(event) {{
                if (!syncEnabled) return;

                const data = event.data;
                console.log('收到消息:', data);

                if (data.type === 'pdf-nav' || data.type === 'pdf-scroll') {{
                    syncToHtml(data.title);
                }} else if (data.type === 'html-nav' || data.type === 'html-scroll') {{
                    syncToPdf(data.title);
                }}
            }});
        }}

        // 同步到HTML视图
        function syncToHtml(title) {{
            const htmlFrame = document.getElementById('html-frame');
            if (!htmlFrame.contentWindow) return;

            // 查找对应的锚点
            const slugId = bookmarkMapping[title] || generateSlug(title);

            try {{
                htmlFrame.contentWindow.postMessage({{
                    type: 'scroll-to',
                    target: slugId
                }}, '*');

                updateSyncStatus(`同步到HTML: ${{title}}`);
            }} catch (e) {{
                console.error('同步到HTML失败:', e);
            }}
        }}

        // 同步到PDF视图
        function syncToPdf(title) {{
            const pdfFrame = document.getElementById('pdf-frame');
            if (!pdfFrame.contentWindow) return;

            try {{
                pdfFrame.contentWindow.postMessage({{
                    type: 'scroll-to',
                    target: title
                }}, '*');

                updateSyncStatus(`同步到PDF: ${{title}}`);
            }} catch (e) {{
                console.error('同步到PDF失败:', e);
            }}
        }}

        // 生成slug
        function generateSlug(text) {{
            return text.replace(/[^\\w\\u4e00-\\u9fff\\s-]/g, '')
                      .replace(/\\s+/g, '-')
                      .replace(/-+/g, '-')
                      .replace(/^-|-$/g, '');
        }}

        // 更新同步状态
        function updateSyncStatus(message) {{
            const statusElement = document.getElementById('sync-status');
            statusElement.textContent = message;

            // 3秒后恢复默认状态
            setTimeout(() => {{
                statusElement.textContent = syncEnabled ? '同步已启用' : '同步已禁用';
            }}, 3000);
        }}

        // 切换同步
        function toggleSync() {{
            syncEnabled = !syncEnabled;
            const indicator = document.querySelector('.sync-dot');
            const statusText = document.querySelector('.sync-indicator span');

            if (syncEnabled) {{
                indicator.style.background = '#4CAF50';
                statusText.textContent = '同步已启用';
                updateSyncStatus('同步已启用');
            }} else {{
                indicator.style.background = '#f39c12';
                statusText.textContent = '同步已禁用';
                updateSyncStatus('同步已禁用');
            }}
        }}

        // 重置布局
        function resetLayout() {{
            const pdfPane = document.getElementById('pdf-pane');
            const htmlPane = document.getElementById('html-pane');

            pdfPane.style.flex = '1';
            htmlPane.style.flex = '1';

            updateSyncStatus('布局已重置');
        }}

        // 下载文件
        function downloadFiles() {{
            const pdfLink = document.createElement('a');
            pdfLink.href = '{pdf_name}';
            pdfLink.download = '{pdf_name}';
            pdfLink.click();

            updateSyncStatus('PDF下载已开始');
        }}

        // PDF加载完成
        function onPdfLoad() {{
            document.getElementById('pdf-loading').style.display = 'none';
            document.getElementById('pdf-status').textContent = '已加载';
            console.log('PDF加载完成');
        }}

        // PDF加载错误
        function onPdfError() {{
            document.getElementById('pdf-loading').innerHTML =
                '<div class="error">PDF加载失败，请检查文件是否存在</div>';
            document.getElementById('pdf-status').textContent = '加载失败';
        }}

        // HTML加载完成
        function onHtmlLoad() {{
            document.getElementById('html-loading').style.display = 'none';
            document.getElementById('html-status').textContent = '已加载';
            console.log('HTML加载完成');

            // 向HTML iframe注入同步脚本
            injectHtmlSyncScript();
        }}

        // HTML加载错误
        function onHtmlError() {{
            document.getElementById('html-loading').innerHTML =
                '<div class="error">HTML加载失败，请检查文件是否存在</div>';
            document.getElementById('html-status').textContent = '加载失败';
        }}

        // 向HTML iframe注入同步脚本
        function injectHtmlSyncScript() {{
            try {{
                const htmlFrame = document.getElementById('html-frame');
                const htmlDoc = htmlFrame.contentDocument || htmlFrame.contentWindow.document;

                // 创建同步脚本
                const script = htmlDoc.createElement('script');
                script.textContent = `
                    // HTML端同步脚本
                    let lastScrollTime = 0;
                    const SCROLL_THROTTLE = 100;

                    // 监听滚动事件
                    window.addEventListener('scroll', function() {{
                        const now = Date.now();
                        if (now - lastScrollTime < SCROLL_THROTTLE) return;
                        lastScrollTime = now;

                        // 查找当前可见的标题
                        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                        let currentHeading = null;

                        for (let heading of headings) {{
                            const rect = heading.getBoundingClientRect();
                            if (rect.top <= 100 && rect.bottom > 0) {{
                                currentHeading = heading;
                            }}
                        }}

                        if (currentHeading) {{
                            parent.postMessage({{
                                type: 'html-scroll',
                                title: currentHeading.textContent.trim()
                            }}, '*');
                        }}
                    }});

                    // 监听点击事件
                    document.addEventListener('click', function(e) {{
                        const heading = e.target.closest('h1, h2, h3, h4, h5, h6');
                        if (heading) {{
                            parent.postMessage({{
                                type: 'html-nav',
                                title: heading.textContent.trim()
                            }}, '*');
                        }}
                    }});

                    // 监听来自父窗口的消息
                    window.addEventListener('message', function(event) {{
                        if (event.data.type === 'scroll-to') {{
                            const target = document.getElementById(event.data.target);
                            if (target) {{
                                target.scrollIntoView({{ behavior: 'smooth', block: 'start' }});
                            }}
                        }}
                    }});
                `;

                htmlDoc.head.appendChild(script);
                console.log('HTML同步脚本注入成功');

            }} catch (e) {{
                console.error('HTML同步脚本注入失败:', e);
            }}
        }}

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {{
            if (e.ctrlKey) {{
                switch(e.key) {{
                    case 'p':
                        e.preventDefault();
                        window.print();
                        break;
                    case 's':
                        e.preventDefault();
                        toggleSync();
                        break;
                    case 'r':
                        e.preventDefault();
                        resetLayout();
                        break;
                }}
            }}
        }});

        // 窗口大小改变时调整布局
        window.addEventListener('resize', function() {{
            // 可以在这里添加响应式布局调整
        }});
    </script>
</body>
</html>
        """

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _find_libreoffice(self) -> Optional[Path]:
        """查找LibreOffice安装路径"""
        # 复用原有的查找逻辑
        for cmd in ['soffice', 'libreoffice']:
            try:
                result = subprocess.run([cmd, '--version'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    return Path(cmd)
            except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                continue

        # Windows路径检查
        if os.name == 'nt':
            windows_paths = [
                r"C:\Program Files\LibreOffice\program\soffice.exe",
                r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            ]

            for path_str in windows_paths:
                path = Path(path_str)
                if path.exists():
                    try:
                        result = subprocess.run([str(path), '--version'],
                                              capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            return path
                    except:
                        continue

        return None

    def open_preview(self, preview_path: str) -> bool:
        """在浏览器中打开预览"""
        try:
            preview_path = Path(preview_path)
            if not preview_path.exists():
                logger.error(f"Preview file not found: {preview_path}")
                return False

            webbrowser.open(f"file://{preview_path.absolute()}")
            logger.info(f"Enhanced preview opened: {preview_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to open preview: {e}")
            return False
