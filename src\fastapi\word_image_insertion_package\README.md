# Word精准插图系统

一个强大的Word文档智能插图系统，支持SVG/HTML转高清图片并精准插入到指定章节位置。

## ✨ 核心功能

- 🎯 **智能章节识别**: 自动识别文档结构，精准定位插入位置
- 🎨 **高质量图片生成**: SVG/HTML转高清PNG，支持自定义分辨率
- 📄 **增强预览功能**: PDF+HTML双视图同步，现代化交互界面
- 🔧 **通用配置支持**: 灵活的章节匹配规则和参数配置
- 📱 **跨平台支持**: Windows/Linux/macOS全平台兼容

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 安装LibreOffice（用于预览功能）
# Windows: https://www.libreoffice.org/download/
# Linux: sudo apt-get install libreoffice
# macOS: brew install --cask libreoffice
```

### 2. 基础使用

```python
from word_image_insertion import WordImageInserter, Config
import asyncio

async def main():
    # 创建插图器
    config = Config()
    inserter = WordImageInserter(config)
    
    # 查找插入点
    targets = inserter.parse_document("document.docx", keyword_pattern="业务流程")
    
    # 生成图片
    image_path = await inserter.image_generator.generate_async("flowchart.svg")
    
    # 插入图片
    final_doc = inserter.insert_image("document.docx", targets[0], image_path, "output.docx")

asyncio.run(main())
```

### 3. 预览功能

```python
from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager

# 创建增强预览
preview_manager = EnhancedPreviewManager()
viewer_path = preview_manager.create_dual_view_preview("document.docx")
preview_manager.open_preview(viewer_path)
```

## 📁 目录结构

```
word_image_insertion_package/
├── word_image_insertion/          # 核心功能包
├── examples/                      # 使用示例
├── input/                         # 输入文件目录
├── output/                        # 输出文件目录
├── requirements.txt               # 依赖包列表
└── README.md                      # 说明文档
```

## 🔧 配置说明

复制 `word_image_insertion/config_template.py` 为 `config_custom.py` 进行自定义配置：

- 章节匹配关键词
- 图片类型优先级  
- 图片尺寸设置
- 文件路径配置

## 📖 更多示例

查看 `examples/` 目录中的示例文件：

- `basic_usage.py` - 基础使用示例
- `preview_demo.py` - 预览功能示例

## 🆘 技术支持

如有问题，请检查：

1. Python版本 >= 3.8
2. LibreOffice是否正确安装
3. Playwright浏览器是否安装
4. 文件路径是否正确

## 📄 许可证

MIT License
