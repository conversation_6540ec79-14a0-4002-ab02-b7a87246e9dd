<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- SLOT::header::BEGIN -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <!-- CHUNK::header-title::BEGIN -->
                    <h1 class="text-3xl font-bold text-gray-900">科技项目管理</h1>
                    <!-- CHUNK::header-title::END -->
                    <!-- CHUNK::header-subtitle::BEGIN -->
                    <p class="mt-2 text-sm text-gray-600">科技项目全生命周期管理平台</p>
                    <!-- CHUNK::header-subtitle::END -->
                </div>
                <div class="flex space-x-3">
                    <!-- CHUNK::header-export-button::BEGIN -->
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        数据导出
                    </button>
                    <!-- CHUNK::header-export-button::END -->
                    <!-- CHUNK::header-refresh-button::BEGIN -->
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                    <!-- CHUNK::header-refresh-button::END -->
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::header::END -->

    <!-- SLOT::main-content::BEGIN -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- SLOT::filters::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <!-- CHUNK::filters-title::BEGIN -->
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                高级检索
            </h2>
            <!-- CHUNK::filters-title::END -->
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- CHUNK::filter-project-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
                    <input type="text" placeholder="请输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <!-- CHUNK::filter-project-name::END -->
                
                <!-- CHUNK::filter-company::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">承担单位</label>
                    <input type="text" placeholder="请输入承担单位" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <!-- CHUNK::filter-company::END -->
                
                <!-- CHUNK::filter-credit-code::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                    <input type="text" placeholder="请输入信用代码" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <!-- CHUNK::filter-credit-code::END -->
                
                <!-- CHUNK::filter-batch::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申报批次</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">全部批次</option>
                        <option value="2023-1">2023年第一批</option>
                        <option value="2023-2">2023年第二批</option>
                    </select>
                </div>
                <!-- CHUNK::filter-batch::END -->
                
                <!-- CHUNK::filter-status::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">全部状态</option>
                        <option value="declared">已申报</option>
                        <option value="approved">已立项</option>
                        <option value="in-progress">实施中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                <!-- CHUNK::filter-status::END -->
                
                <!-- CHUNK::filter-year::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申报年度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">全部年度</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                    </select>
                </div>
                <!-- CHUNK::filter-year::END -->
            </div>
            
            <div class="flex space-x-3 mt-4">
                <!-- CHUNK::filter-reset-button::BEGIN -->
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置筛选
                </button>
                <!-- CHUNK::filter-reset-button::END -->
                
                <!-- CHUNK::filter-search-button::BEGIN -->
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <!-- CHUNK::filter-search-button::END -->
            </div>
        </div>
        <!-- SLOT::filters::END -->
        
        <!-- SLOT::project-list::BEGIN -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <!-- CHUNK::list-title::BEGIN -->
                    <h3 class="text-lg font-semibold text-gray-900">科技项目列表</h3>
                    <!-- CHUNK::list-title::END -->
                    
                    <div class="flex space-x-3">
                        <!-- CHUNK::list-add-button::BEGIN -->
                        <button onclick="openModal('addProjectModal')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增项目
                        </button>
                        <!-- CHUNK::list-add-button::END -->
                        
                        <!-- CHUNK::list-import-button::BEGIN -->
                        <button onclick="openModal('importModal')" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导入
                        </button>
                        <!-- CHUNK::list-import-button::END -->
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目属性</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::project-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研发</div>
                                <div class="text-sm text-gray-500">国家级重点研发计划</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB2023ZD001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01 ~ 2025-12</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">实施中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::project-row-1::END -->
                        
                        <!-- CHUNK::project-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新材料产业化应用研究</div>
                                <div class="text-sm text-gray-500">省级重点研发计划</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZJ2023ZD002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX材料研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03 ~ 2024-12</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已立项</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::project-row-2::END -->
                    </tbody>
                </table>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-2 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::project-list::END -->
    </div>
    <!-- SLOT::main-content::END -->
    
    <!-- SLOT::add-project-modal::BEGIN -->
    <div id="addProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">新增科技项目</h3>
                    <button onclick="closeModal('addProjectModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <form>
                        <div class="space-y-6">
                            <!-- CHUNK::modal-basic-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 <span class="text-red-500">*</span></label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">项目编号</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">项目属性 <span class="text-red-500">*</span></label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                            <option value="">请选择项目属性</option>
                                            <option value="key">重点研发</option>
                                            <option value="normal">一般项目</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">项目级别 <span class="text-red-500">*</span></label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                            <option value="">请选择项目级别</option>
                                            <option value="national">国家级</option>
                                            <option value="provincial">省级</option>
                                            <option value="municipal">市级</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::modal-basic-info::END -->
                            
                            <!-- CHUNK::modal-team-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">负责人与团队</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人 <span class="text-red-500">*</span></label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">联系电话 <span class="text-red-500">*</span></label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">承担单位 <span class="text-red-500">*</span></label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::modal-team-info::END -->
                            
                            <!-- CHUNK::modal-time-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">时间与经费</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">开始时间 <span class="text-red-500">*</span></label>
                                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">结束时间 <span class="text-red-500">*</span></label>
                                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">总经费（万元）</label>
                                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">财政拨款（万元）</label>
                                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::modal-time-info::END -->
                            
                            <!-- CHUNK::modal-attachment::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">附件上传</h4>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <div class="mt-2">
                                        <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>点击上传文件</span>
                                            <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                        </label>
                                        <p class="text-xs text-gray-500">支持PDF、Word、Excel格式</p>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::modal-attachment::END -->
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-6">
                            <button type="button" onclick="closeModal('addProjectModal')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::add-project-modal::END -->
    
    <!-- SLOT::detail-modal::BEGIN -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="lg:col-span-2">
                            <!-- CHUNK::detail-basic-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">项目名称：</span>
                                        <span class="font-medium text-gray-900">宁波市智能制造关键技术研发</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目编号：</span>
                                        <span class="font-medium text-gray-900">NB2023ZD001</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目属性：</span>
                                        <span class="font-medium text-gray-900">重点研发</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目级别：</span>
                                        <span class="font-medium text-gray-900">国家级</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">承担单位：</span>
                                        <span class="font-medium text-gray-900">宁波市XX科技有限公司</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">信用代码：</span>
                                        <span class="font-medium text-gray-900">91330201MA2XXXXXX</span>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-basic-info::END -->
                            
                            <!-- CHUNK::detail-team-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">负责人与团队</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">项目负责人：</span>
                                        <span class="font-medium text-gray-900">张研究员</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">联系电话：</span>
                                        <span class="font-medium text-gray-900">13888888888</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">团队成员：</span>
                                        <span class="font-medium text-gray-900">李工程师、王博士等5人</span>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-team-info::END -->
                            
                            <!-- CHUNK::detail-time-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">时间与经费</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">开始时间：</span>
                                        <span class="font-medium text-gray-900">2023-01-15</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">结束时间：</span>
                                        <span class="font-medium text-gray-900">2025-12-31</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">总经费：</span>
                                        <span class="font-medium text-gray-900">500万元</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">财政拨款：</span>
                                        <span class="font-medium text-gray-900">200万元</span>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-time-info::END -->
                            
                            <!-- CHUNK::detail-progress-info::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">项目进展</h4>
                                <div class="space-y-4">
                                    <div>
                                        <div class="flex justify-between mb-1">
                                            <span class="text-sm font-medium text-gray-700">项目进度</span>
                                            <span class="text-sm font-medium text-gray-700">35%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: 35%"></div>
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-700">
                                        <p>• 已完成需求调研和方案设计</p>
                                        <p>• 正在进行关键技术研发</p>
                                        <p>• 已申请专利2项</p>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-progress-info::END -->
                        </div>
                        
                        <div>
                            <!-- CHUNK::detail-attachment::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">相关附件</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span class="text-sm">项目申报书.pdf</span>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                                    </div>
                                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span class="text-sm">中期报告.docx</span>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-attachment::END -->
                            
                            <!-- CHUNK::detail-actions::BEGIN -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">操作</h4>
                                <div class="space-y-3">
                                    <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                        导出项目报告
                                    </button>
                                    <button class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                                        查看历史变更
                                    </button>
                                    <button class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                                        关联科技成果
                                    </button>
                                </div>
                            </div>
                            <!-- CHUNK::detail-actions::END -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::detail-modal::END -->
    
    <!-- SLOT::import-modal::BEGIN -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入科技项目</h3>
                    <button onclick="closeModal('importModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- CHUNK::import-download::BEGIN -->
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                下载导入模板
                            </a>
                            <span class="text-xs text-gray-500">支持.xlsx格式</span>
                        </div>
                        <!-- CHUNK::import-download::END -->
                        
                        <!-- CHUNK::import-upload::BEGIN -->
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label for="import-file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="import-file" name="import-file" type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                            </div>
                        </div>
                        <!-- CHUNK::import-upload::END -->
                        
                        <!-- CHUNK::import-progress::BEGIN -->
                        <div id="importProgress" class="hidden">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span class="text-sm font-medium text-gray-700">75%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <!-- CHUNK::import-progress::END -->
                        
                        <!-- CHUNK::import-result::BEGIN -->
                        <div id="importResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                    <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                        <li>第3行：项目编号已存在</li>
                                        <li>第5行：项目名称为必填项</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- CHUNK::import-result::END -->
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-6">
                        <button type="button" onclick="closeModal('importModal')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::import-modal::END -->
    
    <script>
        // --- 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 文件上传处理
            document.getElementById('import-file').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    document.getElementById('importProgress').classList.remove('hidden');
                    setTimeout(function() {
                        document.getElementById('importResult').classList.remove('hidden');
                    }, 2000);
                }
            });
            
            // 表单提交处理
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (原型演示)');
                    const parentModalId = form.closest('[id$="Modal"]').id;
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>