#!/usr/bin/env python3
"""
测试所有修复的功能
"""

import os
import requests
import json
import time

def test_document_upload_and_save():
    """测试文档上传和保存功能"""
    print("🧪 测试1: 文档上传和保存")
    print("=" * 50)
    
    # 创建一个简单的测试文档
    test_content = """
    一、系统概述
    本系统是一个投标文件管理系统。
    
    二、功能模块
    2.1 用户管理
    用户登录、注册、权限管理等功能。
    
    三、业务流程
    3.1 投标流程
    投标文件上传、审核、评分等流程。
    
    四、操作流程
    4.1 系统操作
    详细的操作步骤说明。
    """
    
    # 创建临时Word文档（模拟）
    test_doc_path = "test_document.txt"  # 使用txt文件模拟
    with open(test_doc_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # 测试文档上传（这里用txt文件模拟，实际应该是docx）
        print("📤 模拟文档上传...")
        
        # 检查uploads目录
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
            print(f"✅ 创建uploads目录: {uploads_dir}")
        
        # 模拟保存文档到uploads目录
        import uuid
        file_id = str(uuid.uuid4())[:8]
        saved_filename = f"{file_id}_投标文件-0612.docx"
        saved_doc_path = os.path.join(uploads_dir, saved_filename)
        
        # 复制测试文件到uploads目录
        import shutil
        shutil.copy2(test_doc_path, saved_doc_path)
        
        print(f"✅ 文档已保存到: {saved_doc_path}")
        print(f"📏 文件大小: {os.path.getsize(saved_doc_path)} 字节")
        
        return saved_doc_path
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return None
    finally:
        # 清理临时文件
        if os.path.exists(test_doc_path):
            os.remove(test_doc_path)

def test_file_download_api(doc_path):
    """测试文件下载API"""
    print("\n🧪 测试2: 文件下载API")
    print("=" * 50)
    
    if not doc_path or not os.path.exists(doc_path):
        print("❌ 测试文档不存在，跳过下载测试")
        return False
    
    try:
        # 测试新的下载端点
        import urllib.parse
        encoded_path = urllib.parse.quote(doc_path)
        download_url = f"http://localhost:8000/api/files/download/{encoded_path}"
        
        print(f"🌐 测试下载URL: {download_url}")
        
        response = requests.get(download_url)
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 文件下载API测试成功")
            print(f"📊 响应头: {dict(response.headers)}")
            return True
        else:
            print(f"❌ 文件下载失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 下载测试失败: {str(e)}")
        return False

def test_batch_insert_api(doc_path):
    """测试批量插入API"""
    print("\n🧪 测试3: 批量插入API")
    print("=" * 50)
    
    if not doc_path or not os.path.exists(doc_path):
        print("❌ 测试文档不存在，跳过批量插入测试")
        return False
    
    try:
        # 创建测试的generated_files目录和文件
        generated_files_dir = "generated_files"
        if not os.path.exists(generated_files_dir):
            os.makedirs(generated_files_dir)
            print(f"✅ 创建generated_files目录: {generated_files_dir}")
        
        # 创建测试SVG文件
        test_svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect x="50" y="50" width="100" height="60" fill="lightblue" stroke="black"/>
  <text x="100" y="85" text-anchor="middle">测试流程图</text>
</svg>'''
        
        svg_file_path = os.path.join(generated_files_dir, "flowchart_test-task-1_20250728.svg")
        with open(svg_file_path, 'w', encoding='utf-8') as f:
            f.write(test_svg_content)
        print(f"✅ 创建测试SVG文件: {svg_file_path}")
        
        # 创建测试HTML文件
        test_html_content = '''<!DOCTYPE html>
<html>
<head><title>测试UI原型</title></head>
<body>
  <h1>测试UI原型</h1>
  <p>这是一个测试的UI原型页面</p>
</body>
</html>'''
        
        html_file_path = os.path.join(generated_files_dir, "ui_prototype_test-task-2_20250728.html")
        with open(html_file_path, 'w', encoding='utf-8') as f:
            f.write(test_html_content)
        print(f"✅ 创建测试HTML文件: {html_file_path}")
        
        # 测试批量插入API
        api_url = "http://localhost:8000/api/word/batch-insert"
        request_data = {
            "doc_path": doc_path,
            "task_results": [
                {
                    "id": "test-task-1",
                    "type": "flowchart",
                    "status": "completed"
                },
                {
                    "id": "test-task-2", 
                    "type": "ui_prototype",
                    "status": "completed"
                }
            ],
            "config": {
                "business_flow_pattern": "三、业务流程",
                "operation_flow_pattern": "四、操作流程"
            }
        }
        
        print("🌐 发送批量插入请求...")
        response = requests.post(
            api_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(request_data)
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量插入API测试成功")
            print(f"📊 插入结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get('output_path')
        else:
            print(f"❌ 批量插入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 批量插入测试失败: {str(e)}")
        return None

def test_preview_api(doc_path):
    """测试预览API"""
    print("\n🧪 测试4: 预览API")
    print("=" * 50)
    
    if not doc_path or not os.path.exists(doc_path):
        print("❌ 测试文档不存在，跳过预览测试")
        return False
    
    try:
        api_url = "http://localhost:8000/api/word/preview"
        request_data = {
            "doc_path": doc_path,
            "preview_type": "pdf"
        }
        
        print("🌐 发送预览请求...")
        response = requests.post(
            api_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(request_data)
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 预览API调用成功")
            print(f"📊 预览结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"⚠️ 预览API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            print("这是预期的，因为LibreOffice未安装")
            return False
            
    except Exception as e:
        print(f"❌ 预览测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试所有修复的功能...")
    print("=" * 80)
    
    # 测试1: 文档上传和保存
    saved_doc_path = test_document_upload_and_save()
    
    if saved_doc_path:
        # 测试2: 文件下载API
        test_file_download_api(saved_doc_path)
        
        # 测试3: 批量插入API
        output_path = test_batch_insert_api(saved_doc_path)
        
        # 测试4: 预览API
        test_preview_api(output_path or saved_doc_path)
    
    print("\n🏁 所有测试完成")
    print("=" * 80)
