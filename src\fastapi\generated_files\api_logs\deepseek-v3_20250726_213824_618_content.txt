=== API调用成功 ===
时间: 2025-07-26 21:39:35
模型: deepseek-v3
内容长度: 5439 字符

=== 生成内容 ===
Here's the professional, hierarchical, and aesthetically pleasing SVG flowchart based on your requirements:

```svg
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- Definitions -->
  <defs>
    <!-- Arrow style -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- Shadow filter -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="white" />

  <!-- Main Title -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技人才数据管理系统流程</text>

  <!-- Stage 1: Data Aggregation -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与展示</text>
  
  <!-- Node 1: Data Aggregation -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">全市科技人才数据汇聚</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">自动汇聚各类人才基础数据，实时刷新概览卡片和分布大屏</text>
  </g>

  <!-- Stage 2: Data Interaction -->
  <text x="600" y="240" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据交互与筛选</text>

  <!-- Node 2: Category Selection -->
  <g transform="translate(400, 270)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才类别选择</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">用户通过类型切换区选择感兴趣的人才类别，系统刷新数据</text>
  </g>

  <!-- Node 3: Talent List -->
  <g transform="translate(400, 370)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才清册展示</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">点击卡片或区块后展示符合条件的人员信息列表</text>
  </g>

  <!-- Stage 3: Detailed View -->
  <text x="600" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详细信息与操作</text>

  <!-- Node 4: Talent Details -->
  <g transform="translate(300, 510)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才详情查看</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">展示科研背景、项目、成果等详细资料</text>
  </g>

  <!-- Node 5: Data Operations -->
  <g transform="translate(600, 510)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据筛选与导出</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">精细筛查或导出选定人员信息，记录操作日志</text>
  </g>

  <!-- Connection lines -->
  <path d="M 600 200 Q 600 215 600 230" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 340 Q 600 355 600 370" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 440 C 450 460, 300 480, 300 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 440 C 750 460, 900 480, 900 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- Legend -->
  <rect x="50" y="650" width="20" height="20" rx="4" ry="4" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
  <text x="80" y="665" font-size="14" fill="#333">数据汇聚</text>
  
  <rect x="200" y="650" width="20" height="20" rx="4" ry="4" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
  <text x="230" y="665" font-size="14" fill="#333">交互筛选</text>
  
  <rect x="350" y="650" width="20" height="20" rx="4" ry="4" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
  <text x="380" y="665" font-size="14" fill="#333">列表展示</text>
  
  <rect x="500" y="650" width="20" height="20" rx="4" ry="4" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
  <text x="530" y="665" font-size="14" fill="#333">详情与操作</text>
</svg>
```

This SVG flowchart features:

1. **Clear Hierarchy**: Divided into three distinct stages with labeled section headers
2. **Consistent Styling**: 
   - Soft color scheme with different colors for each stage
   - Rounded rectangles with subtle shadows
   - Clean typography with title/description structure

3. **Logical Flow**:
   - Data aggregation → Category selection → Talent list → Details/Operations
   - Smooth Bezier curves connecting nodes

4. **Professional Elements**:
   - Legend explaining the color coding
   - Proper spacing and alignment
   - Responsive viewBox dimensions

The flowchart visually represents the complete workflow from data collection to detailed talent analysis while maintaining a clean, professional appearance.