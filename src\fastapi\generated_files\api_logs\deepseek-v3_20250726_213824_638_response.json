{"timestamp": "2025-07-26 21:42:36", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Sat, 26 Jul 2025 13:42:46 GMT", "content-type": "application/json; charset=utf-8", "content-length": "32994", "x-client-request-id": "unknown-20250726213837-yYZlzCbT", "x-envoy-upstream-service-time": "250195", "x-request-id": "021753537116972c6f5b907bfcacf189664f83e123db882c13056"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>科技人才总数概览</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#3b82f6',\n                        secondary: '#64748b'\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <div class=\"container mx-auto px-4 py-6\">\n        <!-- SLOT::header::BEGIN -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-3xl font-bold text-gray-900 mb-2\">科技人才总数概览</h1>\n            <p class=\"text-gray-600\">宁波市科技人才数据总览与分析</p>\n        </div>\n        <!-- SLOT::header::END -->\n\n        <!-- SLOT::filter-section::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-lg font-semibold text-gray-900 mb-4\">筛选条件</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">人才类型</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\n                        <option>全部类型</option>\n                        <option>研发人员</option>\n                        <option>科研管理人员</option>\n                        <option>专家</option>\n                        <option>科技特派员</option>\n                    </select>\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">所属区域</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\n                        <option>全市</option>\n                        <option>海曙区</option>\n                        <option>江北区</option>\n                        <option>鄞州区</option>\n                        <option>余姚市</option>\n                    </select>\n                </div>\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">行业领域</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\n                        <option>全部行业</option>\n                        <option>电子信息</option>\n                        <option>生物医药</option>\n                        <option>新材料</option>\n                        <option>智能制造</option>\n                    </select>\n                </div>\n            </div>\n            <div class=\"flex justify-end mt-4\">\n                <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">查询</button>\n            </div>\n        </div>\n        <!-- SLOT::filter-section::END -->\n\n        <!-- SLOT::cards-section::BEGIN -->\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <!-- CHUNK::card-rd-personnel::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center justify-between\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">研发人员</h3>\n                    <div class=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4\">\n                    <p class=\"text-3xl font-bold text-gray-900\">12,847</p>\n                    <p class=\"text-sm text-gray-500 mt-2\">同比增长 8.5%</p>\n                </div>\n            </div>\n            <!-- CHUNK::card-rd-personnel::END -->\n\n            <!-- CHUNK::card-research-manager::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center justify-between\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">科研管理人员</h3>\n                    <div class=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4\">\n                    <p class=\"text-3xl font-bold text-gray-900\">3,245</p>\n                    <p class=\"text-sm text-gray-500 mt-2\">同比增长 5.2%</p>\n                </div>\n            </div>\n            <!-- CHUNK::card-research-manager::END -->\n\n            <!-- CHUNK::card-expert::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center justify-between\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">专家</h3>\n                    <div class=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4\">\n                    <p class=\"text-3xl font-bold text-gray-900\">1,876</p>\n                    <p class=\"text-sm text-gray-500 mt-2\">同比增长 12.3%</p>\n                </div>\n            </div>\n            <!-- CHUNK::card-expert::END -->\n\n            <!-- CHUNK::card-tech-specialist::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center justify-between\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">科技特派员</h3>\n                    <div class=\"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4\">\n                    <p class=\"text-3xl font-bold text-gray-900\">856</p>\n                    <p class=\"text-sm text-gray-500 mt-2\">同比增长 15.7%</p>\n                </div>\n            </div>\n            <!-- CHUNK::card-tech-specialist::END -->\n        </div>\n        <!-- SLOT::cards-section::END -->\n\n        <!-- SLOT::chart-section::BEGIN -->\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-lg font-semibold text-gray-900 mb-4\">人才区域分布</h2>\n                <div class=\"h-80\">\n                    <canvas id=\"regionChart\"></canvas>\n                </div>\n            </div>\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-lg font-semibold text-gray-900 mb-4\">人才行业分布</h2>\n                <div class=\"h-80\">\n                    <canvas id=\"industryChart\"></canvas>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::chart-section::END -->\n\n        <!-- SLOT::talent-list::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <div class=\"flex justify-between items-center\">\n                    <h2 class=\"text-lg font-semibold text-gray-900\">人才列表</h2>\n                    <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">导出数据</button>\n                </div>\n            </div>\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">姓名</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">类型</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">所属单位</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">行业领域</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">年龄</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <!-- CHUNK::talent-row-1::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">张三</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">研发人员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX科技有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">电子信息</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">35</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                <button onclick=\"openDetailModal('1')\" class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::talent-row-1::END -->\n                        <!-- CHUNK::talent-row-2::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">李四</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">科研管理人员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX研究院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">生物医药</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">42</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                <button onclick=\"openDetailModal('2')\" class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::talent-row-2::END -->\n                        <!-- CHUNK::talent-row-3::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">王五</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">专家</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波大学</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">新材料</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">56</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                <button onclick=\"openDetailModal('3')\" class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::talent-row-3::END -->\n                        <!-- CHUNK::talent-row-4::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">赵六</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">科技特派员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX农业科技公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">农业科技</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">38</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                <button onclick=\"openDetailModal('4')\" class=\"text-blue-600 hover:text-blue-900\">查看详情</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::talent-row-4::END -->\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-500\">\n                        显示第 1-4 条，共 128 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700\">1</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::talent-list::END -->\n    </div>\n\n    <!-- SLOT::detail-modal::BEGIN -->\n    <div id=\"detailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full\">\n        <div class=\"relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">人才详情</h3>\n                    <button onclick=\"closeDetailModal()\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div class=\"md:col-span-1\">\n                        <!-- CHUNK::detail-basic-info::BEGIN -->\n                        <div class=\"bg-gray-50 p-4 rounded-lg\">\n                            <div class=\"flex items-center space-x-4 mb-4\">\n                                <div class=\"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center\">\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                    </svg>\n                                </div>\n                                <div>\n                                    <h4 class=\"text-xl font-bold text-gray-900\" id=\"detail-name\">张三</h4>\n                                    <p class=\"text-sm text-gray-500\" id=\"detail-type\">研发人员</p>\n                                </div>\n                            </div>\n                            <div class=\"space-y-3 text-sm\">\n                                <div>\n                                    <span class=\"text-gray-500\">所属单位：</span>\n                                    <span class=\"font-medium text-gray-900\" id=\"detail-company\">宁波市XX科技有限公司</span>\n                                </div>\n                                <div>\n                                    <span class=\"text-gray-500\">行业领域：</span>\n                                    <span class=\"font-medium text-gray-900\" id=\"detail-industry\">电子信息</span>\n                                </div>\n                                <div>\n                                    <span class=\"text-gray-500\">年龄：</span>\n                                    <span class=\"font-medium text-gray-900\" id=\"detail-age\">35</span>\n                                </div>\n                                <div>\n                                    <span class=\"text-gray-500\">学历：</span>\n                                    <span class=\"font-medium text-gray-900\" id=\"detail-education\">博士</span>\n                                </div>\n                                <div>\n                                    <span class=\"text-gray-500\">职称：</span>\n                                    <span class=\"font-medium text-gray-900\" id=\"detail-title\">高级工程师</span>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::detail-basic-info::END -->\n                    </div>\n                    <div class=\"md:col-span-2\">\n                        <!-- CHUNK::detail-project-info::BEGIN -->\n                        <div class=\"bg-gray-50 p-4 rounded-lg mb-4\">\n                            <h4 class=\"text-md font-semibold text-gray-900 mb-3\">参与项目</h4>\n                            <div class=\"space-y-2\">\n                                <div class=\"border-b pb-2\">\n                                    <p class=\"font-medium text-gray-900\">宁波市智能制造关键技术研发</p>\n                                    <p class=\"text-sm text-gray-500\">2022-2024 | 项目负责人</p>\n                                </div>\n                                <div class=\"border-b pb-2\">\n                                    <p class=\"font-medium text-gray-900\">新一代信息技术应用研究</p>\n                                    <p class=\"text-sm text-gray-500\">2021-2023 | 核心成员</p>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::detail-project-info::END -->\n                        \n                        <!-- CHUNK::detail-award-info::BEGIN -->\n                        <div class=\"bg-gray-50 p-4 rounded-lg\">\n                            <h4 class=\"text-md font-semibold text-gray-900 mb-3\">获奖情况</h4>\n                            <div class=\"space-y-2\">\n                                <div class=\"border-b pb-2\">\n                                    <p class=\"font-medium text-gray-900\">宁波市科技进步一等奖</p>\n                                    <p class=\"text-sm text-gray-500\">2023年</p>\n                                </div>\n                                <div class=\"border-b pb-2\">\n                                    <p class=\"font-medium text-gray-900\">浙江省优秀科技工作者</p>\n                                    <p class=\"text-sm text-gray-500\">2022年</p>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::detail-award-info::END -->\n                    </div>\n                </div>\n                <div class=\"flex justify-end mt-6\">\n                    <button onclick=\"closeDetailModal()\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50\">关闭</button>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::detail-modal::END -->\n\n    <script>\n        // 全局UI控制函数\n        function openDetailModal(id) {\n            const modal = document.getElementById('detailModal');\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden';\n                \n                // 模拟根据ID加载数据\n                const talentData = getTalentDataById(id);\n                if (talentData) {\n                    document.getElementById('detail-name').textContent = talentData.name;\n                    document.getElementById('detail-type').textContent = talentData.type;\n                    document.getElementById('detail-company').textContent = talentData.company;\n                    document.getElementById('detail-industry').textContent = talentData.industry;\n                    document.getElementById('detail-age').textContent = talentData.age;\n                    document.getElementById('detail-education').textContent = talentData.education;\n                    document.getElementById('detail-title').textContent = talentData.title;\n                }\n            }\n        }\n\n        function closeDetailModal() {\n            const modal = document.getElementById('detailModal');\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto';\n            }\n        }\n\n        // 模拟数据获取\n        function getTalentDataById(id) {\n            const data = {\n                '1': {\n                    name: '张三',\n                    type: '研发人员',\n                    company: '宁波市XX科技有限公司',\n                    industry: '电子信息',\n                    age: 35,\n                    education: '博士',\n                    title: '高级工程师'\n                },\n                '2': {\n                    name: '李四',\n                    type: '科研管理人员',\n                    company: '宁波市XX研究院',\n                    industry: '生物医药',\n                    age: 42,\n                    education: '硕士',\n                    title: '研究员'\n                },\n                '3': {\n                    name: '王五',\n                    type: '专家',\n                    company: '宁波大学',\n                    industry: '新材料',\n                    age: 56,\n                    education: '博士',\n                    title: '教授'\n                },\n                '4': {\n                    name: '赵六',\n                    type: '科技特派员',\n                    company: '宁波市XX农业科技公司',\n                    industry: '农业科技',\n                    age: 38,\n                    education: '本科',\n                    title: '农艺师'\n                }\n            };\n            return data[id] || null;\n        }\n\n        // 初始化图表\n        document.addEventListener('DOMContentLoaded', function() {\n            // 区域分布图表\n            const regionCtx = document.getElementById('regionChart').getContext('2d');\n            new Chart(regionCtx, {\n                type: 'bar',\n                data: {\n                    labels: ['海曙区', '江北区', '鄞州区', '余姚市', '慈溪市', '宁海县'],\n                    datasets: [{\n                        label: '人才数量',\n                        data: [3245, 2876, 5123, 1987, 1564, 987],\n                        backgroundColor: 'rgba(59, 130, 246, 0.7)'\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n\n            // 行业分布图表\n            const industryCtx = document.getElementById('industryChart').getContext('2d');\n            new Chart(industryCtx, {\n                type: 'pie',\n                data: {\n                    labels: ['电子信息', '生物医药', '新材料', '智能制造', '其他'],\n                    datasets: [{\n                        data: [35, 25, 20, 15, 5],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.7)',\n                            'rgba(16, 185, 129, 0.7)',\n                            'rgba(245, 158, 11, 0.7)',\n                            'rgba(239, 68, 68, 0.7)',\n                            'rgba(156, 163, 175, 0.7)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            position: 'right'\n                        }\n                    }\n                }\n            });\n\n            // 点击弹窗外部关闭\n            document.getElementById('detailModal').addEventListener('click', function(e) {\n                if (e.target === this) {\n                    closeDetailModal();\n                }\n            });\n\n            // ESC键关闭\n            document.addEventListener('keydown', function(e) {\n                if (e.key === 'Escape') {\n                    closeDetailModal();\n                }\n            });\n        });\n    </script>\n</body>\n</html>\n<!-- END -->", "role": "assistant"}}], "created": 1753537367, "id": "021753537116972c6f5b907bfcacf189664f83e123db882c13056", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 6449, "prompt_tokens": 38940, "total_tokens": 45389, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}