#!/usr/bin/env python3
"""
Word精准插图 - 配置模板

用户可以复制此文件为 config_custom.py 并修改配置
"""

# 章节匹配配置
SECTION_PATTERNS = {
    # 业务流程章节匹配模式
    'business_flow': {
        'keywords': ['业务流程', '流程说明', '业务说明'],
        'image_types': ['svg', 'png', 'jpg'],  # 优先使用的图片类型
        'image_width': 5.0,  # 图片宽度（英寸）
    },
    
    # 操作流程章节匹配模式  
    'operation_flow': {
        'keywords': ['操作流程', '操作说明', '使用说明'],
        'image_types': ['html', 'png', 'jpg'],  # 优先使用的图片类型
        'image_width': 5.5,  # 图片宽度（英寸）
    },
    
    # 技术方案章节匹配模式
    'technical_solution': {
        'keywords': ['技术方案', '技术架构', '系统架构'],
        'image_types': ['svg', 'png'],
        'image_width': 6.0,
    },
    
    # 实施方案章节匹配模式
    'implementation': {
        'keywords': ['实施方案', '实施计划', '部署方案'],
        'image_types': ['png', 'jpg'],
        'image_width': 5.0,
    }
}

# 文件路径配置
FILE_PATHS = {
    'input_dir': 'word_image_insertion/files',  # 输入文件目录
    'output_dir': 'output',  # 输出文件目录
    'temp_dir': 'temp',  # 临时文件目录
}

# 图片生成配置
IMAGE_GENERATION = {
    'viewport_width': 1800,  # 视口宽度
    'viewport_height': 1200,  # 视口高度
    'device_scale_factor': 2,  # 设备缩放因子（影响图片清晰度）
    'full_page': True,  # HTML截图是否包含整个页面
    'quality': 90,  # 图片质量 (1-100)
}

# 文档处理配置
DOCUMENT_PROCESSING = {
    'backup_original': True,  # 是否备份原始文档
    'auto_save': True,  # 是否自动保存
    'preserve_formatting': True,  # 是否保持原有格式
}

# 预览功能配置
PREVIEW_CONFIG = {
    'auto_open': True,  # 是否自动打开预览
    'output_formats': ['pdf', 'html'],  # 预览输出格式
    'libreoffice_timeout': 30,  # LibreOffice转换超时时间（秒）
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',  # 日志级别: DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_output': False,  # 是否输出到文件
    'log_file': 'word_image_insertion.log',
}

# 高级配置
ADVANCED_CONFIG = {
    # 章节检测配置
    'section_detection': {
        'max_search_depth': 50,  # 最大搜索深度（段落数）
        'module_keywords': ['项目管理', '人才管理', '成果管理'],  # 模块分界关键词
        'ignore_keywords': ['用户', '系统', '功能', '操作', '界面'],  # 忽略的关键词
    },
    
    # 图片处理配置
    'image_processing': {
        'max_width': 8.0,  # 最大图片宽度（英寸）
        'min_width': 3.0,  # 最小图片宽度（英寸）
        'auto_resize': True,  # 是否自动调整大小
        'compression': True,  # 是否压缩图片
    },
    
    # 错误处理配置
    'error_handling': {
        'continue_on_error': True,  # 遇到错误是否继续
        'max_retries': 3,  # 最大重试次数
        'retry_delay': 1.0,  # 重试延迟（秒）
    }
}

# 用户自定义配置示例
CUSTOM_EXAMPLES = {
    # 示例1: 自定义章节匹配
    'custom_section_example': {
        'keywords': ['自定义章节', '特殊流程'],
        'image_types': ['svg'],
        'image_width': 4.5,
    },
    
    # 示例2: 多语言支持
    'multilingual_example': {
        'business_flow_en': ['Business Process', 'Workflow'],
        'operation_flow_en': ['Operation Process', 'User Guide'],
    }
}

# 配置验证函数
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的配置项
    required_sections = ['business_flow', 'operation_flow']
    for section in required_sections:
        if section not in SECTION_PATTERNS:
            errors.append(f"缺少必要的章节配置: {section}")
    
    # 检查图片宽度范围
    for section_name, config in SECTION_PATTERNS.items():
        width = config.get('image_width', 5.0)
        if not (1.0 <= width <= 10.0):
            errors.append(f"章节 {section_name} 的图片宽度超出范围 (1.0-10.0): {width}")
    
    return errors

# 获取配置函数
def get_config():
    """获取当前配置"""
    try:
        # 尝试导入用户自定义配置
        from . import config_custom
        print("✅ 使用用户自定义配置")
        return config_custom
    except ImportError:
        print("ℹ️  使用默认配置")
        return {
            'SECTION_PATTERNS': SECTION_PATTERNS,
            'FILE_PATHS': FILE_PATHS,
            'IMAGE_GENERATION': IMAGE_GENERATION,
            'DOCUMENT_PROCESSING': DOCUMENT_PROCESSING,
            'PREVIEW_CONFIG': PREVIEW_CONFIG,
            'LOGGING_CONFIG': LOGGING_CONFIG,
            'ADVANCED_CONFIG': ADVANCED_CONFIG,
        }

if __name__ == "__main__":
    # 配置验证
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
    
    # 显示当前配置
    config = get_config()
    print(f"\n📋 当前配置:")
    for key, value in config.items():
        print(f"  {key}: {type(value).__name__}")
