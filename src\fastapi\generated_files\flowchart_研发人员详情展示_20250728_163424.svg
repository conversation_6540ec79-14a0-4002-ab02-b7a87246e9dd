<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人才档案管理系统流程</text>

  <!-- 阶段一：数据归集 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与建档</text>
  
  <!-- 节点1: 数据同步 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据同步</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">实时/周期性同步身份、成果、奖励等全量信息</text>
  </g>

  <!-- 节点2: 人才档案 -->
  <g transform="translate(400, 230)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动建档</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">建立完整的人才档案数据库</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 600 200 Q 600 215 600 230" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：详情展示 -->
  <text x="600" y="340" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：人才详情展示与交互</text>

  <!-- 节点3: 触发查看 -->
  <g transform="translate(200, 390)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">触发查看</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">从总览/清册/业务模块点击条目</text>
  </g>

  <!-- 节点4: 详情渲染 -->
  <g transform="translate(500, 390)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载所有分区数据与可视化组件</text>
  </g>

  <!-- 节点5: 交互操作 -->
  <g transform="translate(800, 390)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">交互操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">筛选、排序、导出、合作网络追溯</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 400 300 C 300 320, 250 360, 300 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead" />
  
  <!-- 连接线 3 -> 4 -->
  <path d="M 400 425 L 500 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 700 425 L 800 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点6: 数据刷新 -->
  <g transform="translate(350, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据动态刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据业务场景更新相关数据</text>
  </g>

  <!-- 节点7: 行为记录 -->
  <g transform="translate(650, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">行为记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录浏览与操作行为用于优化</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 800 460 C 700 470, 450 480, 450 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 7 -->
  <path d="M 800 460 C 750 470, 700 480, 650 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：输出与应用 -->
  <text x="600" y="620" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：输出与应用</text>

  <!-- 节点8: 信息导出 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息导出</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">导出简历、报告或定制快照</text>
  </g>

  <!-- 节点9: 变更溯源 -->
  <g transform="translate(650, 670)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更溯源</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">核心变更与数据来源版本归档</text>
  </g>

  <!-- 连接线 6 -> 8 -->
  <path d="M 350 570 C 300 600, 300 640, 300 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 9 -->
  <path d="M 650 570 C 650 600, 650 640, 650 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点10: 多场景应用 -->
  <g transform="translate(475, 780)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多场景应用</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">申报、评价、推荐、对接等</text>
  </g>

  <!-- 连接线 8 -> 10 -->
  <path d="M 425 740 C 425 750, 475 770, 475 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 775 740 C 775 750, 525 770, 525 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
</svg>