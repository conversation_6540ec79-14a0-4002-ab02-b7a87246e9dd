{"timestamp": "2025-07-28 13:50:47", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Mon, 28 Jul 2025 05:50:47 GMT", "content-type": "application/json; charset=utf-8", "content-length": "45514", "x-client-request-id": "unknown-20250728134419-ewsSLrDd", "x-envoy-upstream-service-time": "388645", "x-request-id": "02175368145905720362fc7e6079c27e4aa871382f590ddd0ccfc"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "根据需求分析，这是一个数据大屏类页面，展示科技人才总数概览。我将按照数据大屏专属心法进行设计，使用深色背景、高对比度、信息密集的布局，并严格遵循原子化锚点规范。\n\n```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>科技人才总数概览</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#3b82f6',\n                        dark: {\n                            900: '#0f172a',\n                            800: '#1e293b',\n                            700: '#334155',\n                            600: '#475569'\n                        }\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-slate-900 text-gray-100 min-h-screen\">\n    <!-- SLOT::header::BEGIN -->\n    <header class=\"bg-slate-800 border-b border-slate-700 py-4 px-6\">\n        <div class=\"max-w-7xl mx-auto flex justify-between items-center\">\n            <!-- CHUNK::header-title::BEGIN -->\n            <h1 class=\"text-2xl font-bold\">科技人才总数概览</h1>\n            <!-- CHUNK::header-title::END -->\n            \n            <!-- CHUNK::header-actions::BEGIN -->\n            <div class=\"flex space-x-3\">\n                <button class=\"px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                    </svg>\n                    刷新数据\n                </button>\n                <button class=\"px-4 py-2 bg-slate-700 border border-slate-600 rounded-md hover:bg-slate-600 flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\n                    </svg>\n                    导出数据\n                </button>\n            </div>\n            <!-- CHUNK::header-actions::END -->\n        </div>\n    </header>\n    <!-- SLOT::header::END -->\n\n    <div class=\"max-w-7xl mx-auto px-6 py-8\">\n        <!-- SLOT::filters::BEGIN -->\n        <div class=\"bg-slate-800 rounded-lg shadow-md p-6 mb-8 border border-slate-700\">\n            <h2 class=\"text-lg font-semibold mb-4\">筛选条件</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                <!-- CHUNK::filter-type::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium mb-1\">人才类型</label>\n                    <select class=\"w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\">\n                        <option>全部类型</option>\n                        <option>研发人员</option>\n                        <option>科研管理人员</option>\n                        <option>专家</option>\n                        <option>科技特派员</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-type::END -->\n                \n                <!-- CHUNK::filter-region::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium mb-1\">所在区域</label>\n                    <select class=\"w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\">\n                        <option>全市</option>\n                        <option>海曙区</option>\n                        <option>江北区</option>\n                        <option>鄞州区</option>\n                        <option>镇海区</option>\n                        <option>北仑区</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-region::END -->\n                \n                <!-- CHUNK::filter-industry::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium mb-1\">行业领域</label>\n                    <select class=\"w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\">\n                        <option>全部行业</option>\n                        <option>电子信息</option>\n                        <option>智能制造</option>\n                        <option>生物医药</option>\n                        <option>新材料</option>\n                        <option>新能源</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-industry::END -->\n                \n                <!-- CHUNK::filter-level::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium mb-1\">人才层次</label>\n                    <select class=\"w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\">\n                        <option>全部层次</option>\n                        <option>国家级</option>\n                        <option>省级</option>\n                        <option>市级</option>\n                        <option>区级</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-level::END -->\n            </div>\n        </div>\n        <!-- SLOT::filters::END -->\n\n        <!-- SLOT::kpi-cards::BEGIN -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <!-- CHUNK::card-rd::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer\" onclick=\"showTalentList('研发人员')\">\n                <div class=\"flex justify-between items-start\">\n                    <div>\n                        <h3 class=\"text-lg font-medium text-gray-300 mb-2\">研发人员</h3>\n                        <div class=\"text-3xl font-bold\">12,847</div>\n                    </div>\n                    <div class=\"bg-blue-500/20 p-3 rounded-full\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4 flex items-center text-sm text-green-400\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                    </svg>\n                    较上月增长 5.2%\n                </div>\n            </div>\n            <!-- CHUNK::card-rd::END -->\n            \n            <!-- CHUNK::card-manager::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer\" onclick=\"showTalentList('科研管理人员')\">\n                <div class=\"flex justify-between items-start\">\n                    <div>\n                        <h3 class=\"text-lg font-medium text-gray-300 mb-2\">科研管理人员</h3>\n                        <div class=\"text-3xl font-bold\">3,245</div>\n                    </div>\n                    <div class=\"bg-purple-500/20 p-3 rounded-full\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-purple-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4 flex items-center text-sm text-green-400\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                    </svg>\n                    较上月增长 2.8%\n                </div>\n            </div>\n            <!-- CHUNK::card-manager::END -->\n            \n            <!-- CHUNK::card-expert::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer\" onclick=\"showTalentList('专家')\">\n                <div class=\"flex justify-between items-start\">\n                    <div>\n                        <h3 class=\"text-lg font-medium text-gray-300 mb-2\">专家</h3>\n                        <div class=\"text-3xl font-bold\">1,876</div>\n                    </div>\n                    <div class=\"bg-yellow-500/20 p-3 rounded-full\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4 flex items-center text-sm text-green-400\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                    </svg>\n                    较上月增长 1.5%\n                </div>\n            </div>\n            <!-- CHUNK::card-expert::END -->\n            \n            <!-- CHUNK::card-specialist::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer\" onclick=\"showTalentList('科技特派员')\">\n                <div class=\"flex justify-between items-start\">\n                    <div>\n                        <h3 class=\"text-lg font-medium text-gray-300 mb-2\">科技特派员</h3>\n                        <div class=\"text-3xl font-bold\">523</div>\n                    </div>\n                    <div class=\"bg-green-500/20 p-3 rounded-full\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"mt-4 flex items-center text-sm text-green-400\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                    </svg>\n                    较上月增长 3.7%\n                </div>\n            </div>\n            <!-- CHUNK::card-specialist::END -->\n        </div>\n        <!-- SLOT::kpi-cards::END -->\n\n        <!-- SLOT::charts::BEGIN -->\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n            <!-- CHUNK::chart-region::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">\n                <div class=\"flex justify-between items-center mb-4\">\n                    <h2 class=\"text-lg font-semibold\">各区人才分布</h2>\n                    <select class=\"bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm\">\n                        <option>按数量</option>\n                        <option>按密度</option>\n                    </select>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"regionChart\"></canvas>\n                </div>\n            </div>\n            <!-- CHUNK::chart-region::END -->\n            \n            <!-- CHUNK::chart-industry::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">\n                <div class=\"flex justify-between items-center mb-4\">\n                    <h2 class=\"text-lg font-semibold\">行业人才分布</h2>\n                    <select class=\"bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm\">\n                        <option>全部类型</option>\n                        <option>研发人员</option>\n                        <option>科研管理人员</option>\n                    </select>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"industryChart\"></canvas>\n                </div>\n            </div>\n            <!-- CHUNK::chart-industry::END -->\n            \n            <!-- CHUNK::chart-age::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">\n                <div class=\"flex justify-between items-center mb-4\">\n                    <h2 class=\"text-lg font-semibold\">人才年龄结构</h2>\n                    <select class=\"bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm\">\n                        <option>全部类型</option>\n                        <option>研发人员</option>\n                        <option>专家</option>\n                    </select>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"ageChart\"></canvas>\n                </div>\n            </div>\n            <!-- CHUNK::chart-age::END -->\n            \n            <!-- CHUNK::chart-level::BEGIN -->\n            <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">\n                <div class=\"flex justify-between items-center mb-4\">\n                    <h2 class=\"text-lg font-semibold\">人才层次分布</h2>\n                    <select class=\"bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm\">\n                        <option>全部类型</option>\n                        <option>研发人员</option>\n                        <option>专家</option>\n                    </select>\n                </div>\n                <div class=\"h-80\">\n                    <canvas id=\"levelChart\"></canvas>\n                </div>\n            </div>\n            <!-- CHUNK::chart-level::END -->\n        </div>\n        <!-- SLOT::charts::END -->\n\n        <!-- SLOT::talent-list::BEGIN -->\n        <div id=\"talentList\" class=\"hidden bg-slate-800 rounded-lg shadow-md p-6 mb-8 border border-slate-700\">\n            <div class=\"flex justify-between items-center mb-6\">\n                <h2 class=\"text-xl font-semibold\">人才列表</h2>\n                <div class=\"flex space-x-3\">\n                    <input type=\"text\" placeholder=\"搜索人才姓名\" class=\"bg-slate-700 border border-slate-600 rounded-md px-3 py-1\">\n                    <button class=\"px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700\">导出当前列表</button>\n                </div>\n            </div>\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-slate-700\">\n                    <thead class=\"bg-slate-700\">\n                        <tr>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">姓名</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">类型</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">所属单位</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">行业领域</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">人才层次</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">年龄</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">参与项目</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"divide-y divide-slate-700\">\n                        <tr class=\"hover:bg-slate-700/50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">张三</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">研发人员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">宁波市XX科技有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">电子信息</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">省级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">35</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">5</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <button onclick=\"showTalentDetail('1')\" class=\"text-blue-400 hover:text-blue-300\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-slate-700/50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">李四</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">科研管理人员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">宁波市XX研究院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">生物医药</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">国家级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">42</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">8</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <button onclick=\"showTalentDetail('2')\" class=\"text-blue-400 hover:text-blue-300\">查看详情</button>\n                            </td>\n                        </tr>\n                        <tr class=\"hover:bg-slate-700/50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">王五</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">专家</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">宁波大学</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">新材料</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">国家级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">50</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">12</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <button onclick=\"showTalentDetail('3')\" class=\"text-blue-400 hover:text-blue-300\">查看详情</button>\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-slate-700\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm\">\n                        显示第 1-3 条，共 128 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700\">上一页</button>\n                        <button class=\"px-3 py-1 bg-blue-600 text-white rounded-md\">1</button>\n                        <button class=\"px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700\">2</button>\n                        <button class=\"px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700\">3</button>\n                        <button class=\"px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::talent-list::END -->\n    </div>\n\n    <!-- SLOT::talent-detail-modal::BEGIN -->\n    <div id=\"talentDetailModal\" class=\"fixed inset-0 bg-black bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen\">\n            <div class=\"bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-slate-700\">\n                <div class=\"px-6 py-4 border-b border-slate-700 flex justify-between items-center sticky top-0 bg-slate-800 z-10\">\n                    <h3 class=\"text-xl font-semibold\">人才详情</h3>\n                    <button onclick=\"hideTalentDetail()\" class=\"text-gray-400 hover:text-gray-200\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"p-6\">\n                    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                        <div class=\"lg:col-span-2\">\n                            <!-- CHUNK::detail-basic-info::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-lg font-semibold mb-4\">基本信息</h4>\n                                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">姓名：</span>\n                                        <span class=\"text-sm font-medium\">张三</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">性别：</span>\n                                        <span class=\"text-sm font-medium\">男</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">出生日期：</span>\n                                        <span class=\"text-sm font-medium\">1988-05-15</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">学历：</span>\n                                        <span class=\"text-sm font-medium\">博士</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">职称：</span>\n                                        <span class=\"text-sm font-medium\">高级工程师</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">人才类型：</span>\n                                        <span class=\"text-sm font-medium\">研发人员</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">人才层次：</span>\n                                        <span class=\"text-sm font-medium\">省级</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">所属单位：</span>\n                                        <span class=\"text-sm font-medium\">宁波市XX科技有限公司</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">行业领域：</span>\n                                        <span class=\"text-sm font-medium\">电子信息</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">联系电话：</span>\n                                        <span class=\"text-sm font-medium\">138****1234</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-sm text-gray-400\">电子邮箱：</span>\n                                        <span class=\"text-sm font-medium\"><EMAIL></span>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-basic-info::END -->\n                            \n                            <!-- CHUNK::detail-projects::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-lg font-semibold mb-4\">参与项目</h4>\n                                <div class=\"space-y-4\">\n                                    <div class=\"border-b border-slate-600 pb-4\">\n                                        <h5 class=\"font-medium\">基于人工智能的智能制造系统研发</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">项目编号：NB2023001</p>\n                                        <p class=\"text-sm text-gray-400\">起止时间：2023-01-01 至 2025-12-31</p>\n                                        <p class=\"text-sm text-gray-400 mt-2\">项目角色：技术负责人</p>\n                                    </div>\n                                    <div class=\"border-b border-slate-600 pb-4\">\n                                        <h5 class=\"font-medium\">5G通信关键技术研究与应用</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">项目编号：NB2022005</p>\n                                        <p class=\"text-sm text-gray-400\">起止时间：2022-06-01 至 2024-05-31</p>\n                                        <p class=\"text-sm text-gray-400 mt-2\">项目角色：核心研发人员</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-projects::END -->\n                            \n                            <!-- CHUNK::detail-achievements::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6\">\n                                <h4 class=\"text-lg font-semibold mb-4\">科研成果</h4>\n                                <div class=\"space-y-4\">\n                                    <div>\n                                        <h5 class=\"font-medium\">发明专利：一种智能制造系统及其控制方法</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">专利号：ZL202310123456.7</p>\n                                        <p class=\"text-sm text-gray-400\">授权日期：2023-05-20</p>\n                                    </div>\n                                    <div>\n                                        <h5 class=\"font-medium\">论文：人工智能在智能制造中的应用研究</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">发表期刊：计算机应用研究</p>\n                                        <p class=\"text-sm text-gray-400\">发表时间：2022-08-15</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-achievements::END -->\n                        </div>\n                        \n                        <div>\n                            <!-- CHUNK::detail-photo::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6 mb-6\">\n                                <div class=\"flex justify-center\">\n                                    <img src=\"https://source.unsplash.com/200x200/?portrait,man\" alt=\"人才照片\" class=\"w-32 h-32 rounded-full object-cover\">\n                                </div>\n                                <div class=\"mt-4 text-center\">\n                                    <h4 class=\"text-lg font-semibold\">张三</h4>\n                                    <p class=\"text-sm text-gray-400\">高级工程师</p>\n                                    <p class=\"text-sm text-gray-400 mt-1\">宁波市XX科技有限公司</p>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-photo::END -->\n                            \n                            <!-- CHUNK::detail-awards::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-lg font-semibold mb-4\">获奖情况</h4>\n                                <div class=\"space-y-3\">\n                                    <div>\n                                        <h5 class=\"font-medium\">宁波市科技进步一等奖</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">2022年度</p>\n                                    </div>\n                                    <div>\n                                        <h5 class=\"font-medium\">浙江省优秀科技工作者</h5>\n                                        <p class=\"text-sm text-gray-400 mt-1\">2021年度</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-awards::END -->\n                            \n                            <!-- CHUNK::detail-skills::BEGIN -->\n                            <div class=\"bg-slate-700/50 rounded-lg p-6\">\n                                <h4 class=\"text-lg font-semibold mb-4\">专业技能</h4>\n                                <div class=\"flex flex-wrap gap-2\">\n                                    <span class=\"px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full\">人工智能</span>\n                                    <span class=\"px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full\">机器学习</span>\n                                    <span class=\"px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full\">智能制造</span>\n                                    <span class=\"px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full\">5G通信</span>\n                                    <span class=\"px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full\">工业互联网</span>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-skills::END -->\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::talent-detail-modal::END -->\n\n    <script>\n        // 1. 全局UI控制函数\n        function showTalentList(type) {\n            document.getElementById('talentList').classList.remove('hidden');\n            document.getElementById('talentList').scrollIntoView({ behavior: 'smooth' });\n        }\n\n        function showTalentDetail(id) {\n            document.getElementById('talentDetailModal').classList.remove('hidden');\n            document.body.style.overflow = 'hidden';\n        }\n\n        function hideTalentDetail() {\n            document.getElementById('talentDetailModal').classList.add('hidden');\n            document.body.style.overflow = 'auto';\n        }\n\n        // 2. DOM加载完毕后执行的初始化代码\n        document.addEventListener('DOMContentLoaded', function() {\n            // 初始化所有图表\n            const regionCtx = document.getElementById('regionChart').getContext('2d');\n            new Chart(regionCtx, {\n                type: 'bar',\n                data: {\n                    labels: ['海曙区', '江北区', '鄞州区', '镇海区', '北仑区'],\n                    datasets: [{\n                        label: '人才数量',\n                        data: [3245, 2876, 4987, 1654, 2085],\n                        backgroundColor: 'rgba(59, 130, 246, 0.8)'\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: { display: false }\n                    },\n                    scales: {\n                        y: { beginAtZero: true }\n                    }\n                }\n            });\n\n            const industryCtx = document.getElementById('industryChart').getContext('2d');\n            new Chart(industryCtx, {\n                type: 'pie',\n                data: {\n                    labels: ['电子信息', '智能制造', '生物医药', '新材料', '新能源'],\n                    datasets: [{\n                        data: [35, 25, 20, 15, 5],\n                        backgroundColor: [\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(239, 68, 68, 0.8)',\n                            'rgba(139, 92, 246, 0.8)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: { position: 'right' }\n                    }\n                }\n            });\n\n            const ageCtx = document.getElementById('ageChart').getContext('2d');\n            new Chart(ageCtx, {\n                type: 'line',\n                data: {\n                    labels: ['25岁以下', '26-35岁', '36-45岁', '46-55岁', '56岁以上'],\n                    datasets: [{\n                        label: '人才数量',\n                        data: [1560, 5240, 3870, 1560, 617],\n                        borderColor: 'rgba(59, 130, 246, 0.8)',\n                        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                        fill: true,\n                        tension: 0.4\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: { display: false }\n                    },\n                    scales: {\n                        y: { beginAtZero: true }\n                    }\n                }\n            });\n\n            const levelCtx = document.getElementById('levelChart').getContext('2d');\n            new Chart(levelCtx, {\n                type: 'doughnut',\n                data: {\n                    labels: ['国家级', '省级', '市级', '区级'],\n                    datasets: [{\n                        data: [1560, 3240, 5870, 2210],\n                        backgroundColor: [\n                            'rgba(239, 68, 68, 0.8)',\n                            'rgba(245, 158, 11, 0.8)',\n                            'rgba(59, 130, 246, 0.8)',\n                            'rgba(16, 185, 129, 0.8)'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: { position: 'right' }\n                    }\n                }\n            });\n\n            // 绑定弹窗外部点击关闭事件\n            document.getElementById('talentDetailModal').addEventListener('click', function(e) {\n                if (e.target === this) {\n                    hideTalentDetail();\n                }\n            });\n\n            // 绑定ESC键关闭弹窗\n            document.addEventListener('keydown', function(e) {\n                if (e.key === 'Escape' && !document.getElementById('talentDetailModal').classList.contains('hidden')) {\n                    hideTalentDetail();\n                }\n            });\n        });\n    </script>\n</body>\n</html>\n<!-- END -->\n```", "role": "assistant"}}], "created": 1753681847, "id": "02175368145905720362fc7e6079c27e4aa871382f590ddd0ccfc", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 8845, "prompt_tokens": 38940, "total_tokens": 47785, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}