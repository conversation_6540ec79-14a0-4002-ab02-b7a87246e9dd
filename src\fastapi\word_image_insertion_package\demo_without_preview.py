#!/usr/bin/env python3
"""
不依赖LibreOffice的演示

专注于核心插图功能，跳过PDF预览
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

from word_image_insertion import WordImageInserter, Config


async def main():
    """主演示函数"""
    print("🎭 Word精准插图演示（无预览版）")
    print("=" * 45)
    print("专注于：精准定位 → 段落插图 → Word文档生成")
    
    # 文件路径
    files_dir = Path('word_image_insertion/files')
    word_doc = files_dir / '投标文件-0613.docx'
    svg_file = files_dir / 'flowchart_科技人才总数概览_20250726_205914.svg'
    html_file = files_dir / 'ui_prototype_科技人才总数概览_20250726_204948.html'
    
    output_dir = Path('demo_no_preview_output')
    output_dir.mkdir(exist_ok=True)
    
    # 创建插图器
    config = Config()
    inserter = WordImageInserter(config)
    
    print("\n📍 步骤1: 精准定位插入点")
    try:
        # 定位"三、业务流程"
        business_targets = inserter.parse_document(str(word_doc), keyword_pattern='三、业务流程')
        print(f"  ✅ 找到'三、业务流程' {len(business_targets)} 个位置")
        
        # 定位"四、操作流程"
        operation_targets = inserter.parse_document(str(word_doc), keyword_pattern='四、操作流程')
        print(f"  ✅ 找到'四、操作流程' {len(operation_targets)} 个位置")
        
        if not business_targets or not operation_targets:
            print("❌ 未找到必要的插入点")
            return
        
        # 显示定位详情
        print("  📋 定位详情:")
        print(f"    - 业务流程段落: 第{business_targets[0].paragraph_index}段")
        print(f"    - 操作流程段落: 第{operation_targets[0].paragraph_index}段")
        
    except Exception as e:
        print(f"❌ 定位失败: {e}")
        return
    
    print("\n🎨 步骤2: 生成高质量图片")
    try:
        # 读取SVG内容
        with open(svg_file, 'r', encoding='utf-8') as f:
            svg_content = f.read()
        
        # 生成业务流程图
        print("  🎨 生成业务流程图...")
        business_image = await inserter.image_generator.generate_async(
            svg_content,
            output_path=str(output_dir / 'business_flow_final.png'),
            viewport_width=1800,
            viewport_height=1200,
            device_scale_factor=2
        )
        print(f"  ✅ 业务流程图: {Path(business_image).name}")
        
        # 生成操作界面截图
        print("  📸 生成操作界面截图...")
        html_url = f"file:///{html_file.absolute().as_posix()}"
        operation_image = await inserter.image_generator.generate_async(
            html_url,
            output_path=str(output_dir / 'operation_ui_final.png'),
            viewport_size={'width': 1600, 'height': 1200},
            full_page=True,
            device_scale_factor=2
        )
        print(f"  ✅ 操作界面截图: {Path(operation_image).name}")
        
        # 显示图片信息
        from PIL import Image
        with Image.open(business_image) as img:
            size_mb = Path(business_image).stat().st_size / (1024 * 1024)
            print(f"    - 业务流程图: {img.size[0]}x{img.size[1]} 像素, {size_mb:.2f} MB")
        with Image.open(operation_image) as img:
            size_mb = Path(operation_image).stat().st_size / (1024 * 1024)
            print(f"    - 操作界面图: {img.size[0]}x{img.size[1]} 像素, {size_mb:.2f} MB")
        
    except Exception as e:
        print(f"❌ 图片生成失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n📝 步骤3: 精准插图到段落后")
    try:
        current_doc = str(word_doc)
        
        # 插入业务流程图到"三、业务流程"段落后
        print("  📝 插入业务流程图到段落后...")
        temp_doc1 = inserter.insert_image(
            current_doc,
            business_targets[0],
            business_image,
            str(output_dir / 'temp_with_business.docx')
        )
        print("  ✅ 业务流程图已插入")
        
        # 重新解析文档（因为段落索引可能已改变）
        print("  🔄 重新解析文档...")
        operation_targets_new = inserter.parse_document(temp_doc1, keyword_pattern='四、操作流程')
        if not operation_targets_new:
            print("  ⚠️  重新解析失败，使用原索引")
            operation_target = operation_targets[0]
        else:
            operation_target = operation_targets_new[0]
            print(f"  ✅ 重新定位操作流程: 第{operation_target.paragraph_index}段")
        
        # 插入操作界面图到"四、操作流程"段落后
        print("  📝 插入操作界面图到段落后...")
        final_doc = inserter.insert_image(
            temp_doc1,
            operation_target,
            operation_image,
            str(output_dir / 'final_complete_document.docx')
        )
        print("  ✅ 操作界面图已插入")
        
        # 清理临时文件
        Path(temp_doc1).unlink()
        
        print(f"  🎉 完整文档生成: {Path(final_doc).name}")
        
    except Exception as e:
        print(f"❌ 插图失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 演示完成！")
    print("=" * 45)
    print("📋 完成的工作:")
    print("  ✅ 1. 精准定位 - 找到目标段落")
    print("  ✅ 2. 图片生成 - 高质量SVG和HTML转换")
    print("  ✅ 3. 段落插图 - 图片插入到段落后")
    print("  ✅ 4. Word文档 - 生成最终文档")
    
    print(f"\n📁 生成的文件:")
    print(f"  📄 最终文档: {final_doc}")
    print(f"  🎨 业务流程图: {business_image}")
    print(f"  📸 操作界面图: {operation_image}")
    
    print(f"\n💡 使用建议:")
    print(f"  1. 打开最终Word文档查看插图效果")
    print(f"  2. 检查图片是否在正确的段落位置")
    print(f"  3. 验证图片大小和清晰度")
    
    print(f"\n📋 关于预览功能:")
    print(f"  - 如需PDF预览，请先安装LibreOffice")
    print(f"  - 下载地址: https://www.libreoffice.org/download/")
    print(f"  - 安装后可运行完整版演示")
    
    # 清理浏览器资源
    try:
        await inserter.image_generator._close_browser()
    except:
        pass


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
