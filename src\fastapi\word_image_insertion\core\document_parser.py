"""
文档解析和定位模块

使用python-docx解析Word文档，实现基于标题样式和关键词的精准定位功能。
核心思路：遍历段落，依据标题样式或关键词计算"插入锚点"。
"""

import re
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from docx import Document
from docx.shared import Inches
from docx.enum.style import WD_STYLE_TYPE

logger = logging.getLogger(__name__)


class InsertionPoint:
    """插入点信息类"""
    
    def __init__(self, paragraph_index: int, paragraph, style_name: str, 
                 text: str, match_type: str, match_content: str):
        self.paragraph_index = paragraph_index
        self.paragraph = paragraph
        self.style_name = style_name
        self.text = text
        self.match_type = match_type  # 'heading' or 'keyword'
        self.match_content = match_content
        
    def __repr__(self):
        return f"InsertionPoint(index={self.paragraph_index}, style='{self.style_name}', match='{self.match_content}')"


class DocumentParser:
    """文档解析器"""
    
    def __init__(self, config=None):
        self.config = config
        self._style_cache = {}  # 样式缓存
        self._document_cache = {}  # 文档缓存
        
    def find_insertion_points(self, doc_path: str, heading_pattern: Optional[str] = None, 
                            keyword_pattern: Optional[str] = None) -> List[InsertionPoint]:
        """
        查找插入点
        
        Args:
            doc_path: Word文档路径
            heading_pattern: 标题样式模式，如 'Heading 5'
            keyword_pattern: 关键词正则表达式模式
            
        Returns:
            List[InsertionPoint]: 插入点列表
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
            
        logger.info(f"Parsing document: {doc_path}")
        
        # 加载文档
        doc = self._load_document(doc_path)
        
        # 构建样式映射
        style_mapping = self._build_style_mapping(doc)
        
        insertion_points = []
        
        # 遍历段落查找匹配点
        for idx, paragraph in enumerate(doc.paragraphs):
            # 跳过空段落
            if not paragraph.text.strip():
                continue
                
            style_name = paragraph.style.name if paragraph.style else 'Normal'
            
            # 检查标题样式匹配
            if heading_pattern and self._match_heading_style(style_name, heading_pattern):
                insertion_point = InsertionPoint(
                    paragraph_index=idx,
                    paragraph=paragraph,
                    style_name=style_name,
                    text=paragraph.text,
                    match_type='heading',
                    match_content=heading_pattern
                )
                insertion_points.append(insertion_point)
                logger.debug(f"Found heading match: {insertion_point}")
                
            # 检查关键词匹配
            if keyword_pattern and self._match_keyword(paragraph.text, keyword_pattern):
                # 找到章节结束位置
                section_end_index = self._find_section_end(doc.paragraphs, idx, keyword_pattern)

                insertion_point = InsertionPoint(
                    paragraph_index=section_end_index,
                    paragraph=paragraph,
                    style_name=style_name,
                    text=paragraph.text,
                    match_type='keyword',
                    match_content=keyword_pattern
                )
                insertion_points.append(insertion_point)
                logger.debug(f"Found keyword match at {idx}, section ends at {section_end_index}: {insertion_point}")
        
        logger.info(f"Found {len(insertion_points)} insertion points")
        return insertion_points

    def _find_section_end(self, paragraphs, section_start_index: int, section_pattern: str) -> int:
        """
        找到章节的结束位置

        Args:
            paragraphs: 文档段落列表
            section_start_index: 章节开始段落索引
            section_pattern: 章节匹配模式

        Returns:
            int: 章节结束后的插入位置索引
        """
        import re

        # 获取当前章节的标题文本，用于识别相同章节
        current_section_text = paragraphs[section_start_index].text.strip()

        # 特殊处理：对于包含特定关键词的章节，寻找下一个相同的章节标题或更高级章节
        main_keywords = ['业务流程', '操作流程', '技术方案', '实施方案']
        current_has_main_keyword = any(keyword in current_section_text for keyword in main_keywords)

        if current_has_main_keyword:
            # 对于主要章节，寻找下一个相同标题的章节或逻辑上的下一个章节
            for i in range(section_start_index + 1, len(paragraphs)):
                paragraph = paragraphs[i]
                text = paragraph.text.strip()

                if not text:
                    continue

                # 检查是否是相同的章节标题（重复章节）
                if text == current_section_text:
                    logger.debug(f"Found duplicate main section at paragraph {i}: {text[:50]}...")
                    return i - 1  # 在重复章节前结束

                # 检查是否是逻辑上的下一个主要章节
                if self._is_next_logical_section(text, current_section_text):
                    logger.debug(f"Found next logical section at paragraph {i}: {text[:50]}...")
                    return i - 1
        else:
            # 对于普通章节，使用原来的逻辑
            current_level = self._get_section_level(current_section_text)

            for i in range(section_start_index + 1, len(paragraphs)):
                paragraph = paragraphs[i]
                text = paragraph.text.strip()

                if not text:
                    continue

                # 检查是否是相同的章节标题
                if text == current_section_text:
                    logger.debug(f"Found duplicate section at paragraph {i}: {text[:50]}...")
                    return i - 1

                # 检查是否是同级或更高级的章节
                section_level = self._get_section_level(text)
                if section_level is not None and section_level <= current_level:
                    logger.debug(f"Found same/higher level section at paragraph {i}: {text[:50]}...")
                    return i - 1

        # 如果没找到下一个章节，返回文档末尾
        logger.debug(f"No next section found, using document end")
        return len(paragraphs) - 1

    def _is_next_logical_section(self, text: str, current_text: str) -> bool:
        """
        判断是否是逻辑上的下一个章节

        Args:
            text: 要检查的文本
            current_text: 当前章节文本

        Returns:
            bool: 是否是逻辑上的下一个章节
        """
        import re

        # 检查是否是新的管理模块开始（这是最高优先级的分界点）
        # 精确匹配模块标题
        if text.strip() == '内部研发项目管理':
            return True
        if text.strip() == '重点研发项目管理':
            return True
        if '项目管理' in text and len(text.strip()) < 20:
            # 短文本且包含"项目管理"，很可能是模块标题
            if not any(exclude in text for exclude in ['用户', '系统', '功能', '操作', '界面', '提供', '支持']):
                return True

        # 提取当前章节的关键词
        current_keyword = None
        if '业务流程' in current_text:
            current_keyword = '业务流程'
        elif '操作流程' in current_text:
            current_keyword = '操作流程'

        # 如果当前是业务流程，下一个操作流程就是逻辑下一个章节
        if current_keyword == '业务流程' and '操作流程' in text:
            return True

        # 检查是否是下一个数字编号的章节
        current_match = re.search(r'^([一二三四五六七八九十]+)、', current_text)
        text_match = re.search(r'^([一二三四五六七八九十]+)、', text)

        if current_match and text_match:
            current_num = self._chinese_to_number(current_match.group(1))
            text_num = self._chinese_to_number(text_match.group(1))
            if text_num == current_num + 1:
                return True

        return False

    def _chinese_to_number(self, chinese: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        chinese_to_num = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
        }
        return chinese_to_num.get(chinese, 0)

    def _get_section_level(self, text: str) -> int:
        """
        获取章节级别

        Args:
            text: 段落文本

        Returns:
            int: 章节级别 (1=一级章节, 2=二级章节, None=非章节)
        """
        import re

        # 在这个文档中，所有"一、二、三、四、五..."都应该是同级的子章节
        # 只有当章节编号很大（如"五、六、七..."）且包含特定关键词时，才认为是主章节

        # 检查是否是中文数字章节
        chinese_match = re.match(r'^([一二三四五六七八九十]+)、', text)
        if chinese_match:
            chinese_num = chinese_match.group(1)

            # 中文数字到阿拉伯数字的映射
            chinese_to_num = {
                '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
            }

            num = chinese_to_num.get(chinese_num)
            if num is not None:
                # 主要关键词，这些关键词出现时可能是主章节
                main_keywords = ['技术方案', '实施方案', '总结', '结论', '概述', '介绍']
                has_main_keyword = any(keyword in text for keyword in main_keywords)

                # 对于"业务流程"和"操作流程"，在这个文档中它们是子章节
                # 只有编号>=5且包含其他主要关键词时，才认为是主章节
                if num >= 5 and has_main_keyword:
                    return 1
                else:
                    # 所有"一、二、三、四"都是同级的子章节
                    return 2

        # 阿拉伯数字章节 (5. 6. 7. 等) - 主章节
        if re.match(r'^[5-9]\.', text):
            return 1

        # 低级别阿拉伯数字章节 (1. 2. 3. 4.) - 子章节
        if re.match(r'^[1-4]\.', text):
            return 2

        # 第X章 - 主章节
        if re.match(r'^第[一二三四五六七八九十]+章', text):
            return 1

        # 第X节 - 子章节
        if re.match(r'^第[一二三四五六七八九十]+节', text):
            return 2

        # 英文字母章节 - 子章节
        if re.match(r'^[A-Z]\.', text):
            return 2

        # 不是章节标题
        return None

    def _load_document(self, doc_path: Path) -> Document:
        """加载文档（带缓存）"""
        doc_key = str(doc_path)
        if doc_key not in self._document_cache:
            try:
                self._document_cache[doc_key] = Document(doc_path)
                logger.debug(f"Loaded document: {doc_path}")
            except Exception as e:
                logger.error(f"Failed to load document {doc_path}: {e}")
                raise
        return self._document_cache[doc_key]

    def _build_style_mapping(self, doc: Document) -> Dict[str, str]:
        """构建样式映射表"""
        doc_id = id(doc)
        if doc_id not in self._style_cache:
            style_mapping = {}

            try:
                # 遍历文档样式
                for style in doc.styles:
                    if style.type == WD_STYLE_TYPE.PARAGRAPH:
                        style_mapping[style.name] = style.name
                        logger.debug(f"Found paragraph style: {style.name}")

            except Exception as e:
                logger.warning(f"Failed to build style mapping: {e}")

            self._style_cache[doc_id] = style_mapping
            logger.debug(f"Built style mapping with {len(style_mapping)} styles")

        return self._style_cache[doc_id]

    def _match_heading_style(self, style_name: str, pattern: str) -> bool:
        """匹配标题样式"""
        if not style_name or not pattern:
            return False

        # 精确匹配
        if style_name == pattern:
            return True

        # 模糊匹配（忽略大小写）
        if style_name.lower() == pattern.lower():
            return True

        # 包含匹配
        if pattern.lower() in style_name.lower():
            return True

        return False

    def _match_keyword(self, text: str, pattern: str) -> bool:
        """匹配关键词"""
        if not text or not pattern:
            return False

        try:
            # 尝试正则表达式匹配
            if re.search(pattern, text, re.IGNORECASE):
                return True
        except re.error:
            # 如果不是有效的正则表达式，使用字符串包含匹配
            if pattern.lower() in text.lower():
                return True

        return False

    def get_document_info(self, doc_path: str) -> Dict:
        """获取文档基本信息"""
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")

        doc = self._load_document(doc_path)

        info = {
            'path': str(doc_path),
            'paragraphs_count': len(doc.paragraphs),
            'tables_count': len(doc.tables),
            'styles': []
        }

        # 收集样式信息
        style_mapping = self._build_style_mapping(doc)
        info['styles'] = list(style_mapping.keys())

        return info

    def preview_paragraphs(self, doc_path: str, max_paragraphs: int = 10) -> List[Dict]:
        """预览文档段落"""
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")

        doc = self._load_document(doc_path)

        paragraphs = []
        for idx, paragraph in enumerate(doc.paragraphs[:max_paragraphs]):
            if paragraph.text.strip():  # 跳过空段落
                paragraphs.append({
                    'index': idx,
                    'text': paragraph.text[:100] + ('...' if len(paragraph.text) > 100 else ''),
                    'style': paragraph.style.name if paragraph.style else 'Normal',
                    'full_text': paragraph.text
                })

        return paragraphs
