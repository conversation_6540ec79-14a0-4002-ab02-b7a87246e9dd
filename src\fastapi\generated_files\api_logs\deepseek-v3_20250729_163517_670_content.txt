=== API调用成功 ===
时间: 2025-07-29 16:41:27
模型: deepseek-v3
内容长度: 40591 字符

=== 生成内容 ===
根据需求分析，这是一个典型的"管理后台"类功能，我将按照管理后台的设计心法和代码范例来开发科技项目管理页面。以下是完整的HTML代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- SLOT::header::BEGIN -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技项目管理</h1>
            <p class="text-gray-600">为各级科技项目提供全生命周期的标准化管理平台，支持项目新增、修改、删除、查询、导出和批量导入操作</p>
        </div>
        <!-- SLOT::header::END -->

        <!-- SLOT::filters::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                高级检索
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- CHUNK::filter-project-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                </div>
                <!-- CHUNK::filter-project-name::END -->
                
                <!-- CHUNK::filter-company::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">承担单位</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入承担单位">
                </div>
                <!-- CHUNK::filter-company::END -->
                
                <!-- CHUNK::filter-credit-code::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                </div>
                <!-- CHUNK::filter-credit-code::END -->
                
                <!-- CHUNK::filter-batch::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">申报批次</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部批次</option>
                        <option value="2023-1">2023年第一批</option>
                        <option value="2023-2">2023年第二批</option>
                    </select>
                </div>
                <!-- CHUNK::filter-batch::END -->
                
                <!-- CHUNK::filter-status::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="declared">已申报</option>
                        <option value="approved">已立项</option>
                        <option value="in-progress">执行中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                <!-- CHUNK::filter-status::END -->
                
                <!-- CHUNK::filter-year::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">申报年度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部年度</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                    </select>
                </div>
                <!-- CHUNK::filter-year::END -->
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <!-- CHUNK::filter-reset-button::BEGIN -->
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <!-- CHUNK::filter-reset-button::END -->
                
                <!-- CHUNK::filter-search-button::BEGIN -->
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
                <!-- CHUNK::filter-search-button::END -->
            </div>
        </div>
        <!-- SLOT::filters::END -->

        <!-- SLOT::actions::BEGIN -->
        <div class="flex justify-between items-center mb-4">
            <div class="text-sm text-gray-600">
                共 <span class="font-medium text-gray-900">156</span> 个项目
            </div>
            <div class="flex space-x-3">
                <!-- CHUNK::action-export-button::BEGIN -->
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出
                </button>
                <!-- CHUNK::action-export-button::END -->
                
                <!-- CHUNK::action-import-button::BEGIN -->
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    导入
                </button>
                <!-- CHUNK::action-import-button::END -->
                
                <!-- CHUNK::action-add-button::BEGIN -->
                <button onclick="openModal('addProjectModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增项目
                </button>
                <!-- CHUNK::action-add-button::END -->
            </div>
        </div>
        <!-- SLOT::actions::END -->

        <!-- SLOT::main-table::BEGIN -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <!-- CHUNK::table-header-checkbox::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <!-- CHUNK::table-header-checkbox::END -->
                            
                            <!-- CHUNK::table-header-name::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <!-- CHUNK::table-header-name::END -->
                            
                            <!-- CHUNK::table-header-code::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <!-- CHUNK::table-header-code::END -->
                            
                            <!-- CHUNK::table-header-company::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <!-- CHUNK::table-header-company::END -->
                            
                            <!-- CHUNK::table-header-leader::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <!-- CHUNK::table-header-leader::END -->
                            
                            <!-- CHUNK::table-header-type::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目属性</th>
                            <!-- CHUNK::table-header-type::END -->
                            
                            <!-- CHUNK::table-header-date::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <!-- CHUNK::table-header-date::END -->
                            
                            <!-- CHUNK::table-header-status::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <!-- CHUNK::table-header-status::END -->
                            
                            <!-- CHUNK::table-header-actions::BEGIN -->
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            <!-- CHUNK::table-header-actions::END -->
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::table-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研发</div>
                                <div class="text-sm text-gray-500">2023年重点研发计划</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB2023RD001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">重点研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>2023-01-15</div>
                                <div>2024-12-31</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">执行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::table-row-1::END -->
                        
                        <!-- CHUNK::table-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新材料产业化应用研究</div>
                                <div class="text-sm text-gray-500">2023年科技成果转化</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB2023CG002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市YY材料研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">成果转化</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>2023-03-10</div>
                                <div>2023-12-31</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已立项</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::table-row-2::END -->
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">156</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::main-table::END -->
    </div>

    <!-- SLOT::add-project-modal::BEGIN -->
    <div id="addProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增科技项目</h3>
                    <button onclick="closeModal('addProjectModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- CHUNK::modal-project-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-project-name::END -->
                        
                        <!-- CHUNK::modal-project-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::modal-project-code::END -->
                        
                        <!-- CHUNK::modal-project-type::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目属性 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择项目属性</option>
                                <option value="key">重点研发</option>
                                <option value="transform">成果转化</option>
                                <option value="basic">基础研究</option>
                            </select>
                        </div>
                        <!-- CHUNK::modal-project-type::END -->
                        
                        <!-- CHUNK::modal-project-level::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目级别</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal" selected>市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <!-- CHUNK::modal-project-level::END -->
                        
                        <!-- CHUNK::modal-company::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">承担单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-company::END -->
                        
                        <!-- CHUNK::modal-credit-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::modal-credit-code::END -->
                        
                        <!-- CHUNK::modal-leader::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目负责人 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-leader::END -->
                        
                        <!-- CHUNK::modal-phone::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::modal-phone::END -->
                        
                        <!-- CHUNK::modal-start-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::modal-start-date::END -->
                        
                        <!-- CHUNK::modal-end-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::modal-end-date::END -->
                        
                        <!-- CHUNK::modal-funds::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目经费（万元）</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::modal-funds::END -->
                        
                        <!-- CHUNK::modal-status::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="declared">已申报</option>
                                <option value="approved">已立项</option>
                                <option value="in-progress">执行中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                        <!-- CHUNK::modal-status::END -->
                    </div>
                    
                    <!-- CHUNK::modal-description::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <!-- CHUNK::modal-description::END -->
                    
                    <!-- CHUNK::modal-attachments::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel文件，最大10MB</p>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::modal-attachments::END -->
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <!-- CHUNK::modal-cancel-button::BEGIN -->
                        <button type="button" onclick="closeModal('addProjectModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <!-- CHUNK::modal-cancel-button::END -->
                        
                        <!-- CHUNK::modal-submit-button::BEGIN -->
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                        <!-- CHUNK::modal-submit-button::END -->
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- SLOT::add-project-modal::END -->

    <!-- SLOT::detail-modal::BEGIN -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-6">
                    <!-- CHUNK::detail-basic-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">项目名称：</span>
                                <span class="font-medium text-gray-900">宁波市智能制造关键技术研发</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目编号：</span>
                                <span class="font-medium text-gray-900">NB2023RD001</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目属性：</span>
                                <span class="font-medium text-gray-900">重点研发</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目级别：</span>
                                <span class="font-medium text-gray-900">市级</span>
                            </div>
                            <div>
                                <span class="text-gray-500">承担单位：</span>
                                <span class="font-medium text-gray-900">宁波市XX科技有限公司</span>
                            </div>
                            <div>
                                <span class="text-gray-500">信用代码：</span>
                                <span class="font-medium text-gray-900">91330201MA2XXXXXX</span>
                            </div>
                            <div>
                                <span class="text-gray-500">负责人：</span>
                                <span class="font-medium text-gray-900">张研究员</span>
                            </div>
                            <div>
                                <span class="text-gray-500">联系电话：</span>
                                <span class="font-medium text-gray-900">138XXXX8888</span>
                            </div>
                            <div>
                                <span class="text-gray-500">起止时间：</span>
                                <span class="font-medium text-gray-900">2023-01-15 至 2024-12-31</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目经费：</span>
                                <span class="font-medium text-gray-900">500万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目状态：</span>
                                <span class="font-medium text-gray-900">执行中</span>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-basic-info::END -->
                    
                    <!-- CHUNK::detail-description::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目简介</h4>
                        <div class="text-sm text-gray-700">
                            <p>本项目旨在研发智能制造领域的关键技术，包括智能生产线设计、工业机器人应用、物联网技术集成等。通过本项目的实施，将提升宁波市制造业的智能化水平，预计可带动相关产业产值增长20%以上。</p>
                        </div>
                    </div>
                    <!-- CHUNK::detail-description::END -->
                    
                    <!-- CHUNK::detail-team::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目团队</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分工</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张研究员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术指导</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- CHUNK::detail-team::END -->
                    
                    <!-- CHUNK::detail-attachments::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">相关附件</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">项目申报书.pdf</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">立项通知书.docx</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-attachments::END -->
                    
                    <div class="flex justify-end space-x-3">
                        <!-- CHUNK::detail-export-button::BEGIN -->
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出报告
                        </button>
                        <!-- CHUNK::detail-export-button::END -->
                        
                        <!-- CHUNK::detail-close-button::BEGIN -->
                        <button onclick="closeModal('detailModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            关闭
                        </button>
                        <!-- CHUNK::detail-close-button::END -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::detail-modal::END -->

    <script>
        // --- 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function confirmDelete() {
            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {
                alert('项目已删除 (演示)');
            }
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 绑定ESC键关闭
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (演示)');
                    const parentModalId = form.closest('[id$="Modal"]')?.id;
                    if (parentModalId) closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>
<!-- END -->
```