"""
FastAPI主应用
提供Word文档转换等API服务
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
import sys
import os
import time
import asyncio

# Windows 环境下设置事件循环策略以修复 Playwright 问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

# 添加父目录到路径中，以便导入word_converter
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from word_converter import convert_word_to_text, convert_word_to_structured_data
from task_processor import task_processor
from html_refiner import HTMLRefiner
from word_image_insertion import WordImageInserter, Config as WordInsertionConfig
from pydantic import BaseModel
from typing import List, Optional
import tempfile
import shutil
import asyncio
import logging

logger = logging.getLogger(__name__)

app = FastAPI(
    title="Document Processing API",
    description="提供文档处理和转换服务",
    version="1.0.0"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置为具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求数据模型
class TaskRequest(BaseModel):
    taskId: str
    taskType: str  # function_description, flowchart, ui_prototype
    excelIndex: int
    functionName: str
    modulePath: str
    businessProcess: str
    functionDescription: str

class HTMLRefineRequest(BaseModel):
    html_content: str
    user_request: str
    task_id: str
    html_file_path: Optional[str] = None

# Word图像插入相关数据模型
class ImageInsertionConfig(BaseModel):
    section_id: str
    content_type: str  # 'svg' or 'html'
    content_path: str
    keyword_pattern: str
    image_width: Optional[float] = 5.0

class WordImageInsertRequest(BaseModel):
    doc_path: str
    insertions: List[ImageInsertionConfig]
    output_path: Optional[str] = None

class WordBatchInsertRequest(BaseModel):
    doc_path: str
    task_results: List[dict]  # TaskResult数据
    config: Optional[dict] = None
    output_path: Optional[str] = None

class WordPreviewRequest(BaseModel):
    doc_path: str
    preview_type: str = 'pdf'  # 'pdf', 'html', 'dual'
    
class HTMLRefineResponse(BaseModel):
    success: bool
    modified_html: str = ""
    modification_summary: str = ""
    error: str = ""
    target_anchor: str = ""
    modification_type: str = ""
    analysis_comment: str = ""
    backup_created: str = ""  # 创建的备份文件路径
    file_updated: str = ""    # 更新的原文件路径
    backup_history: List[str] = []  # 备份历史列表

class SaveFileRequest(BaseModel):
    file_path: str
    content: str
    task_id: str = ""

class SaveFileResponse(BaseModel):
    success: bool
    message: str = ""
    error: str = ""
    file_path: str = ""
    backup_created: str = ""


@app.get("/")
async def root():
    """
    健康检查端点
    """
    return {"message": "Document Processing API is running", "status": "healthy"}


@app.post("/api/convert-word-to-markdown")
async def convert_word_to_markdown(file: UploadFile = File(...)):
    """
    将Word文档转换为Markdown/文本格式
    
    Args:
        file: 上传的Word文档文件 (.docx格式)
    
    Returns:
        转换后的文本内容
    """
    # 检查文件类型
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件扩展名
    if not (file.filename.endswith('.docx') or file.filename.endswith('.doc')):
        raise HTTPException(status_code=400, detail="仅支持.docx和.doc格式的Word文档")
    
    # 检查文件大小 (限制为10MB)
    max_size = 10 * 1024 * 1024  # 10MB
    file_content = await file.read()
    
    if len(file_content) > max_size:
        raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
    
    if len(file_content) == 0:
        raise HTTPException(status_code=400, detail="文件内容为空")
    
    try:
        print(f"📁 收到Word文档转换请求: {file.filename}")
        print(f"📏 文件大小: {len(file_content)} 字节")
        
        # 调用转换函数
        converted_text = convert_word_to_text(file_content)
        
        print(f"✅ 转换完成，输出文本长度: {len(converted_text)} 字符")
        
        return converted_text
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档转换失败: {str(e)}")


@app.post("/api/convert-word-to-structured")
async def convert_word_to_structured(file: UploadFile = File(...)):
    """
    将Word文档转换为结构化数据（专用于前端文档处理）
    
    Args:
        file: 上传的Word文档文件 (.docx格式)
    
    Returns:
        Dict: 结构化的模块和功能数据，包含树形结构和扁平结构
        {
            "success": true,
            "modules": {  # 扁平化结构（向后兼容）
                "模块名1": [功能列表],
                "模块名2": [功能列表]
            },
            "tree": [  # 新的树形结构
                {
                    "level": 1,
                    "title": "标题",
                    "children": [...],
                    "sections": [...]
                }
            ],
            "sections": [  # 所有功能的扁平列表
                {
                    "function_name": "功能名称",
                    "module_path": "模块路径", 
                    "full_path": "完整路径",
                    "business_process": "业务流程",
                    "full_description": "完整描述"
                }
            ],
            "metadata": {
                "filename": "文件名",
                "total_modules": 数量,
                "total_functions": 数量
            }
        }
    """
    # 检查文件类型
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件扩展名
    if not (file.filename.endswith('.docx') or file.filename.endswith('.doc')):
        raise HTTPException(status_code=400, detail="仅支持.docx和.doc格式的Word文档")
    
    # 检查文件大小 (限制为10MB)
    max_size = 10 * 1024 * 1024  # 10MB
    file_content = await file.read()
    
    if len(file_content) > max_size:
        raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
    
    if len(file_content) == 0:
        raise HTTPException(status_code=400, detail="文件内容为空")
    
    try:
        print(f"🔥 ===== API /api/convert-word-to-structured 被调用 =====")
        print(f"📁 收到Word文档结构化解析请求: {file.filename}")
        print(f"📏 文件大小: {len(file_content)} 字节")
        print(f"📊 文件类型: {file.content_type}")

        # 保存原始文档到服务器（用于后续导出）
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir, exist_ok=True)

        # 生成唯一文件名避免冲突
        import uuid
        file_id = str(uuid.uuid4())[:8]
        file_extension = os.path.splitext(file.filename)[1]
        saved_filename = f"{file_id}_{file.filename}"
        saved_doc_path = os.path.join(uploads_dir, saved_filename)

        # 保存文件
        with open(saved_doc_path, "wb") as f:
            f.write(file_content)

        print(f"💾 原始文档已保存到: {saved_doc_path}")

        # 调用完整处理流程
        print(f"🚀 开始调用 convert_word_to_structured_data...")
        result_data = convert_word_to_structured_data(file_content)
        
        print(f"✅ 结构化解析完成")
        
        # 提取新格式的数据
        tree = result_data.get('tree', [])
        flat_modules = result_data.get('flat_modules', {})
        sections = result_data.get('sections', [])
        document_sections = result_data.get('document_sections', [])
        
        print(f"📊 树形结构根节点数: {len(tree)}")
        print(f"📊 扁平模块数: {len(flat_modules)}")
        print(f"📋 总功能数: {len(sections)}")
        print(f"📋 文档节点数: {len(document_sections)}")
        
        # 详细输出结果（使用扁平结构保持兼容性）
        for module_name, functions in flat_modules.items():
            print(f"   🔹 模块: {module_name} ({len(functions)} 个功能)")
            for func in functions[:3]:  # 只显示前3个功能
                print(f"      - {func['function_name']}")
            if len(functions) > 3:
                print(f"      ... 还有 {len(functions) - 3} 个功能")
        
        # 构建API响应
        response = {
            "success": True,
            "modules": flat_modules,       # 保持向后兼容的扁平结构
            "tree": tree,                  # 新的树形结构
            "sections": sections,          # 所有功能的详细信息
            "document_sections": document_sections,  # 前端期望的DocumentSection格式
            "saved_doc_path": saved_doc_path,  # 新增：服务器端保存的文档路径
            "metadata": {
                "filename": file.filename,
                "total_modules": len(flat_modules),
                "total_functions": len(sections),
                "tree_nodes": _count_tree_nodes_simple(tree),
                "document_nodes": len(document_sections)
            }
        }
        
        print(f"🎯 准备返回结果给前端")
        print(f"📊 返回数据统计: success={response['success']}, modules={len(response['modules'])}, tree_nodes={response['metadata']['tree_nodes']}")
        
        return response
        
    except Exception as e:
        print(f"❌ 结构化解析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"文档解析失败: {str(e)}")


@app.post("/api/execute-task")
async def execute_task(request: TaskRequest):
    """
    执行AI任务（功能设计、流程图生成、UI原型等）
    
    Args:
        request: 任务请求数据
    
    Returns:
        Dict: 任务执行结果
        {
            "success": true,
            "taskId": "任务ID",
            "taskType": "任务类型", 
            "content": "生成的内容或文件路径",
            "isFile": false,  # 新增：标识是否为文件
            "filePath": null,  # 新增：文件路径（如果是文件）
            "fileInfo": null,  # 新增：文件信息（如果是文件）
            "timestamp": "时间戳"
        }
    """
    try:
        print(f"🎯 ===== API /api/execute-task 被调用 =====")
        print(f"📋 任务ID: {request.taskId}")
        print(f"🔧 任务类型: {request.taskType}")
        print(f"🏷️ 功能名称: {request.functionName}")
        print(f"📍 模块路径: {request.modulePath}")
        
        # 构建任务数据
        task_data = {
            "functionName": request.functionName,
            "modulePath": request.modulePath,
            "businessProcess": request.businessProcess,
            "functionDescription": request.functionDescription,
            "excelIndex": request.excelIndex
        }
        
        # 调用任务处理器（异步）
        print(f"🚀 开始处理任务...")
        start_time = time.time()
        
        result = await task_processor.process_task(request.taskType, task_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"✅ 任务处理完成，耗时: {processing_time:.2f} 秒")
        
        # 检测结果是文件路径还是内容
        is_file = False
        file_path = None
        file_info = None
        content = result
        
        # 检查是否为文件路径
        if isinstance(result, str) and os.path.exists(result):
            is_file = True
            file_path = result
            
            # 获取文件信息
            file_stats = os.stat(result)
            file_size = file_stats.st_size
            file_name = os.path.basename(result)
            file_ext = os.path.splitext(file_name)[1]
            
            file_info = {
                "fileName": file_name,
                "fileExtension": file_ext,
                "fileSize": file_size,
                "fileSizeReadable": f"{file_size / 1024:.1f} KB" if file_size < 1024*1024 else f"{file_size / (1024*1024):.1f} MB",
                "absolutePath": os.path.abspath(result),
                "relativePath": result,
                "createdTime": int(file_stats.st_ctime),
                "modifiedTime": int(file_stats.st_mtime)
            }
            
            print(f"📂 检测到生成文件: {file_name}")
            print(f"📁 文件路径: {result}")
            print(f"📏 文件大小: {file_info['fileSizeReadable']}")
            
            # 对于某些文件类型，也返回部分内容
            if file_ext.lower() in ['.svg', '.html', '.txt', '.md']:
                try:
                    with open(result, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"📄 已读取文件内容，长度: {len(content)} 字符")
                except Exception as e:
                    print(f"⚠️ 读取文件内容失败: {str(e)}")
                    content = f"文件已生成: {file_name}"
            else:
                content = f"文件已生成: {file_name}"
        else:
            print(f"📄 返回内容（非文件），长度: {len(str(result))} 字符")
        
        # 返回结果
        response = {
            "success": True,
            "taskId": request.taskId,
            "taskType": request.taskType,
            "content": content,
            "isFile": is_file,
            "filePath": file_path,
            "fileInfo": file_info,
            "timestamp": int(time.time()),
            "metadata": {
                "functionName": request.functionName,
                "modulePath": request.modulePath,
                "contentLength": len(str(content)),
                "processingTime": round(processing_time, 2),
                "resultType": "file" if is_file else "content"
            }
        }
        
        print(f"🎯 任务执行成功，准备返回结果")
        print(f"📊 返回类型: {'文件' if is_file else '内容'}")
        return response
        
    except Exception as e:
        print(f"❌ 任务执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"任务执行失败: {str(e)}")


@app.get("/api/download-file")
async def download_file(file_path: str, preview: bool = False):
    """
    下载生成的文件或获取文件内容预览
    
    Args:
        file_path: 文件路径（相对路径或绝对路径）
        preview: 是否为预览模式，true时返回文件内容，false时下载文件
    
    Returns:
        FileResponse: 文件下载响应（preview=false）
        PlainTextResponse: 文件内容（preview=true）
    """
    try:
        print(f"📥 ===== API /api/download-file 被调用 =====")
        print(f"📁 请求文件: {file_path}")
        print(f"🔍 预览模式: {preview}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")
        
        # 检查文件是否在允许的目录内（安全检查）
        abs_file_path = os.path.abspath(file_path)
        allowed_dirs = [
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "generated_files")),
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "output"))
        ]
        
        is_allowed = False
        for allowed_dir in allowed_dirs:
            if abs_file_path.startswith(allowed_dir):
                is_allowed = True
                break
        
        if not is_allowed:
            print(f"❌ 文件路径不在允许的目录内: {abs_file_path}")
            raise HTTPException(status_code=403, detail="文件路径不被允许")
        
        # 获取文件信息
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        print(f"✅ 访问文件: {file_name}")
        print(f"📏 文件大小: {file_size} 字节")
        
        # 根据文件扩展名设置媒体类型
        file_ext = os.path.splitext(file_name)[1].lower()
        media_type_map = {
            '.svg': 'image/svg+xml',
            '.html': 'text/html',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.json': 'application/json',
            '.pdf': 'application/pdf'
        }
        
        media_type = media_type_map.get(file_ext, 'application/octet-stream')
        
        # 预览模式：返回文件内容
        if preview:
            print(f"🔍 预览模式：读取文件内容")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✅ 文件内容读取成功，长度: {len(content)} 字符")
                
                # 根据文件类型返回适当的响应
                from fastapi.responses import PlainTextResponse
                return PlainTextResponse(
                    content=content,
                    media_type=media_type
                )
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试其他编码
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                    print(f"✅ 使用GBK编码读取成功，长度: {len(content)} 字符")
                    from fastapi.responses import PlainTextResponse
                    return PlainTextResponse(
                        content=content,
                        media_type=media_type
                    )
                except Exception as e:
                    print(f"❌ 文件编码读取失败: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"无法读取文件内容，可能是编码问题: {str(e)}")
        
        # 下载模式：返回文件下载响应
        else:
            print(f"📥 下载模式：返回文件下载")
            return FileResponse(
                path=file_path,
                filename=file_name,
                media_type=media_type
            )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 处理文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"处理文件失败: {str(e)}")


@app.get("/api/list-generated-files")
async def list_generated_files():
    """
    列出所有生成的文件
    
    Returns:
        Dict: 文件列表
        {
            "success": true,
            "files": [
                {
                    "fileName": "文件名",
                    "filePath": "文件路径",
                    "fileSize": 文件大小,
                    "fileType": "文件类型",
                    "createdTime": "创建时间",
                    "modifiedTime": "修改时间"
                }
            ],
            "total": 文件总数
        }
    """
    try:
        print(f"📋 ===== API /api/list-generated-files 被调用 =====")
        
        # 检查生成文件目录
        generated_files_dir = os.path.join(os.path.dirname(__file__), "..", "generated_files")
        output_dir = os.path.join(os.path.dirname(__file__), "..", "output")
        
        files_list = []
        
        # 扫描生成文件目录
        for directory in [generated_files_dir, output_dir]:
            if os.path.exists(directory):
                for file_name in os.listdir(directory):
                    file_path = os.path.join(directory, file_name)
                    if os.path.isfile(file_path):
                        file_stats = os.stat(file_path)
                        file_ext = os.path.splitext(file_name)[1].lower()
                        
                        # 确定文件类型
                        file_type = "unknown"
                        if file_ext == '.svg':
                            file_type = "flowchart"
                        elif file_ext == '.html':
                            file_type = "ui_prototype" 
                        elif file_ext in ['.txt', '.md']:
                            file_type = "document"
                        
                        files_list.append({
                            "fileName": file_name,
                            "filePath": file_path,
                            "fileSize": file_stats.st_size,
                            "fileSizeReadable": f"{file_stats.st_size / 1024:.1f} KB" if file_stats.st_size < 1024*1024 else f"{file_stats.st_size / (1024*1024):.1f} MB",
                            "fileType": file_type,
                            "fileExtension": file_ext,
                            "createdTime": int(file_stats.st_ctime),
                            "modifiedTime": int(file_stats.st_mtime),
                            "directory": os.path.basename(directory)
                        })
        
        # 按修改时间倒序排列
        files_list.sort(key=lambda x: x['modifiedTime'], reverse=True)
        
        print(f"📊 找到 {len(files_list)} 个生成的文件")
        
        return {
            "success": True,
            "files": files_list,
            "total": len(files_list),
            "directories": [
                {"name": "generated_files", "path": generated_files_dir, "exists": os.path.exists(generated_files_dir)},
                {"name": "output", "path": output_dir, "exists": os.path.exists(output_dir)}
            ]
        }
        
    except Exception as e:
        print(f"❌ 获取文件列表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")


def _count_tree_nodes_simple(tree_nodes):
    """
    简单计算树节点数量的辅助函数
    """
    count = 0
    for node in tree_nodes:
        count += 1
        if node.get('children'):
            count += _count_tree_nodes_simple(node['children'])
    return count


@app.post("/api/refine-html")
async def refine_html(request: HTMLRefineRequest) -> HTMLRefineResponse:
    """
    HTML代码微调接口
    根据用户的自然语言请求对HTML代码进行精确修改
    """
    try:
        # 初始化HTML修改器
        refiner = HTMLRefiner()
        
        # 确定HTML文件路径
        html_file_path = None
        if request.html_file_path and request.html_file_path.strip():
            # 如果前端提供了文件路径，直接使用
            html_file_path = request.html_file_path.strip()
            print(f"📁 [RefineHTML] 使用前端提供的文件路径: {html_file_path}")
        elif request.task_id and request.task_id.strip():
            # 如果前端提供了task_id，尝试自动查找对应的HTML文件
            html_file_path = refiner.find_html_file_by_task_id(request.task_id.strip())
            if html_file_path:
                print(f"📁 [RefineHTML] 根据task_id自动找到文件路径: {html_file_path}")
            else:
                print(f"⚠️ [RefineHTML] 无法根据task_id找到对应的HTML文件: {request.task_id}")
        
        # 执行HTML微调（支持文件路径参数）
        result = await refiner.refine_html(request.html_content, request.user_request, html_file_path)
        
        # 返回结果
        return HTMLRefineResponse(
            success=result.get("success", False),
            modified_html=result.get("modified_html", ""),
            modification_summary=result.get("modification_summary", ""),
            error=result.get("error", ""),
            target_anchor=result.get("target_anchor", ""),
            modification_type=result.get("modification_type", ""),
            analysis_comment=result.get("analysis_comment", ""),
            backup_created=result.get("backup_created", ""),
            file_updated=result.get("file_updated", ""),
            backup_history=result.get("backup_history", [])
        )
        
    except Exception as e:
        print(f"HTML微调接口异常: {e}")
        import traceback
        traceback.print_exc()
        return HTMLRefineResponse(
            success=False,
            error=str(e),
            modified_html=request.html_content
        )

@app.post("/api/refine-html-file")
async def refine_html_file(html_file_path: str, user_request: str, task_id: str = ""):
    """
    直接修改HTML文件的接口（支持版本备份）
    """
    try:
        print(f"🔧 ===== API /api/refine-html-file 被调用 =====")
        print(f"📁 目标文件: {html_file_path}")
        print(f"📝 修改请求: {user_request}")
        
        # 初始化HTML修改器
        refiner = HTMLRefiner()
        
        # 直接修改HTML文件
        result = refiner.refine_html_file(html_file_path, user_request)
        
        print(f"🎯 修改结果: {'成功' if result.get('success') else '失败'}")
        
        # 返回结果
        return {
            "success": result.get("success", False),
            "modified_html": result.get("modified_html", ""),
            "modification_summary": result.get("modification_summary", ""),
            "error": result.get("error", ""),
            "target_anchor": result.get("target_anchor", ""),
            "modification_type": result.get("modification_type", ""),
            "analysis_comment": result.get("analysis_comment", ""),
            "backup_created": result.get("backup_created", ""),
            "file_updated": result.get("file_updated", ""),
            "backup_history": result.get("backup_history", []),
            "task_id": task_id
        }
        
    except Exception as e:
        print(f"❌ HTML文件修改接口异常: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "modified_html": "",
            "task_id": task_id
        }

@app.post("/api/analyze-html-structure")
async def analyze_html_structure(request: HTMLRefineRequest):
    """
    分析HTML结构，列出可用的slots和chunks
    """
    try:
        refiner = HTMLRefiner()
        
        slots = refiner.list_html_slots(request.html_content)
        chunks = refiner.list_html_chunks(request.html_content)
        
        return {
            "success": True,
            "slots": slots,
            "chunks": chunks,
            "total_anchors": len(slots) + len(chunks)
        }
        
    except Exception as e:
        print(f"分析HTML结构失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "slots": [],
            "chunks": []
        }

@app.post("/api/save-file")
async def save_file(request: SaveFileRequest):
    """
    保存文件内容到服务端

    Args:
        request: 保存文件请求数据

    Returns:
        Dict: 保存结果
        {
            "success": true,
            "message": "文件保存成功",
            "file_path": "保存的文件路径",
            "backup_created": "备份文件路径"
        }
    """
    try:
        print(f"📁 ===== API /api/save-file 被调用 =====")
        print(f"📄 文件路径: {request.file_path}")
        print(f"📝 内容长度: {len(request.content)} 字符")
        print(f"🏷️ 任务ID: {request.task_id}")

        # 验证文件路径
        if not request.file_path:
            raise HTTPException(status_code=400, detail="文件路径不能为空")

        # 确保文件路径是绝对路径
        if not os.path.isabs(request.file_path):
            # 如果是相对路径，基于当前工作目录构建绝对路径
            base_dir = os.path.dirname(__file__)
            request.file_path = os.path.join(base_dir, "..", request.file_path)
            request.file_path = os.path.abspath(request.file_path)

        print(f"📍 绝对文件路径: {request.file_path}")

        # 确保目录存在
        file_dir = os.path.dirname(request.file_path)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir, exist_ok=True)
            print(f"📁 创建目录: {file_dir}")

        # 创建备份（如果原文件存在）
        backup_path = ""
        if os.path.exists(request.file_path):
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{os.path.splitext(os.path.basename(request.file_path))[0]}_backup_{timestamp}{os.path.splitext(request.file_path)[1]}"
            backup_path = os.path.join(file_dir, backup_filename)

            # 复制原文件作为备份
            import shutil
            shutil.copy2(request.file_path, backup_path)
            print(f"💾 创建备份: {backup_path}")

        # 保存新内容
        with open(request.file_path, 'w', encoding='utf-8') as f:
            f.write(request.content)

        print(f"✅ 文件保存成功: {request.file_path}")

        return SaveFileResponse(
            success=True,
            message="文件保存成功",
            file_path=request.file_path,
            backup_created=backup_path
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 保存文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")


# Word图像插入API端点
@app.post("/api/word/insert-image")
async def insert_image_to_word(request: WordImageInsertRequest):
    """
    将图像插入到Word文档中
    """
    try:
        # 创建Word插图器
        config = WordInsertionConfig()
        inserter = WordImageInserter(config)

        # 准备插入配置
        image_configs = []
        for insertion in request.insertions:
            # 根据内容类型确定源文件路径
            if insertion.content_type == 'svg':
                source_path = f"generated_files/{insertion.content_path}"
            elif insertion.content_type == 'html':
                source_path = f"generated_files/{insertion.content_path}"
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported content type: {insertion.content_type}")

            # 检查源文件是否存在
            if not os.path.exists(source_path):
                raise HTTPException(status_code=404, detail=f"Source file not found: {source_path}")

            image_configs.append({
                'source': source_path,
                'keyword_pattern': insertion.keyword_pattern,
                'options': {
                    'image_width': insertion.image_width,
                    'format': 'png'
                }
            })

        # 执行批量插入
        result = await inserter.process_batch(
            request.doc_path,
            image_configs,
            request.output_path
        )

        # 处理结果，确保JSON可序列化
        success_details = []
        for item in result['success']:
            success_details.append({
                'source': item['config']['source'],
                'keyword_pattern': item['config']['keyword_pattern'],
                'image_path': item['image_path'],
                'insertion_paragraph': item['insertion_point'].paragraph_index if hasattr(item['insertion_point'], 'paragraph_index') else 'unknown'
            })

        failed_details = []
        for item in result['failed']:
            failed_details.append({
                'source': item['config']['source'] if 'config' in item else 'unknown',
                'error': item['error']
            })

        return {
            "success": True,
            "output_path": result['output_path'],
            "insertions_count": len(result['success']),
            "failed_count": len(result['failed']),
            "success_details": success_details,
            "failed_details": failed_details
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image insertion failed: {str(e)}")

@app.post("/api/word/batch-insert")
async def batch_insert_from_task_results(request: WordBatchInsertRequest):
    """
    从任务执行结果批量插入图像到Word文档
    """
    try:
        # 处理文档路径
        doc_path = request.doc_path
        if not os.path.isabs(doc_path):
            # 如果不是绝对路径，尝试在常见目录中查找
            possible_paths = [
                doc_path,  # 当前目录
                f"uploads/{doc_path}",  # 上传目录
                f"generated_files/{doc_path}",  # 生成文件目录
                f"temp/{doc_path}"  # 临时目录
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    doc_path = path
                    break
            else:
                raise HTTPException(status_code=404, detail=f"Document not found: {request.doc_path}")

        # 创建Word插图器
        config = WordInsertionConfig()
        inserter = WordImageInserter(config)

        # 从任务结果构建插入配置
        image_configs = []

        for task_result in request.task_results:
            task_id = task_result.get('id', '')
            task_type = task_result.get('type', '')
            status = task_result.get('status', '')

            print(f"🔍 处理任务: {task_id} ({task_type}) - {status}")

            # 只处理已完成的任务
            if status != 'completed':
                print(f"  ⏭️ 跳过未完成任务")
                continue

            # 根据任务类型确定关键词模式和源文件
            if task_type == 'flowchart':
                # 查找对应的SVG文件
                generated_files_dir = 'generated_files'
                if os.path.exists(generated_files_dir):
                    # 多种查找策略
                    svg_files = []

                    # 策略1: 精确匹配任务ID
                    svg_files = [f for f in os.listdir(generated_files_dir) if f.startswith(f'flowchart_{task_id}') and f.endswith('.svg')]

                    # 策略2: 如果任务ID包含section信息，尝试提取section部分
                    if not svg_files and '-' in task_id:
                        section_part = task_id.split('-')[1]  # 从section-5-flowchart提取5
                        svg_files = [f for f in os.listdir(generated_files_dir) if f'section_{section_part}' in f and f.endswith('.svg')]
                        if svg_files:
                            print(f"🎯 通过section匹配找到流程图文件: {svg_files}")

                    # 策略3: 基于任务ID中的section信息进行更灵活的匹配
                    if not svg_files and 'section-' in task_id:
                        # 从section-5-flowchart中提取section-5
                        section_id = '-'.join(task_id.split('-')[:2])  # section-5
                        svg_files = [f for f in os.listdir(generated_files_dir) if section_id.replace('-', '_') in f and f.endswith('.svg')]
                        if svg_files:
                            print(f"🎯 通过section ID匹配找到流程图文件: {svg_files}")

                    # 策略4: 模糊匹配，查找最新的flowchart文件
                    if not svg_files:
                        all_svg_files = [f for f in os.listdir(generated_files_dir) if f.startswith('flowchart_') and f.endswith('.svg')]
                        if all_svg_files:
                            # 按修改时间排序，取最新的
                            all_svg_files.sort(key=lambda x: os.path.getmtime(os.path.join(generated_files_dir, x)), reverse=True)
                            svg_files = [all_svg_files[0]]
                            print(f"🎯 使用最新的流程图文件: {svg_files[0]}")

                    if svg_files:
                        svg_path = os.path.join(generated_files_dir, svg_files[0])
                        print(f"🎨 找到流程图文件: {svg_path}")
                        image_configs.append({
                            'source': svg_path,
                            'keyword_pattern': request.config.get('business_flow_pattern', '三、业务流程'),
                            'options': {'image_width': 5.0, 'format': 'png'}
                        })
                    else:
                        print(f"⚠️ 未找到任务 {task_id} 的流程图文件")
                        # 列出所有可用的SVG文件用于调试
                        all_files = os.listdir(generated_files_dir)
                        svg_files_debug = [f for f in all_files if f.endswith('.svg')]
                        print(f"📋 可用的SVG文件: {svg_files_debug}")
                        print(f"📋 查找模式: flowchart_{task_id}_*.svg, section_{task_id.split('-')[1]}_*.svg")
                else:
                    print(f"⚠️ generated_files目录不存在")

            elif task_type == 'ui_prototype':
                # 查找对应的HTML文件
                generated_files_dir = 'generated_files'
                if os.path.exists(generated_files_dir):
                    # 多种查找策略
                    html_files = []

                    # 策略1: 精确匹配任务ID
                    html_files = [f for f in os.listdir(generated_files_dir) if f.startswith(f'ui_prototype_{task_id}') and f.endswith('.html')]

                    # 策略2: 如果任务ID包含section信息，尝试提取section部分
                    if not html_files and '-' in task_id:
                        section_part = task_id.split('-')[1]  # 从section-5-ui_prototype提取5
                        html_files = [f for f in os.listdir(generated_files_dir) if f'section_{section_part}' in f and f.endswith('.html')]
                        if html_files:
                            print(f"🎯 通过section匹配找到UI原型文件: {html_files}")

                    # 策略3: 基于任务ID中的section信息进行更灵活的匹配
                    if not html_files and 'section-' in task_id:
                        # 从section-5-ui_prototype中提取section-5
                        section_id = '-'.join(task_id.split('-')[:2])  # section-5
                        html_files = [f for f in os.listdir(generated_files_dir) if section_id.replace('-', '_') in f and f.endswith('.html')]
                        if html_files:
                            print(f"🎯 通过section ID匹配找到UI原型文件: {html_files}")

                    # 策略4: 模糊匹配，查找最新的ui_prototype文件
                    if not html_files:
                        all_html_files = [f for f in os.listdir(generated_files_dir) if f.startswith('ui_prototype_') and f.endswith('.html')]
                        if all_html_files:
                            # 按修改时间排序，取最新的
                            all_html_files.sort(key=lambda x: os.path.getmtime(os.path.join(generated_files_dir, x)), reverse=True)
                            html_files = [all_html_files[0]]
                            print(f"🎯 使用最新的UI原型文件: {html_files[0]}")

                    if html_files:
                        html_path = os.path.join(generated_files_dir, html_files[0])
                        print(f"🖥️ 找到UI原型文件: {html_path}")
                        image_configs.append({
                            'source': html_path,
                            'keyword_pattern': request.config.get('operation_flow_pattern', '四、操作流程'),
                            'options': {'image_width': 5.5, 'format': 'png'}
                        })
                    else:
                        print(f"⚠️ 未找到任务 {task_id} 的UI原型文件")
                        # 列出所有可用的HTML文件用于调试
                        all_files = os.listdir(generated_files_dir)
                        html_files_debug = [f for f in all_files if f.endswith('.html')]
                        print(f"📋 可用的HTML文件: {html_files_debug}")
                        print(f"📋 查找模式: ui_prototype_{task_id}_*.html, section_{task_id.split('-')[1]}_*.html")
                else:
                    print(f"⚠️ generated_files目录不存在")

        if not image_configs:
            return {
                "success": True,
                "message": "No images to insert",
                "output_path": request.doc_path,
                "insertions_count": 0
            }

        # 执行批量插入
        result = await inserter.process_batch(
            doc_path,  # 使用处理后的文档路径
            image_configs,
            request.output_path
        )

        # 处理结果，确保JSON可序列化
        success_details = []
        for item in result['success']:
            success_details.append({
                'source': item['config']['source'],
                'keyword_pattern': item['config']['keyword_pattern'],
                'image_path': item['image_path'],
                'insertion_paragraph': item['insertion_point'].paragraph_index if hasattr(item['insertion_point'], 'paragraph_index') else 'unknown'
            })

        failed_details = []
        for item in result['failed']:
            failed_details.append({
                'source': item['config']['source'] if 'config' in item else 'unknown',
                'error': item['error']
            })

        return {
            "success": True,
            "output_path": result['output_path'],
            "insertions_count": len(result['success']),
            "failed_count": len(result['failed']),
            "success_details": success_details,
            "failed_details": failed_details
        }

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Batch insertion failed: {e}")
        logger.error(f"Full traceback: {error_details}")
        raise HTTPException(status_code=500, detail=f"Batch insertion failed: {str(e)}\nDetails: {error_details}")

@app.post("/api/word/preview")
async def preview_word_document(request: WordPreviewRequest):
    """
    生成Word文档预览
    """
    try:
        # 处理文档路径
        doc_path = request.doc_path
        if not os.path.isabs(doc_path):
            # 如果不是绝对路径，尝试在常见目录中查找
            possible_paths = [
                doc_path,  # 当前目录
                f"uploads/{doc_path}",  # 上传目录
                f"generated_files/{doc_path}",  # 生成文件目录
                f"temp/{doc_path}"  # 临时目录
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    doc_path = path
                    break
            else:
                raise HTTPException(status_code=404, detail=f"Document not found: {request.doc_path}")

        # 创建Word插图器
        config = WordInsertionConfig()
        inserter = WordImageInserter(config)

        if request.preview_type == 'dual':
            # 使用增强预览管理器
            try:
                from word_image_insertion.core.enhanced_preview import EnhancedPreviewManager
                preview_manager = EnhancedPreviewManager()
                viewer_path = preview_manager.create_dual_view_preview(doc_path)

                return {
                    "success": True,
                    "preview_type": "dual",
                    "viewer_url": f"/api/files/preview/{os.path.basename(viewer_path)}",
                    "viewer_path": viewer_path
                }
            except Exception as e:
                print(f"⚠️ 增强预览失败: {str(e)}")
                # 如果增强预览不可用，降级到标准预览
                try:
                    preview_path = inserter.preview_document(doc_path, 'pdf')
                    return {
                        "success": True,
                        "preview_type": "pdf",
                        "preview_url": f"/api/files/preview/{os.path.basename(preview_path)}",
                        "preview_path": preview_path,
                        "message": f"Enhanced preview not available ({str(e)}), using standard PDF preview"
                    }
                except Exception as pdf_error:
                    print(f"⚠️ PDF预览也失败: {str(pdf_error)}")
                    # 如果PDF预览也失败，返回错误但不中断整个流程
                    return {
                        "success": False,
                        "preview_type": "none",
                        "message": f"Preview generation failed: LibreOffice not found. Please install LibreOffice for preview functionality.",
                        "error": str(e)
                    }
        else:
            # 使用标准预览
            try:
                preview_path = inserter.preview_document(doc_path, request.preview_type)

                return {
                    "success": True,
                    "preview_type": request.preview_type,
                    "preview_url": f"/api/files/preview/{os.path.basename(preview_path)}",
                    "preview_path": preview_path
                }
            except Exception as e:
                print(f"⚠️ 标准预览失败: {str(e)}")
                return {
                    "success": False,
                    "preview_type": "none",
                    "message": f"Preview generation failed: LibreOffice not found. Please install LibreOffice for preview functionality.",
                    "error": str(e)
                }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Preview generation failed: {str(e)}")

@app.get("/api/files/download/{file_path:path}")
async def download_file_by_path(file_path: str):
    """
    通过路径下载文件（前端兼容性端点）

    Args:
        file_path: 文件路径

    Returns:
        FileResponse: 文件下载响应
    """
    try:
        print(f"📥 ===== API /api/files/download/{file_path} 被调用 =====")

        # URL解码
        import urllib.parse
        decoded_path = urllib.parse.unquote(file_path)
        print(f"📁 解码后的文件路径: {decoded_path}")

        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            print(f"❌ 文件不存在: {decoded_path}")
            raise HTTPException(status_code=404, detail=f"文件不存在: {decoded_path}")

        # 检查文件是否在允许的目录内（安全检查）
        abs_file_path = os.path.abspath(decoded_path)
        allowed_dirs = [
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "generated_files")),
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "output")),
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "uploads")),  # 添加uploads目录
            os.path.abspath("uploads"),  # 当前目录下的uploads
            os.path.abspath("generated_files"),  # 当前目录下的generated_files
        ]

        is_allowed = False
        for allowed_dir in allowed_dirs:
            if abs_file_path.startswith(allowed_dir):
                is_allowed = True
                print(f"✅ 文件在允许的目录内: {allowed_dir}")
                break

        if not is_allowed:
            print(f"❌ 文件不在允许的目录内: {abs_file_path}")
            print(f"允许的目录: {allowed_dirs}")
            raise HTTPException(status_code=403, detail="文件访问被拒绝")

        # 获取文件名
        file_name = os.path.basename(decoded_path)

        # 确定MIME类型
        import mimetypes
        media_type, _ = mimetypes.guess_type(decoded_path)
        if not media_type:
            media_type = 'application/octet-stream'

        print(f"📥 开始下载文件: {file_name}")
        print(f"📊 文件类型: {media_type}")

        return FileResponse(
            path=decoded_path,
            filename=file_name,
            media_type=media_type
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 下载文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@app.get("/api/files/preview/{file_path:path}")
async def preview_file_by_path(file_path: str):
    """
    通过路径预览文件（前端兼容性端点）

    Args:
        file_path: 文件路径

    Returns:
        FileResponse: 文件预览响应
    """
    try:
        print(f"👁️ ===== API /api/files/preview/{file_path} 被调用 =====")

        # URL解码
        import urllib.parse
        decoded_path = urllib.parse.unquote(file_path)
        print(f"📁 解码后的文件路径: {decoded_path}")

        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            print(f"❌ 文件不存在: {decoded_path}")
            raise HTTPException(status_code=404, detail=f"文件不存在: {decoded_path}")

        # 检查文件是否在允许的目录内（安全检查）
        abs_file_path = os.path.abspath(decoded_path)
        allowed_dirs = [
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "generated_files")),
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "output")),
            os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "uploads")),
            os.path.abspath("uploads"),
            os.path.abspath("generated_files"),
            os.path.abspath("temp"),  # 添加temp目录用于预览文件
        ]

        is_allowed = False
        for allowed_dir in allowed_dirs:
            if abs_file_path.startswith(allowed_dir):
                is_allowed = True
                print(f"✅ 文件在允许的目录内: {allowed_dir}")
                break

        if not is_allowed:
            print(f"❌ 文件不在允许的目录内: {abs_file_path}")
            print(f"允许的目录: {allowed_dirs}")
            raise HTTPException(status_code=403, detail="文件访问被拒绝")

        # 获取文件名
        file_name = os.path.basename(decoded_path)

        # 确定MIME类型
        import mimetypes
        media_type, _ = mimetypes.guess_type(decoded_path)
        if not media_type:
            # 根据文件扩展名设置特定的MIME类型
            ext = os.path.splitext(decoded_path)[1].lower()
            if ext == '.pdf':
                media_type = 'application/pdf'
            elif ext == '.html':
                media_type = 'text/html'
            else:
                media_type = 'application/octet-stream'

        print(f"👁️ 开始预览文件: {file_name}")
        print(f"📊 文件类型: {media_type}")

        return FileResponse(
            path=decoded_path,
            filename=file_name,
            media_type=media_type
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 预览文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"预览文件失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """
    详细的健康检查端点
    """
    return {
        "status": "healthy",
        "services": {
            "word_converter": "available",
            "api": "running",
            "html_refiner": "available",
            "word_image_insertion": "available"
        },
        "message": "所有服务正常运行"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True) 