"""
Word插图写入模块

实现向Word文档指定位置插入图片，支持格式控制和尺寸适配。
核心功能：在命中的段落 run.add_picture()，自动计算尺寸和格式。
"""

import logging
import shutil
from pathlib import Path
from typing import Optional, Dict, Any, Union
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from PIL import Image

from .document_parser import InsertionPoint

logger = logging.getLogger(__name__)


class WordInserter:
    """Word插图写入器"""
    
    def __init__(self, config=None):
        self.config = config
        self.default_dpi = 300
        self.max_width_inches = 6.5  # Word页面默认最大宽度
        self.min_width_inches = 2.0  # 最小宽度
        self.auto_scale = True  # 启用自动缩放
        
    def insert(self, doc_path: str, insertion_point: InsertionPoint, 
              image_path: str, output_path: Optional[str] = None) -> str:
        """
        插入图片到Word文档
        
        Args:
            doc_path: 源文档路径
            insertion_point: 插入点信息
            image_path: 图片路径
            output_path: 输出文档路径
            
        Returns:
            str: 输出文档路径
        """
        doc_path = Path(doc_path)
        image_path = Path(image_path)
        
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        if not image_path.exists():
            raise FileNotFoundError(f"Image not found: {image_path}")
        
        logger.info(f"Inserting image {image_path} into document {doc_path}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        else:
            output_path = Path(output_path)
        
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 加载文档
            doc = Document(doc_path)
            
            # 获取目标段落
            target_paragraph = doc.paragraphs[insertion_point.paragraph_index]
            
            # 计算图片尺寸
            image_width = self._calculate_image_width(image_path)
            
            # 在目标段落后插入新段落来放置图片
            self._insert_image_after_target_paragraph(doc, insertion_point.paragraph_index, image_path, image_width)
            
            # 保存文档
            doc.save(output_path)
            
            logger.info(f"Image inserted successfully, saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to insert image: {e}")
            raise
    
    def insert_multiple(self, doc_path: str, insertions: list, 
                       output_path: Optional[str] = None) -> str:
        """
        批量插入图片
        
        Args:
            doc_path: 源文档路径
            insertions: 插入配置列表，每个元素包含 (insertion_point, image_path)
            output_path: 输出文档路径
            
        Returns:
            str: 输出文档路径
        """
        doc_path = Path(doc_path)
        
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Batch inserting {len(insertions)} images into document {doc_path}")
        
        # 确定输出路径
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        else:
            output_path = Path(output_path)
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 加载文档
            doc = Document(doc_path)
            
            # 按段落索引排序（从后往前插入，避免索引变化）
            sorted_insertions = sorted(insertions, 
                                     key=lambda x: x[0].paragraph_index, 
                                     reverse=True)
            
            success_count = 0
            
            for insertion_point, image_path in sorted_insertions:
                try:
                    image_path = Path(image_path)
                    if not image_path.exists():
                        logger.warning(f"Image not found, skipping: {image_path}")
                        continue
                    
                    # 获取目标段落
                    target_paragraph = doc.paragraphs[insertion_point.paragraph_index]
                    
                    # 计算图片尺寸
                    image_width = self._calculate_image_width(image_path)
                    
                    # 插入图片
                    self._insert_image_to_paragraph(target_paragraph, image_path, image_width)
                    
                    success_count += 1
                    logger.debug(f"Inserted image: {image_path}")
                    
                except Exception as e:
                    logger.error(f"Failed to insert image {image_path}: {e}")
                    continue
            
            # 保存文档
            doc.save(output_path)
            
            logger.info(f"Batch insertion completed: {success_count}/{len(insertions)} images inserted")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to batch insert images: {e}")
            raise
    
    def _insert_image_after_paragraph(self, doc, target_paragraph, image_path: Path, width: float):
        """在目标段落后插入新段落并添加图片"""
        try:
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml import parse_xml
            from docx.oxml.ns import nsdecls, qn

            # 找到目标段落在文档中的位置
            paragraphs = doc.paragraphs
            target_index = None

            # target_paragraph已经是文档中的段落对象，直接查找索引
            for i, p in enumerate(paragraphs):
                if p == target_paragraph:
                    target_index = i
                    break

            if target_index is None:
                raise ValueError("Target paragraph not found in document")

            # 方法1：尝试在目标段落的XML元素后插入新段落
            try:
                # 获取目标段落的XML元素
                target_p_element = target_paragraph._element
                parent = target_p_element.getparent()

                # 创建新的段落XML元素
                new_p_xml = f'<w:p {nsdecls("w")}><w:pPr><w:jc w:val="center"/></w:pPr></w:p>'
                new_p_element = parse_xml(new_p_xml)

                # 在目标段落后插入新段落
                parent.insert(parent.index(target_p_element) + 1, new_p_element)

                # 创建新段落对象
                from docx.text.paragraph import Paragraph
                new_paragraph = Paragraph(new_p_element, parent)

                # 在新段落中插入图片
                run = new_paragraph.add_run()
                run.add_picture(str(image_path), width=Inches(width))

                logger.debug(f"Image inserted in new paragraph after target with width {width} inches")

            except Exception as xml_error:
                logger.warning(f"XML insertion failed, using fallback method: {xml_error}")

                # 方法2：回退到在目标段落末尾添加图片
                # 添加空行分隔
                run = target_paragraph.add_run()
                run.add_break()
                run.add_break()

                # 插入图片
                picture_run = target_paragraph.add_run()
                picture_run.add_picture(str(image_path), width=Inches(width))

                # 设置段落居中对齐
                target_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                logger.debug(f"Image inserted at end of target paragraph with width {width} inches")

        except Exception as e:
            logger.error(f"Failed to insert image after paragraph: {e}")
            raise

    def _insert_image_after_target_paragraph(self, doc, target_paragraph_index: int, image_path: Path, width: float):
        """在目标段落后插入新段落来放置图片"""
        try:
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 获取目标段落
            target_paragraph = doc.paragraphs[target_paragraph_index]

            # 在目标段落后插入新段落
            # 找到目标段落在文档中的位置
            target_element = target_paragraph._element
            parent = target_element.getparent()

            # 创建新的段落元素
            from docx.oxml import parse_xml
            from docx.oxml.ns import nsdecls, qn

            # 创建新段落
            new_p_xml = f'<w:p {nsdecls("w")}></w:p>'
            new_p_element = parse_xml(new_p_xml)

            # 在目标段落后插入新段落
            parent.insert(parent.index(target_element) + 1, new_p_element)

            # 获取新创建的段落对象
            new_paragraph = None
            for i, p in enumerate(doc.paragraphs):
                if p._element == new_p_element:
                    new_paragraph = p
                    break

            if new_paragraph is None:
                raise Exception("Failed to create new paragraph")

            # 在新段落中插入图片
            run = new_paragraph.add_run()
            run.add_picture(str(image_path), width=Inches(width))

            # 设置段落居中对齐
            new_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加段落间距
            from docx.shared import Pt
            paragraph_format = new_paragraph.paragraph_format
            paragraph_format.space_before = Pt(6)
            paragraph_format.space_after = Pt(6)

            logger.debug(f"Image inserted in new paragraph after target paragraph with width {width} inches")

        except Exception as e:
            logger.error(f"Failed to insert image after target paragraph: {e}")
            raise
    
    def _calculate_image_width(self, image_path: Path) -> float:
        """计算图片在Word中的显示宽度（英寸）- 增强版"""
        try:
            with Image.open(image_path) as img:
                width_px, height_px = img.size

                # 获取DPI信息
                dpi = img.info.get('dpi', (self.default_dpi, self.default_dpi))
                if isinstance(dpi, (int, float)):
                    dpi_x = dpi_y = dpi
                else:
                    dpi_x, dpi_y = dpi

                # 计算英寸尺寸
                width_inches = width_px / dpi_x
                height_inches = height_px / dpi_y

                # 智能缩放逻辑
                if self.auto_scale:
                    # 根据图片内容类型调整大小
                    aspect_ratio = width_px / height_px

                    if aspect_ratio > 2.0:  # 宽图（如流程图）
                        target_width = min(self.max_width_inches, max(4.5, width_inches))
                    elif aspect_ratio < 0.7:  # 高图（如界面截图）
                        target_width = min(self.max_width_inches * 0.8, max(3.5, width_inches))
                    else:  # 方图
                        target_width = min(self.max_width_inches * 0.9, max(4.0, width_inches))

                    # 确保不小于最小宽度
                    width_inches = max(self.min_width_inches, target_width)

                # 最终限制检查
                if width_inches > self.max_width_inches:
                    width_inches = self.max_width_inches
                elif width_inches < self.min_width_inches:
                    width_inches = self.min_width_inches

                logger.debug(f"Calculated image width: {width_inches:.2f} inches "
                           f"(original: {width_px}x{height_px}px at {dpi_x} DPI, aspect: {width_px/height_px:.2f})")

                return width_inches

        except Exception as e:
            logger.error(f"Failed to calculate image width: {e}")
            # 返回默认宽度
            return 4.5
    
    def _generate_output_path(self, doc_path: Path) -> Path:
        """生成输出路径"""
        stem = doc_path.stem
        suffix = doc_path.suffix
        parent = doc_path.parent
        
        # 添加时间戳避免覆盖
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return parent / f"{stem}_with_images_{timestamp}{suffix}"
    
    def insert_at_bookmark(self, doc_path: str, bookmark_name: str, 
                          image_path: str, output_path: Optional[str] = None) -> str:
        """
        在书签位置插入图片
        
        Args:
            doc_path: 文档路径
            bookmark_name: 书签名称
            image_path: 图片路径
            output_path: 输出路径
            
        Returns:
            str: 输出文档路径
        """
        # 注意：python-docx对书签支持有限，这里提供基础实现
        logger.warning("Bookmark insertion has limited support in python-docx")
        
        doc_path = Path(doc_path)
        image_path = Path(image_path)
        
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        
        try:
            doc = Document(doc_path)
            
            # 简单实现：在文档末尾添加图片
            # 实际应用中可能需要使用docx4j或其他工具处理书签
            last_paragraph = doc.paragraphs[-1]
            
            image_width = self._calculate_image_width(image_path)
            self._insert_image_to_paragraph(last_paragraph, image_path, image_width)
            
            doc.save(output_path)
            
            logger.info(f"Image inserted at bookmark (fallback to end): {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to insert image at bookmark: {e}")
            raise
    
    def create_image_paragraph(self, doc_path: str, insertion_point: InsertionPoint,
                              image_path: str, caption: Optional[str] = None,
                              output_path: Optional[str] = None) -> str:
        """
        创建独立的图片段落
        
        Args:
            doc_path: 文档路径
            insertion_point: 插入点
            image_path: 图片路径
            caption: 图片标题
            output_path: 输出路径
            
        Returns:
            str: 输出文档路径
        """
        doc_path = Path(doc_path)
        image_path = Path(image_path)
        
        if not output_path:
            output_path = self._generate_output_path(doc_path)
        
        try:
            doc = Document(doc_path)
            
            # 在指定位置后插入新段落
            target_index = insertion_point.paragraph_index + 1
            
            # 创建新段落（在目标段落后）
            new_paragraph = doc.add_paragraph()
            
            # 移动段落到正确位置
            # 注意：python-docx不直接支持段落插入，这里使用简化方法
            
            # 计算图片尺寸
            image_width = self._calculate_image_width(image_path)
            
            # 插入图片
            run = new_paragraph.add_run()
            run.add_picture(str(image_path), width=Inches(image_width))
            
            # 设置居中对齐
            new_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加标题（如果提供）
            if caption:
                caption_paragraph = doc.add_paragraph(caption)
                caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                # 设置标题样式
                caption_paragraph.style = 'Caption'
            
            doc.save(output_path)
            
            logger.info(f"Image paragraph created: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to create image paragraph: {e}")
            raise
    
    def get_insertion_preview(self, doc_path: str, insertion_point: InsertionPoint,
                             context_lines: int = 2) -> Dict[str, Any]:
        """
        获取插入预览信息
        
        Args:
            doc_path: 文档路径
            insertion_point: 插入点
            context_lines: 上下文行数
            
        Returns:
            Dict: 预览信息
        """
        try:
            doc = Document(doc_path)
            paragraphs = doc.paragraphs
            
            start_idx = max(0, insertion_point.paragraph_index - context_lines)
            end_idx = min(len(paragraphs), insertion_point.paragraph_index + context_lines + 1)
            
            preview = {
                'insertion_index': insertion_point.paragraph_index,
                'total_paragraphs': len(paragraphs),
                'context': []
            }
            
            for i in range(start_idx, end_idx):
                para_info = {
                    'index': i,
                    'text': paragraphs[i].text[:100] + ('...' if len(paragraphs[i].text) > 100 else ''),
                    'style': paragraphs[i].style.name if paragraphs[i].style else 'Normal',
                    'is_target': i == insertion_point.paragraph_index
                }
                preview['context'].append(para_info)
            
            return preview
            
        except Exception as e:
            logger.error(f"Failed to get insertion preview: {e}")
            return {}
