<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技人才数据管理系统流程</text>

  <!-- 阶段一：数据汇聚 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与展示</text>
  
  <!-- 节点1: 数据汇聚 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据汇聚</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动汇聚全市科技人才基础数据</text>
  </g>

  <!-- 节点2: 数据展示 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="90" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">刷新数量概览卡片和分布大屏</text>
    <text x="100" y="80" text-anchor="middle" font-size="12" fill="#555">支持类型切换联动</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 600 200 Q 600 225 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：交互筛选 -->
  <text x="600" y="380" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：交互筛选与详情查看</text>

  <!-- 节点3: 人才清册 -->
  <g transform="translate(300, 430)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才清册</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示符合筛选条件的人员信息</text>
  </g>

  <!-- 节点4: 详情查看 -->
  <g transform="translate(700, 430)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">科研背景、项目、成果等详细信息</text>
  </g>

  <!-- 连接线 展示 -> 清册 (曲线) -->
  <path d="M 500 340 C 450 370, 400 400, 400 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="450" y="390" text-anchor="middle" font-size="12" fill="#555">点击卡片/区块</text>

  <!-- 连接线 清册 -> 详情 (曲线) -->
  <path d="M 500 465 C 550 465, 650 465, 700 465" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="600" y="455" text-anchor="middle" font-size="12" fill="#555">点击人员</text>

  <!-- 阶段三：数据操作 -->
  <text x="600" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与管理</text>
  
  <!-- 节点5: 数据操作 -->
  <g transform="translate(400, 620)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据筛选与导出</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">精细筛查</tspan>
        <tspan dx="80">数据导出</tspan>
        <tspan dx="80">操作日志记录</tspan>
      </text>
  </g>

  <!-- 连接线 清册 -> 数据操作 (曲线) -->
  <path d="M 400 500 C 400 550, 450 580, 500 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 500 C 700 550, 650 580, 600 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>