<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技人才数据可视化分析系统</text>

  <!-- 阶段一：数据汇聚 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与展示</text>
  
  <!-- 节点1: 数据汇聚 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据自动汇聚</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时刷新全市科技人才数据</text>
  </g>

  <!-- 节点2: 可视化展示 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数量概览卡片与分布大屏</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 600 210 Q 600 230 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：交互筛选 -->
  <text x="600" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：交互筛选</text>

  <!-- 节点3: 类型切换 -->
  <g transform="translate(400, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才类型切换</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择感兴趣的人才类别</text>
  </g>

  <!-- 节点4: 全局联动 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据全局联动</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">刷新概览卡片及分布数据</text>
  </g>

  <!-- 连接线 2 -> 3/4 -->
  <path d="M 550 330 C 500 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 330 C 700 350, 750 380, 800 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看 -->
  <text x="600" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看</text>
  
  <!-- 节点5: 人才清册 -->
  <g transform="translate(300, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才清册</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示符合条件的人员信息</text>
  </g>

  <!-- 节点6: 人才详情 -->
  <g transform="translate(600, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">科研背景、项目、成果等</text>
  </g>

  <!-- 连接线 3/4 -> 5/6 -->
  <path d="M 400 490 C 350 520, 300 540, 300 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 490 C 600 520, 600 540, 600 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据操作 -->
  <text x="600" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据操作</text>
  
  <!-- 节点7: 数据操作 -->
  <g transform="translate(450, 720)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据筛选与导出</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">精细筛查或导出选定人员信息</text>
  </g>

  <!-- 连接线 5/6 -> 7 -->
  <path d="M 300 640 C 350 660, 400 680, 450 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 640 C 550 660, 500 680, 550 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
</svg>