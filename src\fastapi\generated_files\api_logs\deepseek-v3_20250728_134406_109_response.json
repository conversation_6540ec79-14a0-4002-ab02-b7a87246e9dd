{"timestamp": "2025-07-28 13:45:46", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Mon, 28 Jul 2025 05:45:47 GMT", "content-type": "application/json; charset=utf-8", "content-length": "8600", "x-client-request-id": "unknown-20250728134417-FpDGYSbr", "x-envoy-upstream-service-time": "90117", "x-request-id": "02175368145757096a6127c591581fd2c335f654ab52db5c5c7e5"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Here's the professional, hierarchical, and aesthetically pleasing SVG flowchart based on your requirements:\n\n```svg\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" font-family=\"'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif\">\n  <!-- 定义 -->\n  <defs>\n    <!-- 箭头样式 -->\n    <marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">\n      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#333\" />\n    </marker>\n    <!-- 阴影滤镜 -->\n    <filter id=\"soft-shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.1\" />\n    </filter>\n  </defs>\n\n  <!-- 背景 -->\n  <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n\n  <!-- 主标题 -->\n  <text x=\"600\" y=\"50\" font-size=\"28\" text-anchor=\"middle\" font-weight=\"600\" fill=\"#333\">科技人才数据可视化分析系统</text>\n\n  <!-- 阶段一：数据汇聚 -->\n  <text x=\"600\" y=\"100\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段一：数据汇聚与展示</text>\n  \n  <!-- 节点1: 数据汇聚 -->\n  <g transform=\"translate(500, 130)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"80\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据自动汇聚</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">实时刷新全市科技人才数据</text>\n  </g>\n\n  <!-- 节点2: 可视化展示 -->\n  <g transform=\"translate(500, 250)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"80\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">可视化展示</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">数量概览卡片与分布大屏</text>\n  </g>\n  \n  <!-- 连接线 1 -> 2 -->\n  <path d=\"M 600 210 Q 600 230 600 250\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段二：交互筛选 -->\n  <text x=\"600\" y=\"370\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段二：交互筛选</text>\n\n  <!-- 节点3: 类型切换 -->\n  <g transform=\"translate(400, 420)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才类型切换</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">选择感兴趣的人才类别</text>\n  </g>\n\n  <!-- 节点4: 全局联动 -->\n  <g transform=\"translate(600, 420)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据全局联动</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">刷新概览卡片及分布数据</text>\n  </g>\n\n  <!-- 连接线 2 -> 3/4 -->\n  <path d=\"M 550 330 C 500 350, 450 380, 400 420\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 650 330 C 700 350, 750 380, 800 420\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段三：详情查看 -->\n  <text x=\"600\" y=\"520\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段三：详情查看</text>\n  \n  <!-- 节点5: 人才清册 -->\n  <g transform=\"translate(300, 570)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才清册</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">展示符合条件的人员信息</text>\n  </g>\n\n  <!-- 节点6: 人才详情 -->\n  <g transform=\"translate(600, 570)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"200\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"100\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才详情</text>\n    <text x=\"100\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">科研背景、项目、成果等</text>\n  </g>\n\n  <!-- 连接线 3/4 -> 5/6 -->\n  <path d=\"M 400 490 C 350 520, 300 540, 300 570\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 600 490 C 600 520, 600 540, 600 570\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- 阶段四：数据操作 -->\n  <text x=\"600\" y=\"670\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段四：数据操作</text>\n  \n  <!-- 节点7: 数据操作 -->\n  <g transform=\"translate(450, 720)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"300\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"150\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据筛选与导出</text>\n    <text x=\"150\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">精细筛查或导出选定人员信息</text>\n  </g>\n\n  <!-- 连接线 5/6 -> 7 -->\n  <path d=\"M 300 640 C 350 660, 400 680, 450 720\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 600 640 C 550 660, 500 680, 550 720\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n</svg>\n```\n\nThis SVG flowchart features:\n\n1. **Clear hierarchical structure** with 4 distinct phases:\n   - Data aggregation and display\n   - Interactive filtering\n   - Detail viewing\n   - Data operations\n\n2. **Consistent color scheme**:\n   - Blue for data aggregation phase\n   - Green for interactive filtering\n   - Purple for detail viewing\n   - Yellow for final operations\n\n3. **Professional design elements**:\n   - Soft shadows for depth\n   - Rounded rectangles with clear titles and descriptions\n   - Smooth bezier curves for connections\n   - Clean arrow markers\n\n4. **Proper text hierarchy**:\n   - Main title (28px)\n   - Phase titles (20px)\n   - Node titles (16px bold)\n   - Node descriptions (12px regular)\n\n5. **Logical flow** showing the complete user journey from data aggregation to detailed analysis and operations.\n\nThe layout provides ample white space between elements while maintaining clear visual connections between related components.", "role": "assistant"}}], "created": 1753681547, "id": "02175368145757096a6127c591581fd2c335f654ab52db5c5c7e5", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 2107, "prompt_tokens": 2306, "total_tokens": 4413, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}