<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技人才总数概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        dark: {
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                            600: '#475569'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-slate-900 text-gray-100 min-h-screen">
    <!-- SLOT::header::BEGIN -->
    <header class="bg-slate-800 border-b border-slate-700 py-4 px-6">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <!-- CHUNK::header-title::BEGIN -->
            <h1 class="text-2xl font-bold">科技人才总数概览</h1>
            <!-- CHUNK::header-title::END -->
            
            <!-- CHUNK::header-actions::BEGIN -->
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    刷新数据
                </button>
                <button class="px-4 py-2 bg-slate-700 border border-slate-600 rounded-md hover:bg-slate-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出数据
                </button>
            </div>
            <!-- CHUNK::header-actions::END -->
        </div>
    </header>
    <!-- SLOT::header::END -->

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- SLOT::filters::BEGIN -->
        <div class="bg-slate-800 rounded-lg shadow-md p-6 mb-8 border border-slate-700">
            <h2 class="text-lg font-semibold mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- CHUNK::filter-type::BEGIN -->
                <div>
                    <label class="block text-sm font-medium mb-1">人才类型</label>
                    <select class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>全部类型</option>
                        <option>研发人员</option>
                        <option>科研管理人员</option>
                        <option>专家</option>
                        <option>科技特派员</option>
                    </select>
                </div>
                <!-- CHUNK::filter-type::END -->
                
                <!-- CHUNK::filter-region::BEGIN -->
                <div>
                    <label class="block text-sm font-medium mb-1">所在区域</label>
                    <select class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>全市</option>
                        <option>海曙区</option>
                        <option>江北区</option>
                        <option>鄞州区</option>
                        <option>镇海区</option>
                        <option>北仑区</option>
                    </select>
                </div>
                <!-- CHUNK::filter-region::END -->
                
                <!-- CHUNK::filter-industry::BEGIN -->
                <div>
                    <label class="block text-sm font-medium mb-1">行业领域</label>
                    <select class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>全部行业</option>
                        <option>电子信息</option>
                        <option>智能制造</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>新能源</option>
                    </select>
                </div>
                <!-- CHUNK::filter-industry::END -->
                
                <!-- CHUNK::filter-level::BEGIN -->
                <div>
                    <label class="block text-sm font-medium mb-1">人才层次</label>
                    <select class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>全部层次</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                        <option>区级</option>
                    </select>
                </div>
                <!-- CHUNK::filter-level::END -->
            </div>
        </div>
        <!-- SLOT::filters::END -->

        <!-- SLOT::kpi-cards::BEGIN -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- CHUNK::card-rd::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer" onclick="showTalentList('研发人员')">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-medium text-gray-300 mb-2">研发人员</h3>
                        <div class="text-3xl font-bold">12,847</div>
                    </div>
                    <div class="bg-blue-500/20 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较上月增长 5.2%
                </div>
            </div>
            <!-- CHUNK::card-rd::END -->
            
            <!-- CHUNK::card-manager::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer" onclick="showTalentList('科研管理人员')">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-medium text-gray-300 mb-2">科研管理人员</h3>
                        <div class="text-3xl font-bold">3,245</div>
                    </div>
                    <div class="bg-purple-500/20 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较上月增长 2.8%
                </div>
            </div>
            <!-- CHUNK::card-manager::END -->
            
            <!-- CHUNK::card-expert::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer" onclick="showTalentList('专家')">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-medium text-gray-300 mb-2">专家</h3>
                        <div class="text-3xl font-bold">1,876</div>
                    </div>
                    <div class="bg-yellow-500/20 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较上月增长 1.5%
                </div>
            </div>
            <!-- CHUNK::card-expert::END -->
            
            <!-- CHUNK::card-specialist::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors cursor-pointer" onclick="showTalentList('科技特派员')">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-medium text-gray-300 mb-2">科技特派员</h3>
                        <div class="text-3xl font-bold">523</div>
                    </div>
                    <div class="bg-green-500/20 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较上月增长 3.7%
                </div>
            </div>
            <!-- CHUNK::card-specialist::END -->
        </div>
        <!-- SLOT::kpi-cards::END -->

        <!-- SLOT::charts::BEGIN -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- CHUNK::chart-region::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">各区人才分布</h2>
                    <select class="bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm">
                        <option>按数量</option>
                        <option>按密度</option>
                    </select>
                </div>
                <div class="h-80">
                    <canvas id="regionChart"></canvas>
                </div>
            </div>
            <!-- CHUNK::chart-region::END -->
            
            <!-- CHUNK::chart-industry::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">行业人才分布</h2>
                    <select class="bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm">
                        <option>全部类型</option>
                        <option>研发人员</option>
                        <option>科研管理人员</option>
                    </select>
                </div>
                <div class="h-80">
                    <canvas id="industryChart"></canvas>
                </div>
            </div>
            <!-- CHUNK::chart-industry::END -->
            
            <!-- CHUNK::chart-age::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">人才年龄结构</h2>
                    <select class="bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm">
                        <option>全部类型</option>
                        <option>研发人员</option>
                        <option>专家</option>
                    </select>
                </div>
                <div class="h-80">
                    <canvas id="ageChart"></canvas>
                </div>
            </div>
            <!-- CHUNK::chart-age::END -->
            
            <!-- CHUNK::chart-level::BEGIN -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">人才层次分布</h2>
                    <select class="bg-slate-700 border border-slate-600 rounded-md px-3 py-1 text-sm">
                        <option>全部类型</option>
                        <option>研发人员</option>
                        <option>专家</option>
                    </select>
                </div>
                <div class="h-80">
                    <canvas id="levelChart"></canvas>
                </div>
            </div>
            <!-- CHUNK::chart-level::END -->
        </div>
        <!-- SLOT::charts::END -->

        <!-- SLOT::talent-list::BEGIN -->
        <div id="talentList" class="hidden bg-slate-800 rounded-lg shadow-md p-6 mb-8 border border-slate-700">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold">人才列表</h2>
                <div class="flex space-x-3">
                    <input type="text" placeholder="搜索人才姓名" class="bg-slate-700 border border-slate-600 rounded-md px-3 py-1">
                    <button class="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700">导出当前列表</button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-slate-700">
                    <thead class="bg-slate-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">所属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">行业领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">人才层次</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">年龄</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">参与项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-slate-700">
                        <tr class="hover:bg-slate-700/50">
                            <td class="px-6 py-4 whitespace-nowrap">张三</td>
                            <td class="px-6 py-4 whitespace-nowrap">研发人员</td>
                            <td class="px-6 py-4 whitespace-nowrap">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap">电子信息</td>
                            <td class="px-6 py-4 whitespace-nowrap">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap">35</td>
                            <td class="px-6 py-4 whitespace-nowrap">5</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <button onclick="showTalentDetail('1')" class="text-blue-400 hover:text-blue-300">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-slate-700/50">
                            <td class="px-6 py-4 whitespace-nowrap">李四</td>
                            <td class="px-6 py-4 whitespace-nowrap">科研管理人员</td>
                            <td class="px-6 py-4 whitespace-nowrap">宁波市XX研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap">42</td>
                            <td class="px-6 py-4 whitespace-nowrap">8</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <button onclick="showTalentDetail('2')" class="text-blue-400 hover:text-blue-300">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-slate-700/50">
                            <td class="px-6 py-4 whitespace-nowrap">王五</td>
                            <td class="px-6 py-4 whitespace-nowrap">专家</td>
                            <td class="px-6 py-4 whitespace-nowrap">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap">新材料</td>
                            <td class="px-6 py-4 whitespace-nowrap">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap">50</td>
                            <td class="px-6 py-4 whitespace-nowrap">12</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <button onclick="showTalentDetail('3')" class="text-blue-400 hover:text-blue-300">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-slate-700">
                <div class="flex items-center justify-between">
                    <div class="text-sm">
                        显示第 1-3 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700">2</button>
                        <button class="px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700">3</button>
                        <button class="px-3 py-1 border border-slate-600 rounded-md hover:bg-slate-700">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::talent-list::END -->
    </div>

    <!-- SLOT::talent-detail-modal::BEGIN -->
    <div id="talentDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-slate-700">
                <div class="px-6 py-4 border-b border-slate-700 flex justify-between items-center sticky top-0 bg-slate-800 z-10">
                    <h3 class="text-xl font-semibold">人才详情</h3>
                    <button onclick="hideTalentDetail()" class="text-gray-400 hover:text-gray-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="lg:col-span-2">
                            <!-- CHUNK::detail-basic-info::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6 mb-6">
                                <h4 class="text-lg font-semibold mb-4">基本信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <span class="text-sm text-gray-400">姓名：</span>
                                        <span class="text-sm font-medium">张三</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">性别：</span>
                                        <span class="text-sm font-medium">男</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">出生日期：</span>
                                        <span class="text-sm font-medium">1988-05-15</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">学历：</span>
                                        <span class="text-sm font-medium">博士</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">职称：</span>
                                        <span class="text-sm font-medium">高级工程师</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">人才类型：</span>
                                        <span class="text-sm font-medium">研发人员</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">人才层次：</span>
                                        <span class="text-sm font-medium">省级</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">所属单位：</span>
                                        <span class="text-sm font-medium">宁波市XX科技有限公司</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">行业领域：</span>
                                        <span class="text-sm font-medium">电子信息</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">联系电话：</span>
                                        <span class="text-sm font-medium">138****1234</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">电子邮箱：</span>
                                        <span class="text-sm font-medium"><EMAIL></span>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-basic-info::END -->
                            
                            <!-- CHUNK::detail-projects::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6 mb-6">
                                <h4 class="text-lg font-semibold mb-4">参与项目</h4>
                                <div class="space-y-4">
                                    <div class="border-b border-slate-600 pb-4">
                                        <h5 class="font-medium">基于人工智能的智能制造系统研发</h5>
                                        <p class="text-sm text-gray-400 mt-1">项目编号：NB2023001</p>
                                        <p class="text-sm text-gray-400">起止时间：2023-01-01 至 2025-12-31</p>
                                        <p class="text-sm text-gray-400 mt-2">项目角色：技术负责人</p>
                                    </div>
                                    <div class="border-b border-slate-600 pb-4">
                                        <h5 class="font-medium">5G通信关键技术研究与应用</h5>
                                        <p class="text-sm text-gray-400 mt-1">项目编号：NB2022005</p>
                                        <p class="text-sm text-gray-400">起止时间：2022-06-01 至 2024-05-31</p>
                                        <p class="text-sm text-gray-400 mt-2">项目角色：核心研发人员</p>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-projects::END -->
                            
                            <!-- CHUNK::detail-achievements::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6">
                                <h4 class="text-lg font-semibold mb-4">科研成果</h4>
                                <div class="space-y-4">
                                    <div>
                                        <h5 class="font-medium">发明专利：一种智能制造系统及其控制方法</h5>
                                        <p class="text-sm text-gray-400 mt-1">专利号：ZL202310123456.7</p>
                                        <p class="text-sm text-gray-400">授权日期：2023-05-20</p>
                                    </div>
                                    <div>
                                        <h5 class="font-medium">论文：人工智能在智能制造中的应用研究</h5>
                                        <p class="text-sm text-gray-400 mt-1">发表期刊：计算机应用研究</p>
                                        <p class="text-sm text-gray-400">发表时间：2022-08-15</p>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-achievements::END -->
                        </div>
                        
                        <div>
                            <!-- CHUNK::detail-photo::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6 mb-6">
                                <div class="flex justify-center">
                                    <img src="https://source.unsplash.com/200x200/?portrait,man" alt="人才照片" class="w-32 h-32 rounded-full object-cover">
                                </div>
                                <div class="mt-4 text-center">
                                    <h4 class="text-lg font-semibold">张三</h4>
                                    <p class="text-sm text-gray-400">高级工程师</p>
                                    <p class="text-sm text-gray-400 mt-1">宁波市XX科技有限公司</p>
                                </div>
                            </div>
                            <!-- CHUNK::detail-photo::END -->
                            
                            <!-- CHUNK::detail-awards::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6 mb-6">
                                <h4 class="text-lg font-semibold mb-4">获奖情况</h4>
                                <div class="space-y-3">
                                    <div>
                                        <h5 class="font-medium">宁波市科技进步一等奖</h5>
                                        <p class="text-sm text-gray-400 mt-1">2022年度</p>
                                    </div>
                                    <div>
                                        <h5 class="font-medium">浙江省优秀科技工作者</h5>
                                        <p class="text-sm text-gray-400 mt-1">2021年度</p>
                                    </div>
                                </div>
                            </div>
                            <!-- CHUNK::detail-awards::END -->
                            
                            <!-- CHUNK::detail-skills::BEGIN -->
                            <div class="bg-slate-700/50 rounded-lg p-6">
                                <h4 class="text-lg font-semibold mb-4">专业技能</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">人工智能</span>
                                    <span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">机器学习</span>
                                    <span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">智能制造</span>
                                    <span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">5G通信</span>
                                    <span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">工业互联网</span>
                                </div>
                            </div>
                            <!-- CHUNK::detail-skills::END -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::talent-detail-modal::END -->

    <script>
        // 1. 全局UI控制函数
        function showTalentList(type) {
            document.getElementById('talentList').classList.remove('hidden');
            document.getElementById('talentList').scrollIntoView({ behavior: 'smooth' });
        }

        function showTalentDetail(id) {
            document.getElementById('talentDetailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function hideTalentDetail() {
            document.getElementById('talentDetailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 2. DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有图表
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'bar',
                data: {
                    labels: ['海曙区', '江北区', '鄞州区', '镇海区', '北仑区'],
                    datasets: [{
                        label: '人才数量',
                        data: [3245, 2876, 4987, 1654, 2085],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            const industryCtx = document.getElementById('industryChart').getContext('2d');
            new Chart(industryCtx, {
                type: 'pie',
                data: {
                    labels: ['电子信息', '智能制造', '生物医药', '新材料', '新能源'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'right' }
                    }
                }
            });

            const ageCtx = document.getElementById('ageChart').getContext('2d');
            new Chart(ageCtx, {
                type: 'line',
                data: {
                    labels: ['25岁以下', '26-35岁', '36-45岁', '46-55岁', '56岁以上'],
                    datasets: [{
                        label: '人才数量',
                        data: [1560, 5240, 3870, 1560, 617],
                        borderColor: 'rgba(59, 130, 246, 0.8)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            const levelCtx = document.getElementById('levelChart').getContext('2d');
            new Chart(levelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['国家级', '省级', '市级', '区级'],
                    datasets: [{
                        data: [1560, 3240, 5870, 2210],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'right' }
                    }
                }
            });

            // 绑定弹窗外部点击关闭事件
            document.getElementById('talentDetailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideTalentDetail();
                }
            });

            // 绑定ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('talentDetailModal').classList.contains('hidden')) {
                    hideTalentDetail();
                }
            });
        });
    </script>
</body>
</html>