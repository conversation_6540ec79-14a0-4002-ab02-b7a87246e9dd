{"timestamp": "2025-07-29 17:11:32", "status_code": 200, "headers": {"server": "istio-envoy", "date": "<PERSON><PERSON>, 29 Jul 2025 09:11:35 GMT", "content-type": "application/json; charset=utf-8", "content-length": "74086", "x-client-request-id": "unknown-20250729170143-sofWACTE", "x-envoy-upstream-service-time": "592975", "x-request-id": "02175377970269181cc4b220e2c57abddebb2bf8b3960404877f8"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>科技项目管理</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#3b82f6',\n                        secondary: '#64748b'\n                    }\n                }\n            }\n        }\n    </script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <div class=\"container mx-auto px-4 py-6\">\n        <!-- SLOT::header::BEGIN -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-3xl font-bold text-gray-900 mb-2\">科技项目管理</h1>\n            <p class=\"text-gray-600\">全生命周期标准化管理平台，支持国家级、省级、市级、区级科技项目管理</p>\n        </div>\n        <!-- SLOT::header::END -->\n\n        <!-- SLOT::filters::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n                </svg>\n                筛选条件\n            </h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                <!-- CHUNK::filters-project-name::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目名称</label>\n                    <input type=\"text\" placeholder=\"请输入项目名称\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                </div>\n                <!-- CHUNK::filters-project-name::END -->\n\n                <!-- CHUNK::filters-org-name::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">承担单位</label>\n                    <input type=\"text\" placeholder=\"请输入承担单位\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                </div>\n                <!-- CHUNK::filters-org-name::END -->\n\n                <!-- CHUNK::filters-credit-code::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">统一社会信用代码</label>\n                    <input type=\"text\" placeholder=\"请输入信用代码\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                </div>\n                <!-- CHUNK::filters-credit-code::END -->\n\n                <!-- CHUNK::filters-project-level::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目级别</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        <option value=\"\">全部级别</option>\n                        <option value=\"national\">国家级</option>\n                        <option value=\"provincial\">省级</option>\n                        <option value=\"municipal\">市级</option>\n                        <option value=\"district\">区级</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filters-project-level::END -->\n\n                <!-- CHUNK::filters-project-status::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目状态</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        <option value=\"\">全部状态</option>\n                        <option value=\"declaration\">申报中</option>\n                        <option value=\"approval\">立项中</option>\n                        <option value=\"implementation\">实施中</option>\n                        <option value=\"acceptance\">验收中</option>\n                        <option value=\"completed\">已完成</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filters-project-status::END -->\n\n                <!-- CHUNK::filters-year-range::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">申报年度</label>\n                    <div class=\"flex space-x-2\">\n                        <select class=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            <option value=\"\">起始年份</option>\n                            <option>2024</option>\n                            <option>2023</option>\n                            <option>2022</option>\n                        </select>\n                        <span class=\"flex items-center\">至</span>\n                        <select class=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            <option value=\"\">结束年份</option>\n                            <option>2024</option>\n                            <option>2023</option>\n                            <option>2022</option>\n                        </select>\n                    </div>\n                </div>\n                <!-- CHUNK::filters-year-range::END -->\n            </div>\n            <div class=\"flex justify-end space-x-3 mt-4\">\n                <!-- CHUNK::filters-reset-button::BEGIN -->\n                <button class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                    重置\n                </button>\n                <!-- CHUNK::filters-reset-button::END -->\n\n                <!-- CHUNK::filters-search-button::BEGIN -->\n                <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                    查询\n                </button>\n                <!-- CHUNK::filters-search-button::END -->\n            </div>\n        </div>\n        <!-- SLOT::filters::END -->\n\n        <!-- SLOT::actions::BEGIN -->\n        <div class=\"flex justify-between items-center mb-4\">\n            <!-- CHUNK::actions-buttons::BEGIN -->\n            <div class=\"flex space-x-3\">\n                <button onclick=\"openModal('importModal')\" class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\">\n                    <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                    </svg>\n                    批量导入\n                </button>\n                <button onclick=\"openModal('exportModal')\" class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\">\n                    <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                    </svg>\n                    批量导出\n                </button>\n            </div>\n            <!-- CHUNK::actions-buttons::END -->\n\n            <!-- CHUNK::actions-add-button::BEGIN -->\n            <button onclick=\"openModal('addProjectModal')\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center\">\n                <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\"></path>\n                </svg>\n                新增项目\n            </button>\n            <!-- CHUNK::actions-add-button::END -->\n        </div>\n        <!-- SLOT::actions::END -->\n\n        <!-- SLOT::projects-table::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md overflow-hidden\">\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                            </th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目名称</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目编号</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">承担单位</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">负责人</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目级别</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">起止时间</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目状态</th>\n                            <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <!-- CHUNK::projects-table-row-1::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">宁波市智能制造关键技术研发</div>\n                                <div class=\"text-sm text-gray-500\">高新技术领域</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">NB2024KJ001</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX科技有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">张研究员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">市级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-01-01 至 2025-12-31</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-blue-100 text-blue-800\">实施中</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('projectDetailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editProjectModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button onclick=\"deleteProject()\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::projects-table-row-1::END -->\n\n                        <!-- CHUNK::projects-table-row-2::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">浙江省新材料应用研究</div>\n                                <div class=\"text-sm text-gray-500\">新材料领域</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">ZJ2023KJ045</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市YY材料研究院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">王教授</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">省级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-06-01 至 2025-05-31</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-green-100 text-green-800\">已完成</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('projectDetailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editProjectModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button onclick=\"deleteProject()\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::projects-table-row-2::END -->\n\n                        <!-- CHUNK::projects-table-row-3::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">国家重大科技专项</div>\n                                <div class=\"text-sm text-gray-500\">高端装备</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">GJ2024ZD001</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市ZZ装备有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">李工程师</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">国家级</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-03-01 至 2026-02-28</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-yellow-100 text-yellow-800\">立项中</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('projectDetailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editProjectModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button onclick=\"deleteProject()\" class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::projects-table-row-3::END -->\n                    </tbody>\n                </table>\n            </div>\n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-700\">\n                        显示 <span class=\"font-medium\">1</span> 到 <span class=\"font-medium\">3</span> 条，共 <span class=\"font-medium\">42</span> 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700\">1</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::projects-table::END -->\n\n        <!-- SLOT::stats-card::BEGIN -->\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mt-8\">\n            <!-- CHUNK::stats-total-projects::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 bg-blue-100 p-3 rounded-lg\">\n                        <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">项目总数</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">156</p>\n                    </div>\n                </div>\n            </div>\n            <!-- CHUNK::stats-total-projects::END -->\n\n            <!-- CHUNK::stats-national-projects::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 bg-green-100 p-3 rounded-lg\">\n                        <svg class=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">国家级项目</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">24</p>\n                    </div>\n                </div>\n            </div>\n            <!-- CHUNK::stats-national-projects::END -->\n\n            <!-- CHUNK::stats-implementing-projects::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 bg-yellow-100 p-3 rounded-lg\">\n                        <svg class=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">实施中项目</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">78</p>\n                    </div>\n                </div>\n            </div>\n            <!-- CHUNK::stats-implementing-projects::END -->\n\n            <!-- CHUNK::stats-completed-projects::BEGIN -->\n            <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 bg-red-100 p-3 rounded-lg\">\n                        <svg class=\"w-6 h-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <p class=\"text-sm font-medium text-gray-500\">已完成项目</p>\n                        <p class=\"text-2xl font-semibold text-gray-900\">54</p>\n                    </div>\n                </div>\n            </div>\n            <!-- CHUNK::stats-completed-projects::END -->\n        </div>\n        <!-- SLOT::stats-card::END -->\n    </div>\n\n    <!-- SLOT::add-project-modal::BEGIN -->\n    <div id=\"addProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">新增科技项目</h3>\n                    <button onclick=\"closeModal('addProjectModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <form class=\"space-y-4\">\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <!-- CHUNK::add-project-name::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目名称 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::add-project-name::END -->\n\n                        <!-- CHUNK::add-project-code::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目编号</label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::add-project-code::END -->\n\n                        <!-- CHUNK::add-project-org::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">承担单位 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::add-project-org::END -->\n\n                        <!-- CHUNK::add-project-leader::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目负责人 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::add-project-leader::END -->\n\n                        <!-- CHUNK::add-project-level::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目级别 <span class=\"text-red-500\">*</span></label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                                <option value=\"\">请选择项目级别</option>\n                                <option value=\"national\">国家级</option>\n                                <option value=\"provincial\">省级</option>\n                                <option value=\"municipal\">市级</option>\n                                <option value=\"district\">区级</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::add-project-level::END -->\n\n                        <!-- CHUNK::add-project-category::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目类别</label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                <option value=\"\">请选择项目类别</option>\n                                <option value=\"high-tech\">高新技术</option>\n                                <option value=\"new-material\">新材料</option>\n                                <option value=\"biomedicine\">生物医药</option>\n                                <option value=\"energy\">新能源</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::add-project-category::END -->\n\n                        <!-- CHUNK::add-project-start-date::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">开始日期 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::add-project-start-date::END -->\n\n                        <!-- CHUNK::add-project-end-date::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">结束日期 <span class=\"text-red-500\">*</span></label>\n                            <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                        </div>\n                        <!-- CHUNK::add-project-end-date::END -->\n\n                        <!-- CHUNK::add-project-budget::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目经费(万元)</label>\n                            <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                        </div>\n                        <!-- CHUNK::add-project-budget::END -->\n\n                        <!-- CHUNK::add-project-status::BEGIN -->\n                        <div>\n                            <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目状态 <span class=\"text-red-500\">*</span></label>\n                            <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" required>\n                                <option value=\"\">请选择项目状态</option>\n                                <option value=\"declaration\">申报中</option>\n                                <option value=\"approval\">立项中</option>\n                                <option value=\"implementation\">实施中</option>\n                                <option value=\"acceptance\">验收中</option>\n                                <option value=\"completed\">已完成</option>\n                            </select>\n                        </div>\n                        <!-- CHUNK::add-project-status::END -->\n                    </div>\n\n                    <!-- CHUNK::add-project-description::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目简介</label>\n                        <textarea rows=\"3\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"></textarea>\n                    </div>\n                    <!-- CHUNK::add-project-description::END -->\n\n                    <!-- CHUNK::add-project-attachments::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">附件上传</label>\n                        <div class=\"border-2 border-dashed border-gray-300 rounded-md p-6 text-center\">\n                            <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                            </svg>\n                            <div class=\"mt-2\">\n                                <label class=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none\">\n                                    <span>点击上传文件</span>\n                                    <input type=\"file\" class=\"sr-only\">\n                                </label>\n                                <p class=\"text-xs text-gray-500\">支持PDF、Word、Excel格式</p>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::add-project-attachments::END -->\n\n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <!-- CHUNK::add-project-cancel-button::BEGIN -->\n                        <button type=\"button\" onclick=\"closeModal('addProjectModal')\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            取消\n                        </button>\n                        <!-- CHUNK::add-project-cancel-button::END -->\n\n                        <!-- CHUNK::add-project-submit-button::BEGIN -->\n                        <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            保存\n                        </button>\n                        <!-- CHUNK::add-project-submit-button::END -->\n                    </div>\n                </form>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::add-project-modal::END -->\n\n    <!-- SLOT::project-detail-modal::BEGIN -->\n    <div id=\"projectDetailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">项目详情</h3>\n                    <button onclick=\"closeModal('projectDetailModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"space-y-6\">\n                    <!-- CHUNK::detail-basic-info::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">基本信息</h4>\n                        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                            <div>\n                                <span class=\"text-gray-500\">项目名称：</span>\n                                <span class=\"font-medium text-gray-900\">宁波市智能制造关键技术研发</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目编号：</span>\n                                <span class=\"font-medium text-gray-900\">NB2024KJ001</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">承担单位：</span>\n                                <span class=\"font-medium text-gray-900\">宁波市XX科技有限公司</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">统一社会信用代码：</span>\n                                <span class=\"font-medium text-gray-900\">91330201MA2XXXXXX</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目负责人：</span>\n                                <span class=\"font-medium text-gray-900\">张研究员</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">联系电话：</span>\n                                <span class=\"font-medium text-gray-900\">13888888888</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目级别：</span>\n                                <span class=\"font-medium text-gray-900\">市级</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目类别：</span>\n                                <span class=\"font-medium text-gray-900\">高新技术</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">开始日期：</span>\n                                <span class=\"font-medium text-gray-900\">2024-01-01</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">结束日期：</span>\n                                <span class=\"font-medium text-gray-900\">2025-12-31</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目经费：</span>\n                                <span class=\"font-medium text-gray-900\">500万元</span>\n                            </div>\n                            <div>\n                                <span class=\"text-gray-500\">项目状态：</span>\n                                <span class=\"font-medium text-gray-900\">实施中</span>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-basic-info::END -->\n\n                    <!-- CHUNK::detail-team-info::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">项目团队</h4>\n                        <div class=\"overflow-x-auto\">\n                            <table class=\"min-w-full divide-y divide-gray-200\">\n                                <thead class=\"bg-gray-100\">\n                                    <tr>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">姓名</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">单位</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">职称</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分工</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">联系电话</th>\n                                    </tr>\n                                </thead>\n                                <tbody class=\"bg-white divide-y divide-gray-200\">\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">张研究员</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX科技有限公司</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">高级工程师</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">项目负责人</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">13888888888</td>\n                                    </tr>\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">王教授</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波大学</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">教授</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">技术指导</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">13999999999</td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-team-info::END -->\n\n                    <!-- CHUNK::detail-funding-info::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">经费拨付情况</h4>\n                        <div class=\"overflow-x-auto\">\n                            <table class=\"min-w-full divide-y divide-gray-200\">\n                                <thead class=\"bg-gray-100\">\n                                    <tr>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">拨付批次</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">拨付金额(万元)</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">拨付日期</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">拨付状态</th>\n                                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">备注</th>\n                                    </tr>\n                                </thead>\n                                <tbody class=\"bg-white divide-y divide-gray-200\">\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">第一批</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">200</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-02-15</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">已拨付</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">启动资金</td>\n                                    </tr>\n                                    <tr>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">第二批</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">150</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2024-06-30</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">待拨付</td>\n                                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">中期检查后拨付</td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-funding-info::END -->\n\n                    <!-- CHUNK::detail-attachments::BEGIN -->\n                    <div class=\"bg-gray-50 rounded-lg p-6\">\n                        <h4 class=\"text-md font-semibold text-gray-900 mb-4\">项目附件</h4>\n                        <div class=\"space-y-2\">\n                            <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                <div class=\"flex items-center\">\n                                    <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                                    </svg>\n                                    <span class=\"text-sm font-medium text-gray-900\">项目申报书.pdf</span>\n                                </div>\n                                <button class=\"text-blue-600 hover:text-blue-900 text-sm\">下载</button>\n                            </div>\n                            <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                <div class=\"flex items-center\">\n                                    <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                                    </svg>\n                                    <span class=\"text-sm font-medium text-gray-900\">项目合同.docx</span>\n                                </div>\n                                <button class=\"text-blue-600 hover:text-blue-900 text-sm\">下载</button>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::detail-attachments::END -->\n\n                    <!-- CHUNK::detail-actions::BEGIN -->\n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <button onclick=\"closeModal('projectDetailModal')\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            关闭\n                        </button>\n                        <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            导出报告\n                        </button>\n                    </div>\n                    <!-- CHUNK::detail-actions::END -->\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::project-detail-modal::END -->\n\n    <!-- SLOT::import-modal::BEGIN -->\n    <div id=\"importModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">批量导入科技项目</h3>\n                    <button onclick=\"closeModal('importModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"space-y-4\">\n                    <!-- CHUNK::import-download-template::BEGIN -->\n                    <div class=\"flex items-center justify-between\">\n                        <a href=\"#\" class=\"text-blue-600 hover:text-blue-800 text-sm flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                            </svg>\n                            下载导入模板\n                        </a>\n                        <span class=\"text-xs text-gray-500\">支持.xlsx格式</span>\n                    </div>\n                    <!-- CHUNK::import-download-template::END -->\n\n                    <!-- CHUNK::import-upload-area::BEGIN -->\n                    <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                        </svg>\n                        <div class=\"mt-4\">\n                            <label for=\"file-upload\" class=\"cursor-pointer\">\n                                <span class=\"mt-2 block text-sm font-medium text-gray-900\">点击上传文件</span>\n                                <span class=\"mt-1 block text-xs text-gray-500\">支持 .xlsx, .xls 格式</span>\n                            </label>\n                            <input id=\"file-upload\" name=\"file-upload\" type=\"file\" class=\"sr-only\" accept=\".xlsx,.xls\">\n                        </div>\n                    </div>\n                    <!-- CHUNK::import-upload-area::END -->\n\n                    <!-- CHUNK::import-progress::BEGIN -->\n                    <div class=\"hidden\" id=\"importProgress\">\n                        <div class=\"flex items-center justify-between mb-1\">\n                            <span class=\"text-sm font-medium text-gray-700\">上传进度</span>\n                            <span class=\"text-sm font-medium text-gray-700\">75%</span>\n                        </div>\n                        <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n                            <div class=\"bg-blue-600 h-2.5 rounded-full\" style=\"width: 75%\"></div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::import-progress::END -->\n\n                    <!-- CHUNK::import-validation-result::BEGIN -->\n                    <div class=\"hidden bg-yellow-50 p-4 rounded-lg\" id=\"importValidationResult\">\n                        <div class=\"flex\">\n                            <div class=\"flex-shrink-0\">\n                                <svg class=\"h-5 w-5 text-yellow-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"></path>\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <p class=\"text-sm font-medium text-yellow-800\">发现2条数据存在问题</p>\n                                <ul class=\"mt-2 text-xs text-yellow-700 list-disc list-inside\">\n                                    <li>第3行：项目编号已存在</li>\n                                    <li>第5行：承担单位为必填项</li>\n                                </ul>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::import-validation-result::END -->\n\n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <!-- CHUNK::import-cancel-button::BEGIN -->\n                        <button type=\"button\" onclick=\"closeModal('importModal')\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            取消\n                        </button>\n                        <!-- CHUNK::import-cancel-button::END -->\n\n                        <!-- CHUNK::import-submit-button::BEGIN -->\n                        <button type=\"button\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            开始导入\n                        </button>\n                        <!-- CHUNK::import-submit-button::END -->\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::import-modal::END -->\n\n    <!-- SLOT::export-modal::BEGIN -->\n    <div id=\"exportModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden\">\n        <div class=\"relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white\">\n            <div class=\"mt-3\">\n                <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900\">批量导出科技项目</h3>\n                    <button onclick=\"closeModal('exportModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                <div class=\"space-y-4\">\n                    <!-- CHUNK::export-format::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">导出格式</label>\n                        <div class=\"flex space-x-4\">\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportFormat\" value=\"excel\" class=\"text-blue-600 focus:ring-blue-500\" checked>\n                                <span class=\"ml-2 text-sm text-gray-700\">Excel (.xlsx)</span>\n                            </label>\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportFormat\" value=\"csv\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">CSV (.csv)</span>\n                            </label>\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportFormat\" value=\"pdf\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">PDF (.pdf)</span>\n                            </label>\n                        </div>\n                    </div>\n                    <!-- CHUNK::export-format::END -->\n\n                    <!-- CHUNK::export-content::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">导出内容</label>\n                        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                            <div>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" checked>\n                                    <span class=\"ml-2 text-sm text-gray-700\">基础信息</span>\n                                </label>\n                            </div>\n                            <div>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" checked>\n                                    <span class=\"ml-2 text-sm text-gray-700\">项目团队</span>\n                                </label>\n                            </div>\n                            <div>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                                    <span class=\"ml-2 text-sm text-gray-700\">经费信息</span>\n                                </label>\n                            </div>\n                            <div>\n                                <label class=\"inline-flex items-center\">\n                                    <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\">\n                                    <span class=\"ml-2 text-sm text-gray-700\">附件列表</span>\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::export-content::END -->\n\n                    <!-- CHUNK::export-range::BEGIN -->\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">导出范围</label>\n                        <div class=\"flex space-x-4\">\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportRange\" value=\"current\" class=\"text-blue-600 focus:ring-blue-500\" checked>\n                                <span class=\"ml-2 text-sm text-gray-700\">当前筛选结果</span>\n                            </label>\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportRange\" value=\"all\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">全部项目</span>\n                            </label>\n                            <label class=\"inline-flex items-center\">\n                                <input type=\"radio\" name=\"exportRange\" value=\"selected\" class=\"text-blue-600 focus:ring-blue-500\">\n                                <span class=\"ml-2 text-sm text-gray-700\">已选项目</span>\n                            </label>\n                        </div>\n                    </div>\n                    <!-- CHUNK::export-range::END -->\n\n                    <div class=\"flex justify-end space-x-3 pt-4\">\n                        <!-- CHUNK::export-cancel-button::BEGIN -->\n                        <button type=\"button\" onclick=\"closeModal('exportModal')\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            取消\n                        </button>\n                        <!-- CHUNK::export-cancel-button::END -->\n\n                        <!-- CHUNK::export-submit-button::BEGIN -->\n                        <button type=\"button\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            开始导出\n                        </button>\n                        <!-- CHUNK::export-submit-button::END -->\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::export-modal::END -->\n\n    <script>\n        // --- 1. 全局UI控制函数 ---\n        function openModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden';\n            }\n        }\n\n        function closeModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto';\n            }\n        }\n\n        function deleteProject() {\n            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {\n                alert('项目已删除 (原型演示)');\n            }\n        }\n\n        // --- 2. DOM加载完毕后执行的初始化代码 ---\n        document.addEventListener('DOMContentLoaded', function() {\n            // 文件上传处理\n            document.getElementById('file-upload').addEventListener('change', function(e) {\n                if (e.target.files.length > 0) {\n                    document.getElementById('importProgress').classList.remove('hidden');\n                    // 模拟上传进度\n                    let progress = 0;\n                    const interval = setInterval(() => {\n                        progress += 10;\n                        document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';\n                        document.querySelector('#importProgress span:last-child').textContent = progress + '%';\n                        if (progress >= 100) {\n                            clearInterval(interval);\n                            setTimeout(() => {\n                                document.getElementById('importValidationResult').classList.remove('hidden');\n                            }, 500);\n                        }\n                    }, 200);\n                }\n            });\n\n            // 点击模态框外部关闭\n            document.querySelectorAll('.modal-overlay').forEach(modal => {\n                modal.addEventListener('click', function(event) {\n                    if (event.target === modal) {\n                        closeModal(modal.id);\n                    }\n                });\n\n                // ESC键关闭\n                document.addEventListener('keydown', function (e) {\n                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {\n                        closeModal(modal.id);\n                    }\n                });\n            });\n            \n            // 表单提交处理（原型演示）\n            document.querySelectorAll('form').forEach(form => {\n                form.addEventListener('submit', function(e) {\n                    e.preventDefault();\n                    alert('表单已提交 (原型演示)');\n                    const parentModalId = form.closest('[id]').id;\n                    if (parentModalId) closeModal(parentModalId);\n                });\n            });\n        });\n    </script>\n</body>\n</html>\n<!-- END -->", "role": "assistant"}}], "created": 1753780296, "id": "02175377970269181cc4b220e2c57abddebb2bf8b3960404877f8", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 13874, "prompt_tokens": 39159, "total_tokens": 53033, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}