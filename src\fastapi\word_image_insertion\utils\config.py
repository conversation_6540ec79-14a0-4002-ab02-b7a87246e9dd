"""
配置管理模块

管理项目的各种配置参数，支持从文件、环境变量等加载配置。
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class ImageConfig:
    """图片生成配置"""
    default_dpi: int = 300
    max_width_inches: float = 6.5
    device_scale_factor: int = 2
    default_format: str = 'png'
    quality: int = 95
    timeout: int = 30000


@dataclass
class DocumentConfig:
    """文档处理配置"""
    cache_enabled: bool = True
    max_cache_size: int = 100
    default_heading_pattern: str = 'Heading 5'
    context_lines: int = 2


@dataclass
class PreviewConfig:
    """预览配置"""
    auto_open: bool = False
    cleanup_hours: int = 24
    pdf_export_options: str = 'SelectPdfVersion=1;ExportBookmarks=1'


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_enabled: bool = False
    file_path: str = 'word_image_insertion.log'


class Config:
    """主配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
        """
        # 默认配置
        self.image = ImageConfig()
        self.document = DocumentConfig()
        self.preview = PreviewConfig()
        self.logging = LoggingConfig()
        
        # 项目根目录
        self.project_root = Path(__file__).parent.parent
        
        # 临时目录
        self.temp_dir = Path.home() / '.word_image_insertion' / 'temp'
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存目录
        self.cache_dir = Path.home() / '.word_image_insertion' / 'cache'
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置文件
        if config_file:
            self.load_from_file(config_file)
        else:
            # 尝试加载默认配置文件
            default_config = self.project_root / 'config.json'
            if default_config.exists():
                self.load_from_file(str(default_config))
        
        # 从环境变量加载
        self.load_from_env()
        
        # 设置日志级别
        self.log_level = getattr(logging, self.logging.level.upper(), logging.INFO)
    
    def load_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                logger.warning(f"Config file not found: {config_file}")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            self._update_from_dict(config_data)
            logger.info(f"Configuration loaded from: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to load config from {config_file}: {e}")
    
    def load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            'WII_LOG_LEVEL': ('logging', 'level'),
            'WII_IMAGE_DPI': ('image', 'default_dpi'),
            'WII_MAX_WIDTH': ('image', 'max_width_inches'),
            'WII_DEVICE_SCALE': ('image', 'device_scale_factor'),
            'WII_CACHE_ENABLED': ('document', 'cache_enabled'),
            'WII_AUTO_OPEN': ('preview', 'auto_open'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    # 类型转换
                    config_obj = getattr(self, section)
                    current_value = getattr(config_obj, key)
                    
                    if isinstance(current_value, bool):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        value = int(value)
                    elif isinstance(current_value, float):
                        value = float(value)
                    
                    setattr(config_obj, key, value)
                    logger.debug(f"Set {section}.{key} = {value} from environment")
                    
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Failed to set {env_var}: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        for section_name, section_data in config_data.items():
            if hasattr(self, section_name) and isinstance(section_data, dict):
                config_obj = getattr(self, section_name)
                for key, value in section_data.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
                        logger.debug(f"Updated {section_name}.{key} = {value}")
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            config_data = {
                'image': asdict(self.image),
                'document': asdict(self.document),
                'preview': asdict(self.preview),
                'logging': asdict(self.logging)
            }
            
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to save config to {config_file}: {e}")
    
    def get_section_patterns(self) -> Dict[str, Dict[str, Any]]:
        """获取章节匹配模式配置"""
        return {
            'business_flow': {
                'keywords': ['三、业务流程', '业务流程'],
                'image_types': ['svg', 'png'],
                'image_width': 5.0,
            },
            'operation_flow': {
                'keywords': ['四、操作流程', '操作流程'],
                'image_types': ['html', 'png'],
                'image_width': 5.5,
            },
            'technical_solution': {
                'keywords': ['技术方案', '技术架构'],
                'image_types': ['svg', 'png'],
                'image_width': 6.0,
            },
            'implementation_plan': {
                'keywords': ['实施方案', '实施计划'],
                'image_types': ['html', 'png'],
                'image_width': 5.5,
            }
        }
    
    def get_playwright_options(self) -> Dict[str, Any]:
        """获取Playwright配置选项"""
        return {
            'headless': True,
            'device_scale_factor': self.image.device_scale_factor,
            'timeout': self.image.timeout,
            'viewport': {'width': 1920, 'height': 1080},
            'args': [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-extensions'
            ]
        }
    
    def get_image_generation_options(self) -> Dict[str, Any]:
        """获取图片生成选项"""
        return {
            'dpi': self.image.default_dpi,
            'format': self.image.default_format,
            'quality': self.image.quality,
            'max_width_inches': self.image.max_width_inches,
            'device_scale_factor': self.image.device_scale_factor
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'image': asdict(self.image),
            'document': asdict(self.document),
            'preview': asdict(self.preview),
            'logging': asdict(self.logging),
            'paths': {
                'project_root': str(self.project_root),
                'temp_dir': str(self.temp_dir),
                'cache_dir': str(self.cache_dir)
            }
        }
    
    def __repr__(self):
        return f"Config(image={self.image}, document={self.document}, preview={self.preview}, logging={self.logging})"
