<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- SLOT::header::BEGIN -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技项目管理</h1>
            <p class="text-gray-600">全生命周期标准化管理平台，支持国家级、省级、市级、区级科技项目管理</p>
        </div>
        <!-- SLOT::header::END -->

        <!-- SLOT::filters::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- CHUNK::filters-project-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
                    <input type="text" placeholder="请输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <!-- CHUNK::filters-project-name::END -->

                <!-- CHUNK::filters-org-name::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">承担单位</label>
                    <input type="text" placeholder="请输入承担单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <!-- CHUNK::filters-org-name::END -->

                <!-- CHUNK::filters-credit-code::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                    <input type="text" placeholder="请输入信用代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <!-- CHUNK::filters-credit-code::END -->

                <!-- CHUNK::filters-project-level::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目级别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="district">区级</option>
                    </select>
                </div>
                <!-- CHUNK::filters-project-level::END -->

                <!-- CHUNK::filters-project-status::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="declaration">申报中</option>
                        <option value="approval">立项中</option>
                        <option value="implementation">实施中</option>
                        <option value="acceptance">验收中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                <!-- CHUNK::filters-project-status::END -->

                <!-- CHUNK::filters-year-range::BEGIN -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申报年度</label>
                    <div class="flex space-x-2">
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">起始年份</option>
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                        <span class="flex items-center">至</span>
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                    </div>
                </div>
                <!-- CHUNK::filters-year-range::END -->
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <!-- CHUNK::filters-reset-button::BEGIN -->
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <!-- CHUNK::filters-reset-button::END -->

                <!-- CHUNK::filters-search-button::BEGIN -->
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
                <!-- CHUNK::filters-search-button::END -->
            </div>
        </div>
        <!-- SLOT::filters::END -->

        <!-- SLOT::actions::BEGIN -->
        <div class="flex justify-between items-center mb-4">
            <!-- CHUNK::actions-buttons::BEGIN -->
            <div class="flex space-x-3">
                <button onclick="openModal('importModal')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    批量导入
                </button>
                <button onclick="openModal('exportModal')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    批量导出
                </button>
            </div>
            <!-- CHUNK::actions-buttons::END -->

            <!-- CHUNK::actions-add-button::BEGIN -->
            <button onclick="openModal('addProjectModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                新增项目
            </button>
            <!-- CHUNK::actions-add-button::END -->
        </div>
        <!-- SLOT::actions::END -->

        <!-- SLOT::projects-table::BEGIN -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::projects-table-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研发</div>
                                <div class="text-sm text-gray-500">高新技术领域</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB2024KJ001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-01 至 2025-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-blue-100 text-blue-800">实施中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('projectDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editProjectModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject()" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::projects-table-row-1::END -->

                        <!-- CHUNK::projects-table-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">浙江省新材料应用研究</div>
                                <div class="text-sm text-gray-500">新材料领域</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZJ2023KJ045</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市YY材料研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-01 至 2025-05-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-green-100 text-green-800">已完成</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('projectDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editProjectModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject()" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::projects-table-row-2::END -->

                        <!-- CHUNK::projects-table-row-3::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">国家重大科技专项</div>
                                <div class="text-sm text-gray-500">高端装备</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">GJ2024ZD001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市ZZ装备有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-01 至 2026-02-28</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-5 rounded-full bg-yellow-100 text-yellow-800">立项中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('projectDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openModal('editProjectModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject()" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::projects-table-row-3::END -->
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::projects-table::END -->

        <!-- SLOT::stats-card::BEGIN -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            <!-- CHUNK::stats-total-projects::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">项目总数</p>
                        <p class="text-2xl font-semibold text-gray-900">156</p>
                    </div>
                </div>
            </div>
            <!-- CHUNK::stats-total-projects::END -->

            <!-- CHUNK::stats-national-projects::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">国家级项目</p>
                        <p class="text-2xl font-semibold text-gray-900">24</p>
                    </div>
                </div>
            </div>
            <!-- CHUNK::stats-national-projects::END -->

            <!-- CHUNK::stats-implementing-projects::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">实施中项目</p>
                        <p class="text-2xl font-semibold text-gray-900">78</p>
                    </div>
                </div>
            </div>
            <!-- CHUNK::stats-implementing-projects::END -->

            <!-- CHUNK::stats-completed-projects::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已完成项目</p>
                        <p class="text-2xl font-semibold text-gray-900">54</p>
                    </div>
                </div>
            </div>
            <!-- CHUNK::stats-completed-projects::END -->
        </div>
        <!-- SLOT::stats-card::END -->
    </div>

    <!-- SLOT::add-project-modal::BEGIN -->
    <div id="addProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增科技项目</h3>
                    <button onclick="closeModal('addProjectModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- CHUNK::add-project-name::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::add-project-name::END -->

                        <!-- CHUNK::add-project-code::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目编号</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::add-project-code::END -->

                        <!-- CHUNK::add-project-org::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">承担单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::add-project-org::END -->

                        <!-- CHUNK::add-project-leader::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::add-project-leader::END -->

                        <!-- CHUNK::add-project-level::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目级别 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择项目级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <!-- CHUNK::add-project-level::END -->

                        <!-- CHUNK::add-project-category::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目类别</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择项目类别</option>
                                <option value="high-tech">高新技术</option>
                                <option value="new-material">新材料</option>
                                <option value="biomedicine">生物医药</option>
                                <option value="energy">新能源</option>
                            </select>
                        </div>
                        <!-- CHUNK::add-project-category::END -->

                        <!-- CHUNK::add-project-start-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">开始日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::add-project-start-date::END -->

                        <!-- CHUNK::add-project-end-date::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">结束日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <!-- CHUNK::add-project-end-date::END -->

                        <!-- CHUNK::add-project-budget::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目经费(万元)</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <!-- CHUNK::add-project-budget::END -->

                        <!-- CHUNK::add-project-status::BEGIN -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目状态 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择项目状态</option>
                                <option value="declaration">申报中</option>
                                <option value="approval">立项中</option>
                                <option value="implementation">实施中</option>
                                <option value="acceptance">验收中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                        <!-- CHUNK::add-project-status::END -->
                    </div>

                    <!-- CHUNK::add-project-description::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">项目简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <!-- CHUNK::add-project-description::END -->

                    <!-- CHUNK::add-project-attachments::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500">支持PDF、Word、Excel格式</p>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::add-project-attachments::END -->

                    <div class="flex justify-end space-x-3 pt-4">
                        <!-- CHUNK::add-project-cancel-button::BEGIN -->
                        <button type="button" onclick="closeModal('addProjectModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            取消
                        </button>
                        <!-- CHUNK::add-project-cancel-button::END -->

                        <!-- CHUNK::add-project-submit-button::BEGIN -->
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            保存
                        </button>
                        <!-- CHUNK::add-project-submit-button::END -->
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- SLOT::add-project-modal::END -->

    <!-- SLOT::project-detail-modal::BEGIN -->
    <div id="projectDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeModal('projectDetailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-6">
                    <!-- CHUNK::detail-basic-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">项目名称：</span>
                                <span class="font-medium text-gray-900">宁波市智能制造关键技术研发</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目编号：</span>
                                <span class="font-medium text-gray-900">NB2024KJ001</span>
                            </div>
                            <div>
                                <span class="text-gray-500">承担单位：</span>
                                <span class="font-medium text-gray-900">宁波市XX科技有限公司</span>
                            </div>
                            <div>
                                <span class="text-gray-500">统一社会信用代码：</span>
                                <span class="font-medium text-gray-900">91330201MA2XXXXXX</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目负责人：</span>
                                <span class="font-medium text-gray-900">张研究员</span>
                            </div>
                            <div>
                                <span class="text-gray-500">联系电话：</span>
                                <span class="font-medium text-gray-900">13888888888</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目级别：</span>
                                <span class="font-medium text-gray-900">市级</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目类别：</span>
                                <span class="font-medium text-gray-900">高新技术</span>
                            </div>
                            <div>
                                <span class="text-gray-500">开始日期：</span>
                                <span class="font-medium text-gray-900">2024-01-01</span>
                            </div>
                            <div>
                                <span class="text-gray-500">结束日期：</span>
                                <span class="font-medium text-gray-900">2025-12-31</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目经费：</span>
                                <span class="font-medium text-gray-900">500万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目状态：</span>
                                <span class="font-medium text-gray-900">实施中</span>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-basic-info::END -->

                    <!-- CHUNK::detail-team-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目团队</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分工</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张研究员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13888888888</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术指导</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13999999999</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- CHUNK::detail-team-info::END -->

                    <!-- CHUNK::detail-funding-info::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">经费拨付情况</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付批次</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付金额(万元)</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付日期</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">已拨付</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">启动资金</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">第二批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">150</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">待拨付</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中期检查后拨付</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- CHUNK::detail-funding-info::END -->

                    <!-- CHUNK::detail-attachments::BEGIN -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目附件</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">项目申报书.pdf</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">项目合同.docx</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::detail-attachments::END -->

                    <!-- CHUNK::detail-actions::BEGIN -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button onclick="closeModal('projectDetailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            关闭
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            导出报告
                        </button>
                    </div>
                    <!-- CHUNK::detail-actions::END -->
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::project-detail-modal::END -->

    <!-- SLOT::import-modal::BEGIN -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批量导入科技项目</h3>
                    <button onclick="closeModal('importModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <!-- CHUNK::import-download-template::BEGIN -->
                    <div class="flex items-center justify-between">
                        <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            下载导入模板
                        </a>
                        <span class="text-xs text-gray-500">支持.xlsx格式</span>
                    </div>
                    <!-- CHUNK::import-download-template::END -->

                    <!-- CHUNK::import-upload-area::BEGIN -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                            </label>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                        </div>
                    </div>
                    <!-- CHUNK::import-upload-area::END -->

                    <!-- CHUNK::import-progress::BEGIN -->
                    <div class="hidden" id="importProgress">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">上传进度</span>
                            <span class="text-sm font-medium text-gray-700">75%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    <!-- CHUNK::import-progress::END -->

                    <!-- CHUNK::import-validation-result::BEGIN -->
                    <div class="hidden bg-yellow-50 p-4 rounded-lg" id="importValidationResult">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                    <li>第3行：项目编号已存在</li>
                                    <li>第5行：承担单位为必填项</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::import-validation-result::END -->

                    <div class="flex justify-end space-x-3 pt-4">
                        <!-- CHUNK::import-cancel-button::BEGIN -->
                        <button type="button" onclick="closeModal('importModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            取消
                        </button>
                        <!-- CHUNK::import-cancel-button::END -->

                        <!-- CHUNK::import-submit-button::BEGIN -->
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            开始导入
                        </button>
                        <!-- CHUNK::import-submit-button::END -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::import-modal::END -->

    <!-- SLOT::export-modal::BEGIN -->
    <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批量导出科技项目</h3>
                    <button onclick="closeModal('exportModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <!-- CHUNK::export-format::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportFormat" value="excel" class="text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">Excel (.xlsx)</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportFormat" value="csv" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">CSV (.csv)</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportFormat" value="pdf" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">PDF (.pdf)</span>
                            </label>
                        </div>
                    </div>
                    <!-- CHUNK::export-format::END -->

                    <!-- CHUNK::export-content::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出内容</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">基础信息</span>
                                </label>
                            </div>
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">项目团队</span>
                                </label>
                            </div>
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">经费信息</span>
                                </label>
                            </div>
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">附件列表</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <!-- CHUNK::export-content::END -->

                    <!-- CHUNK::export-range::BEGIN -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出范围</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportRange" value="current" class="text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">当前筛选结果</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportRange" value="all" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">全部项目</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportRange" value="selected" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">已选项目</span>
                            </label>
                        </div>
                    </div>
                    <!-- CHUNK::export-range::END -->

                    <div class="flex justify-end space-x-3 pt-4">
                        <!-- CHUNK::export-cancel-button::BEGIN -->
                        <button type="button" onclick="closeModal('exportModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            取消
                        </button>
                        <!-- CHUNK::export-cancel-button::END -->

                        <!-- CHUNK::export-submit-button::BEGIN -->
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            开始导出
                        </button>
                        <!-- CHUNK::export-submit-button::END -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::export-modal::END -->

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function deleteProject() {
            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {
                alert('项目已删除 (原型演示)');
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 文件上传处理
            document.getElementById('file-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    document.getElementById('importProgress').classList.remove('hidden');
                    // 模拟上传进度
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 10;
                        document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';
                        document.querySelector('#importProgress span:last-child').textContent = progress + '%';
                        if (progress >= 100) {
                            clearInterval(interval);
                            setTimeout(() => {
                                document.getElementById('importValidationResult').classList.remove('hidden');
                            }, 500);
                        }
                    }, 200);
                }
            });

            // 点击模态框外部关闭
            document.querySelectorAll('.modal-overlay').forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（原型演示）
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (原型演示)');
                    const parentModalId = form.closest('[id]').id;
                    if (parentModalId) closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>