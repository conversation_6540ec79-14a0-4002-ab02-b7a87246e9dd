{"timestamp": "2025-07-26 21:39:34", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Sat, 26 Jul 2025 13:39:45 GMT", "content-type": "application/json; charset=utf-8", "content-length": "7695", "x-client-request-id": "unknown-20250726213836-NBIFbtoN", "x-envoy-upstream-service-time": "69931", "x-request-id": "0217535371165554b33b7fe07e64e0d4f41d33f2775486d433931"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Here's the professional, hierarchical, and aesthetically pleasing SVG flowchart based on your requirements:\n\n```svg\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" font-family=\"'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif\">\n  <!-- Definitions -->\n  <defs>\n    <!-- Arrow style -->\n    <marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" refX=\"9\" refY=\"3.5\" orient=\"auto\">\n      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"#333\" />\n    </marker>\n    <!-- Shadow filter -->\n    <filter id=\"soft-shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.1\" />\n    </filter>\n  </defs>\n\n  <!-- Background -->\n  <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n\n  <!-- Main Title -->\n  <text x=\"600\" y=\"50\" font-size=\"28\" text-anchor=\"middle\" font-weight=\"600\" fill=\"#333\">科技人才数据管理系统流程</text>\n\n  <!-- Stage 1: Data Aggregation -->\n  <text x=\"600\" y=\"100\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段一：数据汇聚与展示</text>\n  \n  <!-- Node 1: Data Aggregation -->\n  <g transform=\"translate(400, 130)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"400\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n    <text x=\"200\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">全市科技人才数据汇聚</text>\n    <text x=\"200\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">自动汇聚各类人才基础数据，实时刷新概览卡片和分布大屏</text>\n  </g>\n\n  <!-- Stage 2: Data Interaction -->\n  <text x=\"600\" y=\"240\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段二：数据交互与筛选</text>\n\n  <!-- Node 2: Category Selection -->\n  <g transform=\"translate(400, 270)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"400\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n    <text x=\"200\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才类别选择</text>\n    <text x=\"200\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">用户通过类型切换区选择感兴趣的人才类别，系统刷新数据</text>\n  </g>\n\n  <!-- Node 3: Talent List -->\n  <g transform=\"translate(400, 370)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"400\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n    <text x=\"200\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才清册展示</text>\n    <text x=\"200\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">点击卡片或区块后展示符合条件的人员信息列表</text>\n  </g>\n\n  <!-- Stage 3: Detailed View -->\n  <text x=\"600\" y=\"480\" font-size=\"20\" text-anchor=\"middle\" font-weight=\"500\" fill=\"#555\">阶段三：详细信息与操作</text>\n\n  <!-- Node 4: Talent Details -->\n  <g transform=\"translate(300, 510)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"300\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"150\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">人才详情查看</text>\n    <text x=\"150\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">展示科研背景、项目、成果等详细资料</text>\n  </g>\n\n  <!-- Node 5: Data Operations -->\n  <g transform=\"translate(600, 510)\" filter=\"url(#soft-shadow)\">\n    <rect width=\"300\" height=\"70\" rx=\"8\" ry=\"8\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n    <text x=\"150\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"600\" fill=\"#333\">数据筛选与导出</text>\n    <text x=\"150\" y=\"55\" text-anchor=\"middle\" font-size=\"12\" fill=\"#555\">精细筛查或导出选定人员信息，记录操作日志</text>\n  </g>\n\n  <!-- Connection lines -->\n  <path d=\"M 600 200 Q 600 215 600 230\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 600 340 Q 600 355 600 370\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 450 440 C 450 460, 300 480, 300 510\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n  <path d=\"M 750 440 C 750 460, 900 480, 900 510\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\" marker-end=\"url(#arrowhead)\" />\n\n  <!-- Legend -->\n  <rect x=\"50\" y=\"650\" width=\"20\" height=\"20\" rx=\"4\" ry=\"4\" fill=\"#E3F2FD\" stroke=\"#90CAF9\" stroke-width=\"1.5\" />\n  <text x=\"80\" y=\"665\" font-size=\"14\" fill=\"#333\">数据汇聚</text>\n  \n  <rect x=\"200\" y=\"650\" width=\"20\" height=\"20\" rx=\"4\" ry=\"4\" fill=\"#E6F4EA\" stroke=\"#A5D6A7\" stroke-width=\"1.5\" />\n  <text x=\"230\" y=\"665\" font-size=\"14\" fill=\"#333\">交互筛选</text>\n  \n  <rect x=\"350\" y=\"650\" width=\"20\" height=\"20\" rx=\"4\" ry=\"4\" fill=\"#F3E8FD\" stroke=\"#CE93D8\" stroke-width=\"1.5\" />\n  <text x=\"380\" y=\"665\" font-size=\"14\" fill=\"#333\">列表展示</text>\n  \n  <rect x=\"500\" y=\"650\" width=\"20\" height=\"20\" rx=\"4\" ry=\"4\" fill=\"#FFFBEB\" stroke=\"#FFE082\" stroke-width=\"1.5\" />\n  <text x=\"530\" y=\"665\" font-size=\"14\" fill=\"#333\">详情与操作</text>\n</svg>\n```\n\nThis SVG flowchart features:\n\n1. **Clear Hierarchy**: Divided into three distinct stages with labeled section headers\n2. **Consistent Styling**: \n   - Soft color scheme with different colors for each stage\n   - Rounded rectangles with subtle shadows\n   - Clean typography with title/description structure\n\n3. **Logical Flow**:\n   - Data aggregation → Category selection → Talent list → Details/Operations\n   - Smooth Bezier curves connecting nodes\n\n4. **Professional Elements**:\n   - Legend explaining the color coding\n   - Proper spacing and alignment\n   - Responsive viewBox dimensions\n\nThe flowchart visually represents the complete workflow from data collection to detailed talent analysis while maintaining a clean, professional appearance.", "role": "assistant"}}], "created": 1753537186, "id": "0217535371165554b33b7fe07e64e0d4f41d33f2775486d433931", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 1842, "prompt_tokens": 2306, "total_tokens": 4148, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}