<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技人才总数概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- SLOT::header::BEGIN -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技人才总数概览</h1>
            <p class="text-gray-600">全市范围内研发人员、科研管理人员、专家、科技特派员等多类科技人才的规模与分布分析</p>
        </div>
        <!-- SLOT::header::END -->

        <!-- SLOT::filter-section::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">人才类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="rd">研发人员</option>
                        <option value="manager">科研管理人员</option>
                        <option value="expert">专家</option>
                        <option value="special">科技特派员</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属区域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部区域</option>
                        <option value="haishu">海曙区</option>
                        <option value="jiangdong">江东区</option>
                        <option value="jiangbei">江北区</option>
                        <option value="yinzhou">鄞州区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部行业</option>
                        <option value="it">信息技术</option>
                        <option value="bio">生物医药</option>
                        <option value="material">新材料</option>
                        <option value="energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">引进方式</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部方式</option>
                        <option value="local">本地培养</option>
                        <option value="introduce">人才引进</option>
                        <option value="return">海归人才</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>
        <!-- SLOT::filter-section::END -->

        <!-- SLOT::stats-cards::BEGIN -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- CHUNK::stats-card-rd::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="showTalentList('rd')">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">研发人员</h3>
                        <p class="text-sm text-gray-500">全市研发人员总数</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">12,847</p>
                    <p class="text-sm text-gray-500 mt-1">较上月 <span class="text-green-600">+5.2%</span></p>
                </div>
            </div>
            <!-- CHUNK::stats-card-rd::END -->

            <!-- CHUNK::stats-card-manager::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="showTalentList('manager')">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">科研管理人员</h3>
                        <p class="text-sm text-gray-500">全市科研管理人员总数</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">3,245</p>
                    <p class="text-sm text-gray-500 mt-1">较上月 <span class="text-green-600">+2.1%</span></p>
                </div>
            </div>
            <!-- CHUNK::stats-card-manager::END -->

            <!-- CHUNK::stats-card-expert::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="showTalentList('expert')">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">专家人才</h3>
                        <p class="text-sm text-gray-500">全市专家人才总数</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">1,856</p>
                    <p class="text-sm text-gray-500 mt-1">较上月 <span class="text-green-600">+3.7%</span></p>
                </div>
            </div>
            <!-- CHUNK::stats-card-expert::END -->

            <!-- CHUNK::stats-card-special::BEGIN -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="showTalentList('special')">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">科技特派员</h3>
                        <p class="text-sm text-gray-500">全市科技特派员总数</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-3xl font-bold text-gray-900">756</p>
                    <p class="text-sm text-gray-500 mt-1">较上月 <span class="text-green-600">+1.5%</span></p>
                </div>
            </div>
            <!-- CHUNK::stats-card-special::END -->
        </div>
        <!-- SLOT::stats-cards::END -->

        <!-- SLOT::distribution-section::BEGIN -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-900">人才分布分析</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">区域分布</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">行业分布</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">年龄结构</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="h-80">
                    <canvas id="regionChart"></canvas>
                </div>
                <div class="h-80">
                    <canvas id="industryChart"></canvas>
                </div>
            </div>
        </div>
        <!-- SLOT::distribution-section::END -->

        <!-- SLOT::talent-list::BEGIN -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">科技人才列表</h2>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出数据
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增人才
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人才类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引进方式</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- CHUNK::talent-row-1::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张明</div>
                                        <div class="text-sm text-gray-500">高级工程师</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">研发人员</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">信息技术</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">本地培养</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">38</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showTalentDetail('1')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-1::END -->

                        <!-- CHUNK::talent-row-2::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李华</div>
                                        <div class="text-sm text-gray-500">研究员</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">科研管理人员</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市XX研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人才引进</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showTalentDetail('2')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-2::END -->

                        <!-- CHUNK::talent-row-3::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">王伟</div>
                                        <div class="text-sm text-gray-500">教授</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">专家人才</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">海归人才</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">52</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showTalentDetail('3')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-3::END -->

                        <!-- CHUNK::talent-row-4::BEGIN -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">赵芳</div>
                                        <div class="text-sm text-gray-500">高级农艺师</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">科技特派员</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市农业技术推广中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农业科技</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">本地培养</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">41</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showTalentDetail('4')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- CHUNK::talent-row-4::END -->
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">2,847</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- SLOT::talent-list::END -->
    </div>

    <!-- SLOT::talent-detail-modal::BEGIN -->
    <div id="talentDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">人才详细信息</h3>
                    <button onclick="closeModal('talentDetailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-1">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex flex-col items-center">
                                <img class="h-32 w-32 rounded-full mb-4" src="https://source.unsplash.com/random/300x300/?portrait" alt="">
                                <h4 class="text-xl font-medium text-gray-900">张明</h4>
                                <p class="text-sm text-gray-500">高级工程师</p>
                                <div class="mt-4">
                                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">研发人员</span>
                                </div>
                            </div>
                            <div class="mt-6 space-y-3">
                                <div>
                                    <span class="text-sm text-gray-500">所属单位：</span>
                                    <span class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</span>
                                </div>
                                <div>
                                    <span class="text-sm text-gray-500">行业领域：</span>
                                    <span class="text-sm font-medium text-gray-900">信息技术</span>
                                </div>
                                <div>
                                    <span class="text-sm text-gray-500">引进方式：</span>
                                    <span class="text-sm font-medium text-gray-900">本地培养</span>
                                </div>
                                <div>
                                    <span class="text-sm text-gray-500">年龄：</span>
                                    <span class="text-sm font-medium text-gray-900">38岁</span>
                                </div>
                                <div>
                                    <span class="text-sm text-gray-500">联系电话：</span>
                                    <span class="text-sm font-medium text-gray-900">138****1234</span>
                                </div>
                                <div>
                                    <span class="text-sm text-gray-500">电子邮箱：</span>
                                    <span class="text-sm font-medium text-gray-900"><EMAIL></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="lg:col-span-2">
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">性别：</span>
                                    <span class="font-medium text-gray-900">男</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">出生日期：</span>
                                    <span class="font-medium text-gray-900">1985-05-15</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">学历：</span>
                                    <span class="font-medium text-gray-900">硕士研究生</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">专业：</span>
                                    <span class="font-medium text-gray-900">计算机科学与技术</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">职称：</span>
                                    <span class="font-medium text-gray-900">高级工程师</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">工作年限：</span>
                                    <span class="font-medium text-gray-900">15年</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">科研参与</h4>
                            <div class="text-sm space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">参与项目数量：</span>
                                    <span class="font-medium text-gray-900">12个</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">主持项目数量：</span>
                                    <span class="font-medium text-gray-900">5个</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">发表论文数量：</span>
                                    <span class="font-medium text-gray-900">8篇</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">专利数量：</span>
                                    <span class="font-medium text-gray-900">3项</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">获奖情况</h4>
                            <div class="text-sm space-y-3">
                                <div>
                                    <p class="font-medium text-gray-900">宁波市科技进步一等奖</p>
                                    <p class="text-gray-500">2022年</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">浙江省优秀科技工作者</p>
                                    <p class="text-gray-500">2020年</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">国家发明专利优秀奖</p>
                                    <p class="text-gray-500">2019年</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="closeModal('talentDetailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        关闭
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        编辑信息
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- SLOT::talent-detail-modal::END -->

    <script>
        // --- 全局UI控制函数 ---
        function showTalentList(type) {
            // 根据不同类型筛选人才列表
            console.log('显示类型为' + type + '的人才列表');
        }

        function showTalentDetail(id) {
            openModal('talentDetailModal');
        }

        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化区域分布图表
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'bar',
                data: {
                    labels: ['海曙区', '江东区', '江北区', '鄞州区', '镇海区', '北仑区'],
                    datasets: [{
                        label: '人才数量',
                        data: [2156, 1876, 1543, 3245, 1234, 1567],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化行业分布图表
            const industryCtx = document.getElementById('industryChart').getContext('2d');
            new Chart(industryCtx, {
                type: 'pie',
                data: {
                    labels: ['信息技术', '生物医药', '新材料', '新能源', '智能制造', '其他'],
                    datasets: [{
                        data: [3245, 2156, 1876, 1234, 1567, 756],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('talentDetailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('talentDetailModal');
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('talentDetailModal').classList.contains('hidden')) {
                    closeModal('talentDetailModal');
                }
            });
        });
    </script>
</body>
</html>