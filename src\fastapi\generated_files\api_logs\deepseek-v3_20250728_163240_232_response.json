{"timestamp": "2025-07-28 16:37:20", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Mon, 28 Jul 2025 08:37:21 GMT", "content-type": "application/json; charset=utf-8", "content-length": "36694", "x-client-request-id": "unknown-20250728163241-yDvjIOEG", "x-envoy-upstream-service-time": "279991", "x-request-id": "021753691561455ffaecfd6d28214757daa7e292561e4cfe20258"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>研发人员详情展示</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n</head>\n<body class=\"bg-gray-50 min-h-screen\">\n    <!-- SLOT::header::BEGIN -->\n    <header class=\"bg-white shadow-sm\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n            <div class=\"flex justify-between items-center\">\n                <h1 class=\"text-2xl font-bold text-gray-900\">研发人员详情</h1>\n                <div class=\"flex space-x-3\">\n                    <button class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                        </svg>\n                        导出简历\n                    </button>\n                    <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                        </svg>\n                        编辑信息\n                    </button>\n                </div>\n            </div>\n        </div>\n    </header>\n    <!-- SLOT::header::END -->\n\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <!-- SLOT::basic-info::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n            <div class=\"flex flex-col md:flex-row\">\n                <!-- CHUNK::basic-info-avatar::BEGIN -->\n                <div class=\"flex-shrink-0 mb-4 md:mb-0 md:mr-6\">\n                    <img src=\"https://source.unsplash.com/150x150/?portrait\" alt=\"头像\" class=\"w-32 h-32 rounded-full object-cover border-4 border-blue-100\">\n                </div>\n                <!-- CHUNK::basic-info-avatar::END -->\n\n                <div class=\"flex-1\">\n                    <!-- CHUNK::basic-info-name::BEGIN -->\n                    <h2 class=\"text-2xl font-bold text-gray-900 mb-2\">张明远</h2>\n                    <!-- CHUNK::basic-info-name::END -->\n\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                        <!-- CHUNK::basic-info-gender::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">性别：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">男</span>\n                        </div>\n                        <!-- CHUNK::basic-info-gender::END -->\n\n                        <!-- CHUNK::basic-info-age::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">年龄：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">38岁</span>\n                        </div>\n                        <!-- CHUNK::basic-info-age::END -->\n\n                        <!-- CHUNK::basic-info-birthday::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">出生日期：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">1985-05-12</span>\n                        </div>\n                        <!-- CHUNK::basic-info-birthday::END -->\n\n                        <!-- CHUNK::basic-info-education::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">学历：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">博士</span>\n                        </div>\n                        <!-- CHUNK::basic-info-education::END -->\n\n                        <!-- CHUNK::basic-info-degree::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">学位：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">工学博士</span>\n                        </div>\n                        <!-- CHUNK::basic-info-degree::END -->\n\n                        <!-- CHUNK::basic-info-school::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">毕业院校：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">浙江大学</span>\n                        </div>\n                        <!-- CHUNK::basic-info-school::END -->\n\n                        <!-- CHUNK::basic-info-company::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">工作单位：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">宁波市XX科技有限公司</span>\n                        </div>\n                        <!-- CHUNK::basic-info-company::END -->\n\n                        <!-- CHUNK::basic-info-title::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">技术职称：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">高级工程师</span>\n                        </div>\n                        <!-- CHUNK::basic-info-title::END -->\n\n                        <!-- CHUNK::basic-info-field::BEGIN -->\n                        <div>\n                            <span class=\"text-sm text-gray-500\">技术领域：</span>\n                            <span class=\"text-sm font-medium text-gray-900\">人工智能、大数据分析</span>\n                        </div>\n                        <!-- CHUNK::basic-info-field::END -->\n                    </div>\n\n                    <!-- CHUNK::basic-info-contact::BEGIN -->\n                    <div class=\"mt-4 pt-4 border-t border-gray-200\">\n                        <h3 class=\"text-sm font-medium text-gray-500 mb-2\">联系方式</h3>\n                        <div class=\"flex flex-wrap gap-4\">\n                            <div>\n                                <span class=\"text-sm text-gray-500\">手机：</span>\n                                <span class=\"text-sm font-medium text-gray-900\">138****1234</span>\n                            </div>\n                            <div>\n                                <span class=\"text-sm text-gray-500\">邮箱：</span>\n                                <span class=\"text-sm font-medium text-gray-900\"><EMAIL></span>\n                            </div>\n                        </div>\n                    </div>\n                    <!-- CHUNK::basic-info-contact::END -->\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::basic-info::END -->\n\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            <div class=\"lg:col-span-2 space-y-6\">\n                <!-- SLOT::achievements::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <div class=\"flex justify-between items-center mb-4\">\n                        <h2 class=\"text-xl font-semibold text-gray-900\">科技成果</h2>\n                        <div class=\"flex space-x-2\">\n                            <select class=\"px-3 py-1 text-sm border border-gray-300 rounded-md\">\n                                <option>全部类型</option>\n                                <option>专利</option>\n                                <option>论文</option>\n                                <option>其他</option>\n                            </select>\n                            <button class=\"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 inline mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\" />\n                                </svg>\n                                筛选\n                            </button>\n                        </div>\n                    </div>\n\n                    <div class=\"space-y-4\">\n                        <!-- CHUNK::achievements-patent-1::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-4 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-md font-medium text-blue-600\">一种基于深度学习的图像识别方法及系统</h3>\n                                <span class=\"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded\">发明专利</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-500\">\n                                <span>专利号：ZL202010123456.7</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>授权日期：2022-03-15</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-700\">\n                                本发明公开了一种基于深度学习的图像识别方法及系统，通过改进的卷积神经网络结构，提高了图像识别的准确率和效率...\n                            </div>\n                            <div class=\"mt-2 flex justify-between items-center\">\n                                <span class=\"text-sm text-gray-500\">第一发明人</span>\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看详情</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::achievements-patent-1::END -->\n\n                        <!-- CHUNK::achievements-paper-1::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-4 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-md font-medium text-blue-600\">基于Transformer的大规模预训练语言模型研究</h3>\n                                <span class=\"text-sm bg-green-100 text-green-800 px-2 py-1 rounded\">SCI论文</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-500\">\n                                <span>期刊：计算机学报</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>发表时间：2021-08-20</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-700\">\n                                本文提出了一种改进的Transformer结构，在大规模中文语料上进行了预训练，实验表明该模型在多个NLP任务上达到了SOTA...\n                            </div>\n                            <div class=\"mt-2 flex justify-between items-center\">\n                                <span class=\"text-sm text-gray-500\">通讯作者</span>\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看详情</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::achievements-paper-1::END -->\n                    </div>\n\n                    <div class=\"mt-4 pt-4 border-t border-gray-200\">\n                        <button class=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 inline mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\n                            </svg>\n                            导出全部科技成果\n                        </button>\n                    </div>\n                </div>\n                <!-- SLOT::achievements::END -->\n\n                <!-- SLOT::awards::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">奖励荣誉</h2>\n                    \n                    <div class=\"space-y-4\">\n                        <!-- CHUNK::awards-national::BEGIN -->\n                        <div class=\"border-l-4 border-blue-500 pl-4 py-2\">\n                            <div class=\"flex justify-between items-start\">\n                                <div>\n                                    <h3 class=\"text-md font-medium text-gray-900\">国家科学技术进步奖</h3>\n                                    <div class=\"mt-1 text-sm text-gray-500\">\n                                        <span>二等奖</span>\n                                        <span class=\"mx-2\">|</span>\n                                        <span>2020年度</span>\n                                    </div>\n                                </div>\n                                <span class=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">国家级</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-700\">\n                                项目名称：基于人工智能的智能制造关键技术研究与应用\n                            </div>\n                            <div class=\"mt-2\">\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看证书</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::awards-national::END -->\n\n                        <!-- CHUNK::awards-provincial::BEGIN -->\n                        <div class=\"border-l-4 border-green-500 pl-4 py-2\">\n                            <div class=\"flex justify-between items-start\">\n                                <div>\n                                    <h3 class=\"text-md font-medium text-gray-900\">浙江省科学技术奖</h3>\n                                    <div class=\"mt-1 text-sm text-gray-500\">\n                                        <span>一等奖</span>\n                                        <span class=\"mx-2\">|</span>\n                                        <span>2018年度</span>\n                                    </div>\n                                </div>\n                                <span class=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">省级</span>\n                            </div>\n                            <div class=\"mt-2 text-sm text-gray-700\">\n                                项目名称：大数据分析与挖掘关键技术研究\n                            </div>\n                            <div class=\"mt-2\">\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看证书</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::awards-provincial::END -->\n                    </div>\n                </div>\n                <!-- SLOT::awards::END -->\n\n                <!-- SLOT::experience::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">教育与工作经历</h2>\n                    \n                    <div class=\"space-y-6\">\n                        <!-- CHUNK::experience-education::BEGIN -->\n                        <div>\n                            <h3 class=\"text-lg font-medium text-gray-900 mb-3\">教育经历</h3>\n                            <div class=\"space-y-4\">\n                                <div class=\"pl-4 border-l-2 border-blue-200\">\n                                    <div class=\"flex justify-between\">\n                                        <span class=\"text-sm font-medium text-gray-900\">浙江大学</span>\n                                        <span class=\"text-sm text-gray-500\">2003-2007</span>\n                                    </div>\n                                    <div class=\"text-sm text-gray-500\">计算机科学与技术 / 本科</div>\n                                </div>\n                                <div class=\"pl-4 border-l-2 border-blue-200\">\n                                    <div class=\"flex justify-between\">\n                                        <span class=\"text-sm font-medium text-gray-900\">浙江大学</span>\n                                        <span class=\"text-sm text-gray-500\">2007-2012</span>\n                                    </div>\n                                    <div class=\"text-sm text-gray-500\">计算机应用技术 / 博士</div>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::experience-education::END -->\n\n                        <!-- CHUNK::experience-work::BEGIN -->\n                        <div>\n                            <h3 class=\"text-lg font-medium text-gray-900 mb-3\">工作经历</h3>\n                            <div class=\"space-y-4\">\n                                <div class=\"pl-4 border-l-2 border-green-200\">\n                                    <div class=\"flex justify-between\">\n                                        <span class=\"text-sm font-medium text-gray-900\">宁波市XX科技有限公司</span>\n                                        <span class=\"text-sm text-gray-500\">2012-至今</span>\n                                    </div>\n                                    <div class=\"text-sm text-gray-500\">首席技术官</div>\n                                </div>\n                                <div class=\"pl-4 border-l-2 border-green-200\">\n                                    <div class=\"flex justify-between\">\n                                        <span class=\"text-sm font-medium text-gray-900\">阿里巴巴集团</span>\n                                        <span class=\"text-sm text-gray-500\">2010-2012</span>\n                                    </div>\n                                    <div class=\"text-sm text-gray-500\">算法工程师（实习）</div>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::experience-work::END -->\n                    </div>\n                </div>\n                <!-- SLOT::experience::END -->\n            </div>\n\n            <div class=\"space-y-6\">\n                <!-- SLOT::titles::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">称号认定</h2>\n                    \n                    <div class=\"space-y-3\">\n                        <!-- CHUNK::titles-national::BEGIN -->\n                        <div class=\"p-3 bg-blue-50 rounded-lg\">\n                            <div class=\"flex justify-between\">\n                                <span class=\"text-sm font-medium text-gray-900\">国家\"万人计划\"科技创新领军人才</span>\n                                <span class=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">国家级</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">2019年 / 中组部</div>\n                        </div>\n                        <!-- CHUNK::titles-national::END -->\n\n                        <!-- CHUNK::titles-provincial::BEGIN -->\n                        <div class=\"p-3 bg-green-50 rounded-lg\">\n                            <div class=\"flex justify-between\">\n                                <span class=\"text-sm font-medium text-gray-900\">浙江省\"151人才工程\"第一层次</span>\n                                <span class=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">省级</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">2017年 / 浙江省人社厅</div>\n                        </div>\n                        <!-- CHUNK::titles-provincial::END -->\n                    </div>\n                </div>\n                <!-- SLOT::titles::END -->\n\n                <!-- SLOT::projects::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">科研项目</h2>\n                    \n                    <div class=\"space-y-4\">\n                        <!-- CHUNK::projects-national::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-3 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-sm font-medium text-gray-900\">国家重点研发计划项目</h3>\n                                <span class=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">国家级</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                <span>项目编号：2020YFB1000001</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>2019-2023</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-700\">\n                                项目名称：新一代人工智能关键技术研究与应用示范\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                角色：项目负责人\n                            </div>\n                        </div>\n                        <!-- CHUNK::projects-national::END -->\n\n                        <!-- CHUNK::projects-provincial::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-3 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-sm font-medium text-gray-900\">浙江省重点研发计划项目</h3>\n                                <span class=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">省级</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                <span>项目编号：2020C01001</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>2018-2021</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-700\">\n                                项目名称：大数据分析与挖掘关键技术研究\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                角色：技术负责人\n                            </div>\n                        </div>\n                        <!-- CHUNK::projects-provincial::END -->\n                    </div>\n                </div>\n                <!-- SLOT::projects::END -->\n\n                <!-- SLOT::network::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">合作关系网络</h2>\n                    \n                    <div class=\"bg-gray-100 rounded-lg h-64 flex items-center justify-center\">\n                        <div class=\"text-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 mx-auto text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n                            </svg>\n                            <p class=\"mt-2 text-sm text-gray-500\">合作关系网络图</p>\n                        </div>\n                    </div>\n                </div>\n                <!-- SLOT::network::END -->\n\n                <!-- SLOT::policy::BEGIN -->\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">政策享受</h2>\n                    \n                    <div class=\"space-y-3\">\n                        <!-- CHUNK::policy-subsidy-1::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-3 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-sm font-medium text-gray-900\">宁波市高层次人才安家补助</h3>\n                                <span class=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">已发放</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                <span>金额：80万元</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>2020年</span>\n                            </div>\n                            <div class=\"mt-1\">\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看详情</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::policy-subsidy-1::END -->\n\n                        <!-- CHUNK::policy-subsidy-2::BEGIN -->\n                        <div class=\"border border-gray-200 rounded-lg p-3 hover:bg-gray-50\">\n                            <div class=\"flex justify-between\">\n                                <h3 class=\"text-sm font-medium text-gray-900\">浙江省\"151人才工程\"科研资助</h3>\n                                <span class=\"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded\">审核中</span>\n                            </div>\n                            <div class=\"mt-1 text-sm text-gray-500\">\n                                <span>金额：50万元</span>\n                                <span class=\"mx-2\">|</span>\n                                <span>2023年</span>\n                            </div>\n                            <div class=\"mt-1\">\n                                <button class=\"text-sm text-blue-600 hover:text-blue-800\">查看详情</button>\n                            </div>\n                        </div>\n                        <!-- CHUNK::policy-subsidy-2::END -->\n                    </div>\n                </div>\n                <!-- SLOT::policy::END -->\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 全局UI控制函数\n        function openModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden';\n            }\n        }\n\n        function closeModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto';\n            }\n        }\n\n        // DOM加载完毕后执行的初始化代码\n        document.addEventListener('DOMContentLoaded', function() {\n            // 初始化合作关系网络图\n            const networkCtx = document.createElement('canvas');\n            networkCtx.id = 'networkChart';\n            document.querySelector('#network .bg-gray-100').appendChild(networkCtx);\n            \n            const ctx = networkCtx.getContext('2d');\n            new Chart(ctx, {\n                type: 'graph',\n                data: {\n                    nodes: [\n                        {id: '张明远', label: '张明远', color: 'rgba(59, 130, 246, 0.8)'},\n                        {id: '李研究员', label: '李研究员', color: 'rgba(16, 185, 129, 0.8)'},\n                        {id: '王教授', label: '王教授', color: 'rgba(245, 158, 11, 0.8)'},\n                        {id: '宁波大学', label: '宁波大学', color: 'rgba(139, 92, 246, 0.8)'},\n                        {id: '浙江大学', label: '浙江大学', color: 'rgba(239, 68, 68, 0.8)'}\n                    ],\n                    edges: [\n                        {from: '张明远', to: '李研究员', label: '合作5次'},\n                        {from: '张明远', to: '王教授', label: '合作3次'},\n                        {from: '张明远', to: '宁波大学', label: '项目合作'},\n                        {from: '张明远', to: '浙江大学', label: '项目合作'}\n                    ]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    layout: {\n                        hierarchical: false\n                    }\n                }\n            });\n\n            // 为所有弹窗绑定\"点击外部关闭\"事件\n            const modals = document.querySelectorAll('.modal-overlay');\n            modals.forEach(modal => {\n                modal.addEventListener('click', function(event) {\n                    if (event.target === modal) {\n                        closeModal(modal.id);\n                    }\n                });\n\n                document.addEventListener('keydown', function (e) {\n                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {\n                        closeModal(modal.id);\n                    }\n                });\n            });\n        });\n    </script>\n</body>\n</html>", "role": "assistant"}}], "created": 1753691841, "id": "021753691561455ffaecfd6d28214757daa7e292561e4cfe20258", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 6411, "prompt_tokens": 39169, "total_tokens": 45580, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}