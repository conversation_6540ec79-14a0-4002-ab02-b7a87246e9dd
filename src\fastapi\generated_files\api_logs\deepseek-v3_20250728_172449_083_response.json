{"timestamp": "2025-07-28 17:31:31", "status_code": 200, "headers": {"server": "istio-envoy", "date": "Mon, 28 Jul 2025 09:31:32 GMT", "content-type": "application/json; charset=utf-8", "content-length": "56611", "x-client-request-id": "unknown-20250728172450-RWrGjBMu", "x-envoy-upstream-service-time": "401953", "x-request-id": "02175369469033761ee2447fb8673108fd2fdb6d547618fcaa57c"}, "response_data": {"choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>科技项目管理</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n</head>\n<body class=\"bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen\">\n    <!-- SLOT::header::BEGIN -->\n    <div class=\"bg-white shadow-sm border-b\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div class=\"flex justify-between items-center py-6\">\n                <div>\n                    <!-- CHUNK::header-title::BEGIN -->\n                    <h1 class=\"text-3xl font-bold text-gray-900\">科技项目管理</h1>\n                    <!-- CHUNK::header-title::END -->\n                    <!-- CHUNK::header-subtitle::BEGIN -->\n                    <p class=\"mt-2 text-sm text-gray-600\">科技项目全生命周期管理平台</p>\n                    <!-- CHUNK::header-subtitle::END -->\n                </div>\n                <div class=\"flex space-x-3\">\n                    <!-- CHUNK::header-export-button::BEGIN -->\n                    <button class=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                        </svg>\n                        数据导出\n                    </button>\n                    <!-- CHUNK::header-export-button::END -->\n                    <!-- CHUNK::header-refresh-button::BEGIN -->\n                    <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\">\n                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                        </svg>\n                        刷新数据\n                    </button>\n                    <!-- CHUNK::header-refresh-button::END -->\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::header::END -->\n\n    <!-- SLOT::main-content::BEGIN -->\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <!-- SLOT::filters::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <!-- CHUNK::filters-title::BEGIN -->\n            <h2 class=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <svg class=\"w-5 h-5 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n                </svg>\n                高级检索\n            </h2>\n            <!-- CHUNK::filters-title::END -->\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                <!-- CHUNK::filter-project-name::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目名称</label>\n                    <input type=\"text\" placeholder=\"请输入项目名称\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <!-- CHUNK::filter-project-name::END -->\n                \n                <!-- CHUNK::filter-company::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">承担单位</label>\n                    <input type=\"text\" placeholder=\"请输入承担单位\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <!-- CHUNK::filter-company::END -->\n                \n                <!-- CHUNK::filter-credit-code::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">统一社会信用代码</label>\n                    <input type=\"text\" placeholder=\"请输入信用代码\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                </div>\n                <!-- CHUNK::filter-credit-code::END -->\n                \n                <!-- CHUNK::filter-batch::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">申报批次</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                        <option value=\"\">全部批次</option>\n                        <option value=\"2023-1\">2023年第一批</option>\n                        <option value=\"2023-2\">2023年第二批</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-batch::END -->\n                \n                <!-- CHUNK::filter-status::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目状态</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                        <option value=\"\">全部状态</option>\n                        <option value=\"declared\">已申报</option>\n                        <option value=\"approved\">已立项</option>\n                        <option value=\"in-progress\">实施中</option>\n                        <option value=\"completed\">已完成</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-status::END -->\n                \n                <!-- CHUNK::filter-year::BEGIN -->\n                <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-2\">申报年度</label>\n                    <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                        <option value=\"\">全部年度</option>\n                        <option value=\"2023\">2023年</option>\n                        <option value=\"2022\">2022年</option>\n                    </select>\n                </div>\n                <!-- CHUNK::filter-year::END -->\n            </div>\n            \n            <div class=\"flex space-x-3 mt-4\">\n                <!-- CHUNK::filter-reset-button::BEGIN -->\n                <button class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                    <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                    </svg>\n                    重置筛选\n                </button>\n                <!-- CHUNK::filter-reset-button::END -->\n                \n                <!-- CHUNK::filter-search-button::BEGIN -->\n                <button class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\">\n                    <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n                    </svg>\n                    查询\n                </button>\n                <!-- CHUNK::filter-search-button::END -->\n            </div>\n        </div>\n        <!-- SLOT::filters::END -->\n        \n        <!-- SLOT::project-list::BEGIN -->\n        <div class=\"bg-white rounded-lg shadow-md\">\n            <div class=\"px-6 py-4 border-b border-gray-200\">\n                <div class=\"flex justify-between items-center\">\n                    <!-- CHUNK::list-title::BEGIN -->\n                    <h3 class=\"text-lg font-semibold text-gray-900\">科技项目列表</h3>\n                    <!-- CHUNK::list-title::END -->\n                    \n                    <div class=\"flex space-x-3\">\n                        <!-- CHUNK::list-add-button::BEGIN -->\n                        <button onclick=\"openModal('addProjectModal')\" class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\">\n                            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                            </svg>\n                            新增项目\n                        </button>\n                        <!-- CHUNK::list-add-button::END -->\n                        \n                        <!-- CHUNK::list-import-button::BEGIN -->\n                        <button onclick=\"openModal('importModal')\" class=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                            </svg>\n                            批量导入\n                        </button>\n                        <!-- CHUNK::list-import-button::END -->\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目名称</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目编号</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">承担单位</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">负责人</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目属性</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">起止时间</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">项目状态</th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">\n                        <!-- CHUNK::project-row-1::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">宁波市智能制造关键技术研发</div>\n                                <div class=\"text-sm text-gray-500\">国家级重点研发计划</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">NB2023ZD001</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX科技有限公司</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">张研究员</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">重点研发</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-01 ~ 2025-12</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">实施中</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('detailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::project-row-1::END -->\n                        \n                        <!-- CHUNK::project-row-2::BEGIN -->\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <input type=\"checkbox\" class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <div class=\"text-sm font-medium text-gray-900\">新材料产业化应用研究</div>\n                                <div class=\"text-sm text-gray-500\">省级重点研发计划</div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">ZJ2023ZD002</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">宁波市XX材料研究院</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">王教授</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">重点研发</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">2023-03 ~ 2024-12</td>\n                            <td class=\"px-6 py-4 whitespace-nowrap\">\n                                <span class=\"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">已立项</span>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                                <button onclick=\"openModal('detailModal')\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                                <button onclick=\"openModal('editModal')\" class=\"text-indigo-600 hover:text-indigo-900\">编辑</button>\n                                <button class=\"text-red-600 hover:text-red-900\">删除</button>\n                            </td>\n                        </tr>\n                        <!-- CHUNK::project-row-2::END -->\n                    </tbody>\n                </table>\n            </div>\n            \n            <div class=\"px-6 py-4 border-t border-gray-200\">\n                <div class=\"flex items-center justify-between\">\n                    <div class=\"text-sm text-gray-500\">\n                        显示第 1-2 条，共 128 条记录\n                    </div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">上一页</button>\n                        <button class=\"px-3 py-1 text-sm bg-blue-600 text-white rounded-md\">1</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">2</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">3</button>\n                        <button class=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50\">下一页</button>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <!-- SLOT::project-list::END -->\n    </div>\n    <!-- SLOT::main-content::END -->\n    \n    <!-- SLOT::add-project-modal::BEGIN -->\n    <div id=\"addProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen\">\n            <div class=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n                <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">新增科技项目</h3>\n                    <button onclick=\"closeModal('addProjectModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                \n                <div class=\"p-6\">\n                    <form>\n                        <div class=\"space-y-6\">\n                            <!-- CHUNK::modal-basic-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">基础信息</h4>\n                                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目名称 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目编号</label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目属性 <span class=\"text-red-500\">*</span></label>\n                                        <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                            <option value=\"\">请选择项目属性</option>\n                                            <option value=\"key\">重点研发</option>\n                                            <option value=\"normal\">一般项目</option>\n                                        </select>\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目级别 <span class=\"text-red-500\">*</span></label>\n                                        <select class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                            <option value=\"\">请选择项目级别</option>\n                                            <option value=\"national\">国家级</option>\n                                            <option value=\"provincial\">省级</option>\n                                            <option value=\"municipal\">市级</option>\n                                        </select>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::modal-basic-info::END -->\n                            \n                            <!-- CHUNK::modal-team-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">负责人与团队</h4>\n                                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">项目负责人 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">联系电话 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">承担单位 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">统一社会信用代码</label>\n                                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::modal-team-info::END -->\n                            \n                            <!-- CHUNK::modal-time-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">时间与经费</h4>\n                                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">开始时间 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">结束时间 <span class=\"text-red-500\">*</span></label>\n                                        <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">总经费（万元）</label>\n                                        <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">财政拨款（万元）</label>\n                                        <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\">\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::modal-time-info::END -->\n                            \n                            <!-- CHUNK::modal-attachment::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">附件上传</h4>\n                                <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                                    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                                    </svg>\n                                    <div class=\"mt-2\">\n                                        <label for=\"file-upload\" class=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none\">\n                                            <span>点击上传文件</span>\n                                            <input id=\"file-upload\" name=\"file-upload\" type=\"file\" class=\"sr-only\">\n                                        </label>\n                                        <p class=\"text-xs text-gray-500\">支持PDF、Word、Excel格式</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::modal-attachment::END -->\n                        </div>\n                        \n                        <div class=\"flex justify-end space-x-3 pt-6\">\n                            <button type=\"button\" onclick=\"closeModal('addProjectModal')\" class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                                取消\n                            </button>\n                            <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\">\n                                保存\n                            </button>\n                        </div>\n                    </form>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::add-project-modal::END -->\n    \n    <!-- SLOT::detail-modal::BEGIN -->\n    <div id=\"detailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen\">\n            <div class=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n                <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">项目详情</h3>\n                    <button onclick=\"closeModal('detailModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                \n                <div class=\"p-6\">\n                    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                        <div class=\"lg:col-span-2\">\n                            <!-- CHUNK::detail-basic-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">基础信息</h4>\n                                <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                                    <div>\n                                        <span class=\"text-gray-500\">项目名称：</span>\n                                        <span class=\"font-medium text-gray-900\">宁波市智能制造关键技术研发</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">项目编号：</span>\n                                        <span class=\"font-medium text-gray-900\">NB2023ZD001</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">项目属性：</span>\n                                        <span class=\"font-medium text-gray-900\">重点研发</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">项目级别：</span>\n                                        <span class=\"font-medium text-gray-900\">国家级</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">承担单位：</span>\n                                        <span class=\"font-medium text-gray-900\">宁波市XX科技有限公司</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">信用代码：</span>\n                                        <span class=\"font-medium text-gray-900\">91330201MA2XXXXXX</span>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-basic-info::END -->\n                            \n                            <!-- CHUNK::detail-team-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">负责人与团队</h4>\n                                <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                                    <div>\n                                        <span class=\"text-gray-500\">项目负责人：</span>\n                                        <span class=\"font-medium text-gray-900\">张研究员</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">联系电话：</span>\n                                        <span class=\"font-medium text-gray-900\">13888888888</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">团队成员：</span>\n                                        <span class=\"font-medium text-gray-900\">李工程师、王博士等5人</span>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-team-info::END -->\n                            \n                            <!-- CHUNK::detail-time-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">时间与经费</h4>\n                                <div class=\"grid grid-cols-2 gap-4 text-sm\">\n                                    <div>\n                                        <span class=\"text-gray-500\">开始时间：</span>\n                                        <span class=\"font-medium text-gray-900\">2023-01-15</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">结束时间：</span>\n                                        <span class=\"font-medium text-gray-900\">2025-12-31</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">总经费：</span>\n                                        <span class=\"font-medium text-gray-900\">500万元</span>\n                                    </div>\n                                    <div>\n                                        <span class=\"text-gray-500\">财政拨款：</span>\n                                        <span class=\"font-medium text-gray-900\">200万元</span>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-time-info::END -->\n                            \n                            <!-- CHUNK::detail-progress-info::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">项目进展</h4>\n                                <div class=\"space-y-4\">\n                                    <div>\n                                        <div class=\"flex justify-between mb-1\">\n                                            <span class=\"text-sm font-medium text-gray-700\">项目进度</span>\n                                            <span class=\"text-sm font-medium text-gray-700\">35%</span>\n                                        </div>\n                                        <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n                                            <div class=\"bg-blue-600 h-2.5 rounded-full\" style=\"width: 35%\"></div>\n                                        </div>\n                                    </div>\n                                    <div class=\"text-sm text-gray-700\">\n                                        <p>• 已完成需求调研和方案设计</p>\n                                        <p>• 正在进行关键技术研发</p>\n                                        <p>• 已申请专利2项</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-progress-info::END -->\n                        </div>\n                        \n                        <div>\n                            <!-- CHUNK::detail-attachment::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">相关附件</h4>\n                                <div class=\"space-y-3\">\n                                    <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                        <div class=\"flex items-center\">\n                                            <svg class=\"w-5 h-5 text-gray-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                                            </svg>\n                                            <span class=\"text-sm\">项目申报书.pdf</span>\n                                        </div>\n                                        <button class=\"text-blue-600 hover:text-blue-800 text-sm\">下载</button>\n                                    </div>\n                                    <div class=\"flex items-center justify-between p-3 border border-gray-200 rounded-md\">\n                                        <div class=\"flex items-center\">\n                                            <svg class=\"w-5 h-5 text-gray-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                                            </svg>\n                                            <span class=\"text-sm\">中期报告.docx</span>\n                                        </div>\n                                        <button class=\"text-blue-600 hover:text-blue-800 text-sm\">下载</button>\n                                    </div>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-attachment::END -->\n                            \n                            <!-- CHUNK::detail-actions::BEGIN -->\n                            <div class=\"bg-gray-50 rounded-lg p-6\">\n                                <h4 class=\"text-md font-semibold text-gray-900 mb-4\">操作</h4>\n                                <div class=\"space-y-3\">\n                                    <button class=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\">\n                                        导出项目报告\n                                    </button>\n                                    <button class=\"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                                        查看历史变更\n                                    </button>\n                                    <button class=\"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                                        关联科技成果\n                                    </button>\n                                </div>\n                            </div>\n                            <!-- CHUNK::detail-actions::END -->\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::detail-modal::END -->\n    \n    <!-- SLOT::import-modal::BEGIN -->\n    <div id=\"importModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50\">\n        <div class=\"flex items-center justify-center min-h-screen\">\n            <div class=\"bg-white rounded-lg shadow-xl max-w-2xl w-full\">\n                <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n                    <h3 class=\"text-lg font-semibold text-gray-900\">批量导入科技项目</h3>\n                    <button onclick=\"closeModal('importModal')\" class=\"text-gray-400 hover:text-gray-600\">\n                        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                        </svg>\n                    </button>\n                </div>\n                \n                <div class=\"p-6\">\n                    <div class=\"space-y-6\">\n                        <!-- CHUNK::import-download::BEGIN -->\n                        <div class=\"flex items-center justify-between\">\n                            <a href=\"#\" class=\"text-sm text-blue-600 hover:text-blue-800 flex items-center\">\n                                <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                                </svg>\n                                下载导入模板\n                            </a>\n                            <span class=\"text-xs text-gray-500\">支持.xlsx格式</span>\n                        </div>\n                        <!-- CHUNK::import-download::END -->\n                        \n                        <!-- CHUNK::import-upload::BEGIN -->\n                        <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                            <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                            </svg>\n                            <div class=\"mt-2\">\n                                <label for=\"import-file\" class=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none\">\n                                    <span>点击上传文件</span>\n                                    <input id=\"import-file\" name=\"import-file\" type=\"file\" class=\"sr-only\">\n                                </label>\n                                <p class=\"text-xs text-gray-500\">或拖拽文件到此处</p>\n                            </div>\n                        </div>\n                        <!-- CHUNK::import-upload::END -->\n                        \n                        <!-- CHUNK::import-progress::BEGIN -->\n                        <div id=\"importProgress\" class=\"hidden\">\n                            <div class=\"flex items-center justify-between mb-1\">\n                                <span class=\"text-sm font-medium text-gray-700\">上传进度</span>\n                                <span class=\"text-sm font-medium text-gray-700\">75%</span>\n                            </div>\n                            <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n                                <div class=\"bg-blue-600 h-2.5 rounded-full\" style=\"width: 75%\"></div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::import-progress::END -->\n                        \n                        <!-- CHUNK::import-result::BEGIN -->\n                        <div id=\"importResult\" class=\"hidden bg-yellow-50 p-4 rounded-lg\">\n                            <div class=\"flex\">\n                                <div class=\"flex-shrink-0\">\n                                    <svg class=\"h-5 w-5 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"></path>\n                                    </svg>\n                                </div>\n                                <div class=\"ml-3\">\n                                    <p class=\"text-sm font-medium text-yellow-800\">发现2条数据存在问题</p>\n                                    <ul class=\"mt-2 text-xs text-yellow-700 list-disc list-inside\">\n                                        <li>第3行：项目编号已存在</li>\n                                        <li>第5行：项目名称为必填项</li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </div>\n                        <!-- CHUNK::import-result::END -->\n                    </div>\n                    \n                    <div class=\"flex justify-end space-x-3 pt-6\">\n                        <button type=\"button\" onclick=\"closeModal('importModal')\" class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm\">\n                            取消\n                        </button>\n                        <button type=\"button\" class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\">\n                            开始导入\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <!-- SLOT::import-modal::END -->\n    \n    <script>\n        // --- 全局UI控制函数 ---\n        function openModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.remove('hidden');\n                document.body.style.overflow = 'hidden';\n            }\n        }\n\n        function closeModal(modalId) {\n            const modal = document.getElementById(modalId);\n            if (modal) {\n                modal.classList.add('hidden');\n                document.body.style.overflow = 'auto';\n            }\n        }\n\n        // --- DOM加载完毕后执行的初始化代码 ---\n        document.addEventListener('DOMContentLoaded', function() {\n            // 为所有弹窗绑定\"点击外部关闭\"事件\n            const modals = document.querySelectorAll('[id$=\"Modal\"]');\n            modals.forEach(modal => {\n                modal.addEventListener('click', function(event) {\n                    if (event.target === modal) {\n                        closeModal(modal.id);\n                    }\n                });\n\n                document.addEventListener('keydown', function (e) {\n                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {\n                        closeModal(modal.id);\n                    }\n                });\n            });\n            \n            // 文件上传处理\n            document.getElementById('import-file').addEventListener('change', function(e) {\n                if (e.target.files.length > 0) {\n                    document.getElementById('importProgress').classList.remove('hidden');\n                    setTimeout(function() {\n                        document.getElementById('importResult').classList.remove('hidden');\n                    }, 2000);\n                }\n            });\n            \n            // 表单提交处理\n            document.querySelectorAll('form').forEach(form => {\n                form.addEventListener('submit', function(e) {\n                    e.preventDefault();\n                    alert('表单已提交 (原型演示)');\n                    const parentModalId = form.closest('[id$=\"Modal\"]').id;\n                    closeModal(parentModalId);\n                });\n            });\n        });\n    </script>\n</body>\n</html>", "role": "assistant"}}], "created": 1753695092, "id": "02175369469033761ee2447fb8673108fd2fdb6d547618fcaa57c", "model": "deepseek-v3-250324", "service_tier": "default", "object": "chat.completion", "usage": {"completion_tokens": 10229, "prompt_tokens": 39159, "total_tokens": 49388, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}}