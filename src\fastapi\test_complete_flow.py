#!/usr/bin/env python3
"""
完整流程测试：从文档上传到图像插入
"""

import os
import json
import requests
import time
import re

def create_test_document():
    """创建测试文档"""
    print("📄 创建测试文档...")
    
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)
    
    # 模拟一个已上传的文档
    import uuid
    file_id = str(uuid.uuid4())[:8]
    doc_filename = f"{file_id}_投标文件-测试.docx"
    doc_path = os.path.join(uploads_dir, doc_filename)
    
    # 创建一个简单的文本文件模拟Word文档
    test_content = """
    一、系统概述
    本系统是一个投标文件管理系统。
    
    二、功能模块
    2.1 用户管理
    用户登录、注册、权限管理等功能。
    
    三、业务流程
    3.1 投标流程
    投标文件上传、审核、评分等流程。
    
    四、操作流程
    4.1 系统操作
    详细的操作步骤说明。
    """
    
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 测试文档创建: {doc_path}")
    return doc_path

def create_test_generated_files():
    """创建测试生成的文件"""
    print("🎨 创建测试生成文件...")
    
    generated_files_dir = "generated_files"
    if not os.path.exists(generated_files_dir):
        os.makedirs(generated_files_dir)
    
    # 模拟任务执行生成的文件
    test_files = []
    
    # 创建SVG文件（模拟flowchart任务）
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect x="50" y="50" width="100" height="60" fill="lightblue" stroke="black"/>
  <text x="100" y="85" text-anchor="middle">用户管理流程</text>
  <rect x="200" y="50" width="100" height="60" fill="lightgreen" stroke="black"/>
  <text x="250" y="85" text-anchor="middle">权限验证</text>
  <line x1="150" y1="80" x2="200" y2="80" stroke="black" marker-end="url(#arrowhead)"/>
</svg>'''
    
    # 创建HTML文件（模拟ui_prototype任务）
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>用户管理界面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 10px; border-radius: 5px; }
        .form-group { margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>用户管理系统</h1>
    </div>
    <div class="form-group">
        <label>用户名：</label>
        <input type="text" placeholder="请输入用户名">
    </div>
    <div class="form-group">
        <label>密码：</label>
        <input type="password" placeholder="请输入密码">
    </div>
    <div class="form-group">
        <button class="btn">登录</button>
        <button class="btn">注册</button>
    </div>
</body>
</html>'''
    
    # 生成不同命名模式的文件
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 模式1: 基于功能名称
    svg_file1 = f"flowchart_用户管理_{timestamp}.svg"
    html_file1 = f"ui_prototype_用户管理_{timestamp}.html"
    
    # 模式2: 基于section ID
    svg_file2 = f"flowchart_section_5_{timestamp}.svg"
    html_file2 = f"ui_prototype_section_5_{timestamp}.html"
    
    # 模式3: 基于任务ID（最匹配的模式）
    svg_file3 = f"flowchart_section-5-flowchart_{timestamp}.svg"
    html_file3 = f"ui_prototype_section-5-ui_prototype_{timestamp}.html"
    
    files_to_create = [
        (svg_file1, svg_content),
        (html_file1, html_content),
        (svg_file2, svg_content),
        (html_file2, html_content),
        (svg_file3, svg_content),
        (html_file3, html_content)
    ]
    
    for filename, content in files_to_create:
        file_path = os.path.join(generated_files_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 创建文件: {filename}")
        test_files.append(filename)
        time.sleep(0.1)  # 确保文件时间戳不同
    
    return test_files

def test_batch_insert_api(doc_path):
    """测试批量插入API"""
    print("\n🧪 测试批量插入API...")
    
    api_url = "http://localhost:8000/api/word/batch-insert"
    
    # 模拟前端发送的请求
    request_data = {
        "doc_path": doc_path,
        "task_results": [
            {
                "id": "section-5-flowchart",
                "type": "flowchart",
                "status": "completed"
            },
            {
                "id": "section-5-ui_prototype",
                "type": "ui_prototype",
                "status": "completed"
            }
        ],
        "config": {
            "business_flow_pattern": "三、业务流程",
            "operation_flow_pattern": "四、操作流程"
        }
    }
    
    print(f"📋 请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            api_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(request_data)
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量插入API调用成功")
            print(f"📊 返回结果:")
            print(f"  - 成功插入: {result.get('insertions_count', 0)} 个图像")
            print(f"  - 失败数量: {result.get('failed_count', 0)}")
            print(f"  - 输出文档: {result.get('output_path', 'N/A')}")
            
            if result.get('success_details'):
                print("📋 成功详情:")
                for detail in result['success_details']:
                    print(f"  - 源文件: {detail['source']}")
                    print(f"    关键词: {detail['keyword_pattern']}")
                    print(f"    图像路径: {detail['image_path']}")
            
            if result.get('failed_details'):
                print("❌ 失败详情:")
                for detail in result['failed_details']:
                    print(f"  - 源文件: {detail['source']}")
                    print(f"    错误: {detail['error']}")
            
            return result.get('output_path')
        else:
            print(f"❌ 批量插入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 批量插入测试失败: {str(e)}")
        return None

def test_file_download(output_path):
    """测试文件下载"""
    if not output_path:
        print("⏭️ 跳过文件下载测试（没有输出文件）")
        return
    
    print(f"\n🧪 测试文件下载: {output_path}")
    
    import urllib.parse
    encoded_path = urllib.parse.quote(output_path)
    download_url = f"http://localhost:8000/api/files/download/{encoded_path}"
    
    try:
        response = requests.get(download_url)
        print(f"📡 下载响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 文件下载成功")
            print(f"📊 文件大小: {len(response.content)} 字节")
            print(f"📋 Content-Type: {response.headers.get('content-type', 'N/A')}")
        else:
            print(f"❌ 文件下载失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 文件下载测试失败: {str(e)}")

def main():
    print("🧪 开始完整流程测试...")
    print("=" * 80)
    
    # 步骤1: 创建测试文档
    doc_path = create_test_document()
    
    # 步骤2: 创建测试生成文件
    generated_files = create_test_generated_files()
    
    # 步骤3: 测试批量插入
    output_path = test_batch_insert_api(doc_path)
    
    # 步骤4: 测试文件下载
    test_file_download(output_path)
    
    print("\n🏁 完整流程测试完成")
    print("=" * 80)
    
    # 清理提示
    print("\n🧹 清理提示:")
    print("如果测试成功，可以手动删除以下测试文件:")
    print(f"  - 测试文档: {doc_path}")
    print(f"  - 生成文件: generated_files/ 目录中的测试文件")
    if output_path:
        print(f"  - 输出文档: {output_path}")

if __name__ == "__main__":
    main()
