"""
增强预览管理器

提供PDF+HTML双视图预览功能，支持同步滚动和书签导航。
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any
import json

from .preview_manager import PreviewManager

logger = logging.getLogger(__name__)


class EnhancedPreviewManager(PreviewManager):
    """增强预览管理器"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.viewer_template_path = Path(__file__).parent.parent / 'templates' / 'dual_viewer.html'
        
    def create_dual_view_preview(self, doc_path: str) -> str:
        """
        创建双视图预览
        
        Args:
            doc_path: 文档路径
            
        Returns:
            str: 预览页面路径
        """
        doc_path = Path(doc_path)
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Creating dual view preview for: {doc_path}")
        
        try:
            # 生成PDF和HTML预览
            pdf_path = self._convert_to_pdf(doc_path)
            html_path = self._convert_to_html(doc_path)
            
            # 创建双视图页面
            viewer_path = self._create_dual_viewer_page(
                doc_path.stem,
                pdf_path,
                html_path
            )
            
            logger.info(f"Dual view preview created: {viewer_path}")
            return viewer_path
            
        except Exception as e:
            logger.error(f"Failed to create dual view preview: {e}")
            raise
    
    def _create_dual_viewer_page(self, doc_name: str, pdf_path: str, html_path: str) -> str:
        """创建双视图预览页面"""
        viewer_dir = self.temp_dir / 'dual_viewer'
        viewer_dir.mkdir(exist_ok=True)
        
        viewer_path = viewer_dir / f"{doc_name}_viewer.html"
        
        # 获取相对路径
        pdf_rel_path = os.path.relpath(pdf_path, viewer_dir)
        html_rel_path = os.path.relpath(html_path, viewer_dir)
        
        # 生成预览页面HTML
        html_content = self._generate_viewer_html(doc_name, pdf_rel_path, html_rel_path)
        
        with open(viewer_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(viewer_path)
    
    def _generate_viewer_html(self, doc_name: str, pdf_path: str, html_path: str) -> str:
        """生成双视图预览HTML"""
        return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{doc_name} - 双视图预览</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }}
        
        .header {{
            background: #fff;
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }}
        
        .title {{
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }}
        
        .controls {{
            display: flex;
            gap: 12px;
            align-items: center;
        }}
        
        .btn {{
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }}
        
        .btn:hover {{
            border-color: #1890ff;
            color: #1890ff;
        }}
        
        .btn.active {{
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }}
        
        .container {{
            display: flex;
            height: calc(100vh - 60px);
        }}
        
        .panel {{
            flex: 1;
            background: #fff;
            border-right: 1px solid #e8e8e8;
            position: relative;
        }}
        
        .panel:last-child {{
            border-right: none;
        }}
        
        .panel-header {{
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            color: #666;
        }}
        
        .panel-content {{
            height: calc(100% - 40px);
            overflow: auto;
        }}
        
        .pdf-viewer {{
            width: 100%;
            height: 100%;
            border: none;
        }}
        
        .html-viewer {{
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
        }}
        
        .loading {{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #666;
            font-size: 16px;
        }}
        
        .error {{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #ff4d4f;
            font-size: 16px;
            flex-direction: column;
            gap: 12px;
        }}
        
        .sync-indicator {{
            position: absolute;
            top: 50%;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
            z-index: 100;
            cursor: pointer;
            transition: all 0.2s;
        }}
        
        .sync-indicator:hover {{
            background: #40a9ff;
            transform: scale(1.1);
        }}
        
        .fullscreen {{
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: #fff;
        }}
        
        .fullscreen .container {{
            height: calc(100vh - 60px);
        }}
        
        @media (max-width: 768px) {{
            .container {{
                flex-direction: column;
            }}
            
            .panel {{
                border-right: none;
                border-bottom: 1px solid #e8e8e8;
            }}
            
            .panel:last-child {{
                border-bottom: none;
            }}
            
            .sync-indicator {{
                top: auto;
                bottom: -12px;
                right: 50%;
                transform: translateX(50%);
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="title">📄 {doc_name} - 双视图预览</div>
        <div class="controls">
            <button class="btn" id="syncBtn" title="同步滚动">🔗 同步</button>
            <button class="btn" id="pdfOnlyBtn" title="仅PDF视图">📄 PDF</button>
            <button class="btn" id="htmlOnlyBtn" title="仅HTML视图">🌐 HTML</button>
            <button class="btn" id="dualViewBtn" title="双视图" class="active">⚌ 双视图</button>
            <button class="btn" id="fullscreenBtn" title="全屏">⛶ 全屏</button>
        </div>
    </div>
    
    <div class="container" id="container">
        <div class="panel" id="pdfPanel">
            <div class="panel-header">📄 PDF视图</div>
            <div class="panel-content">
                <iframe class="pdf-viewer" id="pdfViewer" src="{pdf_path}" title="PDF预览"></iframe>
            </div>
            <div class="sync-indicator" title="同步滚动">⚌</div>
        </div>
        
        <div class="panel" id="htmlPanel">
            <div class="panel-header">🌐 HTML视图</div>
            <div class="panel-content">
                <iframe class="html-viewer" id="htmlViewer" src="{html_path}" title="HTML预览"></iframe>
            </div>
        </div>
    </div>
    
    <script>
        class DualViewer {{
            constructor() {{
                this.syncEnabled = true;
                this.isFullscreen = false;
                this.currentView = 'dual';
                
                this.initElements();
                this.bindEvents();
                this.setupSync();
            }}
            
            initElements() {{
                this.container = document.getElementById('container');
                this.pdfPanel = document.getElementById('pdfPanel');
                this.htmlPanel = document.getElementById('htmlPanel');
                this.pdfViewer = document.getElementById('pdfViewer');
                this.htmlViewer = document.getElementById('htmlViewer');
                
                this.syncBtn = document.getElementById('syncBtn');
                this.pdfOnlyBtn = document.getElementById('pdfOnlyBtn');
                this.htmlOnlyBtn = document.getElementById('htmlOnlyBtn');
                this.dualViewBtn = document.getElementById('dualViewBtn');
                this.fullscreenBtn = document.getElementById('fullscreenBtn');
            }}
            
            bindEvents() {{
                this.syncBtn.addEventListener('click', () => this.toggleSync());
                this.pdfOnlyBtn.addEventListener('click', () => this.setView('pdf'));
                this.htmlOnlyBtn.addEventListener('click', () => this.setView('html'));
                this.dualViewBtn.addEventListener('click', () => this.setView('dual'));
                this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
                
                // 键盘快捷键
                document.addEventListener('keydown', (e) => {{
                    if (e.ctrlKey || e.metaKey) {{
                        switch(e.key) {{
                            case '1':
                                e.preventDefault();
                                this.setView('pdf');
                                break;
                            case '2':
                                e.preventDefault();
                                this.setView('html');
                                break;
                            case '3':
                                e.preventDefault();
                                this.setView('dual');
                                break;
                            case 'f':
                                e.preventDefault();
                                this.toggleFullscreen();
                                break;
                        }}
                    }}
                }});
            }}
            
            setupSync() {{
                // 同步滚动功能（简化版）
                let syncTimeout;
                
                const syncScroll = (source, target) => {{
                    if (!this.syncEnabled) return;
                    
                    clearTimeout(syncTimeout);
                    syncTimeout = setTimeout(() => {{
                        try {{
                            const sourceDoc = source.contentDocument || source.contentWindow.document;
                            const targetDoc = target.contentDocument || target.contentWindow.document;
                            
                            if (sourceDoc && targetDoc) {{
                                const sourceScrollTop = sourceDoc.documentElement.scrollTop || sourceDoc.body.scrollTop;
                                const sourceScrollHeight = sourceDoc.documentElement.scrollHeight || sourceDoc.body.scrollHeight;
                                const targetScrollHeight = targetDoc.documentElement.scrollHeight || targetDoc.body.scrollHeight;
                                
                                const scrollRatio = sourceScrollTop / (sourceScrollHeight - sourceDoc.documentElement.clientHeight);
                                const targetScrollTop = scrollRatio * (targetScrollHeight - targetDoc.documentElement.clientHeight);
                                
                                targetDoc.documentElement.scrollTop = targetScrollTop;
                                targetDoc.body.scrollTop = targetScrollTop;
                            }}
                        }} catch (e) {{
                            // 跨域限制，忽略错误
                        }}
                    }}, 100);
                }};
                
                // 监听滚动事件（如果可能）
                try {{
                    this.pdfViewer.addEventListener('load', () => {{
                        try {{
                            const pdfDoc = this.pdfViewer.contentDocument || this.pdfViewer.contentWindow.document;
                            pdfDoc.addEventListener('scroll', () => syncScroll(this.pdfViewer, this.htmlViewer));
                        }} catch (e) {{
                            // 跨域限制
                        }}
                    }});
                    
                    this.htmlViewer.addEventListener('load', () => {{
                        try {{
                            const htmlDoc = this.htmlViewer.contentDocument || this.htmlViewer.contentWindow.document;
                            htmlDoc.addEventListener('scroll', () => syncScroll(this.htmlViewer, this.pdfViewer));
                        }} catch (e) {{
                            // 跨域限制
                        }}
                    }});
                }} catch (e) {{
                    console.warn('Sync scroll setup failed:', e);
                }}
            }}
            
            toggleSync() {{
                this.syncEnabled = !this.syncEnabled;
                this.syncBtn.classList.toggle('active', this.syncEnabled);
                this.syncBtn.textContent = this.syncEnabled ? '🔗 同步' : '🔗 关闭';
            }}
            
            setView(view) {{
                this.currentView = view;
                
                // 更新按钮状态
                document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
                
                switch(view) {{
                    case 'pdf':
                        this.pdfPanel.style.display = 'block';
                        this.htmlPanel.style.display = 'none';
                        this.pdfOnlyBtn.classList.add('active');
                        break;
                    case 'html':
                        this.pdfPanel.style.display = 'none';
                        this.htmlPanel.style.display = 'block';
                        this.htmlOnlyBtn.classList.add('active');
                        break;
                    case 'dual':
                        this.pdfPanel.style.display = 'block';
                        this.htmlPanel.style.display = 'block';
                        this.dualViewBtn.classList.add('active');
                        break;
                }}
            }}
            
            toggleFullscreen() {{
                if (!this.isFullscreen) {{
                    document.body.classList.add('fullscreen');
                    this.fullscreenBtn.textContent = '⛶ 退出';
                    this.isFullscreen = true;
                }} else {{
                    document.body.classList.remove('fullscreen');
                    this.fullscreenBtn.textContent = '⛶ 全屏';
                    this.isFullscreen = false;
                }}
            }}
        }}
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {{
            new DualViewer();
        }});
        
        // 错误处理
        window.addEventListener('error', (e) => {{
            console.error('Viewer error:', e);
        }});
    </script>
</body>
</html>'''
    
    def get_preview_stats(self, doc_path: str) -> Dict[str, Any]:
        """获取预览统计信息"""
        doc_path = Path(doc_path)
        
        stats = {
            'document': {
                'name': doc_path.name,
                'size': doc_path.stat().st_size if doc_path.exists() else 0,
                'exists': doc_path.exists()
            },
            'previews': {
                'pdf': {'exists': False, 'size': 0, 'path': None},
                'html': {'exists': False, 'size': 0, 'path': None},
                'dual_viewer': {'exists': False, 'size': 0, 'path': None}
            },
            'capabilities': {
                'libreoffice_available': self.libreoffice_path is not None,
                'dual_view_supported': True,
                'sync_scroll_supported': True
            }
        }
        
        # 检查预览文件
        if doc_path.exists():
            try:
                pdf_path = Path(self.temp_dir) / 'pdf' / f"{doc_path.stem}.pdf"
                if pdf_path.exists():
                    stats['previews']['pdf'] = {
                        'exists': True,
                        'size': pdf_path.stat().st_size,
                        'path': str(pdf_path)
                    }
                
                html_path = Path(self.temp_dir) / 'html' / f"{doc_path.stem}.html"
                if html_path.exists():
                    stats['previews']['html'] = {
                        'exists': True,
                        'size': html_path.stat().st_size,
                        'path': str(html_path)
                    }
                
                viewer_path = Path(self.temp_dir) / 'dual_viewer' / f"{doc_path.stem}_viewer.html"
                if viewer_path.exists():
                    stats['previews']['dual_viewer'] = {
                        'exists': True,
                        'size': viewer_path.stat().st_size,
                        'path': str(viewer_path)
                    }
                    
            except Exception as e:
                logger.warning(f"Failed to get preview stats: {e}")
        
        return stats
