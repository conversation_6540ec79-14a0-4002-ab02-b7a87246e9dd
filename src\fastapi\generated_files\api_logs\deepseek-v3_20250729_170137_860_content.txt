=== API调用成功 ===
时间: 2025-07-29 17:03:17
模型: deepseek-v3
内容长度: 6560 字符

=== 生成内容 ===
Here's the SVG flowchart for the tech project management module based on your requirements:

```svg
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技项目管理流程</text>

  <!-- 阶段一：项目入口 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：项目入口</text>
  
  <!-- 节点1: 主入口 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科技项目管理模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">查询/录入/导入业务入口</text>
  </g>

  <!-- 阶段二：核心操作 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：核心操作</text>

  <!-- 节点2: 查询 -->
  <g transform="translate(150, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选条件，检索项目数据</text>
  </g>

  <!-- 节点3: 新增 -->
  <g transform="translate(450, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写表单，上传资料，校验入库</text>
  </g>

  <!-- 节点4: 详情 -->
  <g transform="translate(750, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示关键信息、成果和附件</text>
  </g>

  <!-- 节点5: 批量操作 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">模板下载，批量录入，自动校验</text>
  </g>

  <!-- 节点6: 批量操作 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">导出/删除/成果关联</text>
  </g>

  <!-- 连接线 主入口 -> 核心操作 -->
  <path d="M 600 200 Q 600 225 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 主入口 -> 查询 -->
  <path d="M 500 200 C 450 250, 250 250, 250 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 主入口 -> 新增 -->
  <path d="M 500 200 C 500 250, 550 250, 550 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 主入口 -> 详情 -->
  <path d="M 700 200 C 750 250, 850 250, 850 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 查询 -> 批量导入 -->
  <path d="M 250 380 C 250 400, 250 420, 300 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 新增 -> 批量操作 -->
  <path d="M 550 380 C 550 400, 550 420, 600 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：扩展功能 -->
  <text x="600" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：扩展功能</text>

  <!-- 节点7: 变更记录 -->
  <g transform="translate(300, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录历史变更，查看日志</text>
  </g>

  <!-- 节点8: 成果关联 -->
  <g transform="translate(600, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果关联</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">项目与科技成果灵活关联</text>
  </g>

  <!-- 连接线 批量操作 -> 变更记录 -->
  <path d="M 400 500 C 350 550, 350 600, 300 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 批量操作 -> 成果关联 -->
  <path d="M 600 500 C 600 550, 600 600, 600 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情 -> 成果关联 -->
  <path d="M 850 380 C 950 450, 700 550, 700 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>
```

This SVG flowchart follows all your design requirements:
1. **Clear Phased Structure**: Divided into three main stages (Entry, Core Operations, Extended Features)
2. **Color Scheme**: 
   - Entry phase uses light blue (#E3F2FD)
   - Core operations use light green (#E6F4EA) and purple (#F3E8FD)
   - Extended features use light yellow (#FFFBEB)
3. **Visual Elements**:
   - All nodes are rounded rectangles with soft shadows
   - Each node has a bold title and descriptive subtitle
   - Smooth Bezier curves connect nodes with clear arrowheads
4. **Typography**: Uses a clean sans-serif font with appropriate sizing
5. **Layout**: Well-spaced with clear visual hierarchy and grouping

The flowchart effectively visualizes the entire tech project management workflow from entry points through core operations to extended features.