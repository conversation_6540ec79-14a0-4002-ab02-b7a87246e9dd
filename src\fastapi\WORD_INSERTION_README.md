# Word图像插入功能使用指南

## 🎯 功能概述

Word图像插入功能允许将生成的SVG流程图和HTML UI原型自动插入到Word文档的指定位置，并提供增强的预览功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 进入FastAPI目录
cd src/fastapi

# 运行安装脚本
python setup_word_insertion.py
```

### 2. 手动安装（如果自动安装失败）

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 安装LibreOffice（用于预览功能）
# Windows: 从官网下载安装
# Linux: sudo apt-get install libreoffice
# macOS: brew install --cask libreoffice
```

### 3. 启动服务

```bash
# 启动FastAPI服务器
python -m uvicorn app.main:app --reload
```

## 📡 API接口

### 1. 批量插入图像

```http
POST /api/word/batch-insert
Content-Type: application/json

{
  "doc_path": "document.docx",
  "task_results": [
    {
      "id": "task_id",
      "type": "flowchart",
      "status": "completed"
    }
  ],
  "config": {
    "business_flow_pattern": "三、业务流程",
    "operation_flow_pattern": "四、操作流程"
  }
}
```

### 2. 单个图像插入

```http
POST /api/word/insert-image
Content-Type: application/json

{
  "doc_path": "document.docx",
  "insertions": [
    {
      "section_id": "section1",
      "content_type": "svg",
      "content_path": "flowchart.svg",
      "keyword_pattern": "业务流程",
      "image_width": 5.0
    }
  ],
  "output_path": "output.docx"
}
```

### 3. 文档预览

```http
POST /api/word/preview
Content-Type: application/json

{
  "doc_path": "document.docx",
  "preview_type": "pdf"
}
```

## 🎨 前端集成

### StepDocumentExport组件

新的Word导出功能已集成到文档导出步骤中：

1. **选择导出选项**：
   - 插入生成的图像到Word文档
   - 启用增强预览功能
   - 自动格式优化

2. **执行导出**：
   - 点击"导出Word文档"按钮
   - 系统自动处理图像插入
   - 显示进度和状态

3. **预览和下载**：
   - 支持PDF和HTML双视图预览
   - 自动下载生成的Word文档

## ⚙️ 配置说明

### 配置文件：`word_insertion_config.json`

```json
{
  "image": {
    "default_dpi": 300,
    "max_width_inches": 6.5,
    "device_scale_factor": 3
  },
  "section_patterns": {
    "business_flow": {
      "keywords": ["三、业务流程", "业务流程"],
      "image_width": 5.0
    },
    "operation_flow": {
      "keywords": ["四、操作流程", "操作流程"],
      "image_width": 5.5
    }
  }
}
```

### 环境变量

```bash
# 日志级别
WII_LOG_LEVEL=INFO

# 图像DPI
WII_IMAGE_DPI=300

# 最大图像宽度
WII_MAX_WIDTH=6.5

# 设备缩放因子
WII_DEVICE_SCALE=3

# 缓存启用
WII_CACHE_ENABLED=true

# 自动打开预览
WII_AUTO_OPEN=false
```

## 🔧 工作流程

### 1. 图像生成流程

```
SVG/HTML文件 → Playwright渲染 → 高清PNG → Word插入
```

### 2. 文档处理流程

```
原始Word文档 → 解析章节 → 定位插入点 → 插入图像 → 生成新文档
```

### 3. 预览生成流程

```
Word文档 → LibreOffice转换 → PDF/HTML → 浏览器预览
```

## 📁 文件结构

```
src/fastapi/
├── word_image_insertion/           # 核心功能包
│   ├── core/                      # 核心模块
│   │   ├── document_parser.py     # 文档解析
│   │   ├── image_generator.py     # 图像生成
│   │   ├── word_inserter.py       # Word插入
│   │   └── preview_manager.py     # 预览管理
│   └── utils/                     # 工具模块
│       ├── config.py              # 配置管理
│       └── helpers.py             # 辅助函数
├── setup_word_insertion.py        # 安装脚本
├── word_insertion_config.json     # 配置文件
└── requirements.txt               # 依赖列表
```

## 🐛 故障排除

### 常见问题

1. **LibreOffice未找到**
   ```bash
   # 检查安装路径
   which libreoffice
   # 或
   where soffice.exe
   ```

2. **Playwright浏览器缺失**
   ```bash
   playwright install chromium
   ```

3. **权限问题**
   ```bash
   # 确保有读写临时目录的权限
   chmod 755 ~/.word_image_insertion
   ```

4. **内存不足**
   - 调整图像生成参数
   - 减少device_scale_factor
   - 增加系统内存

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用调试配置
from word_image_insertion import Config
config = Config()
config.logging.level = 'DEBUG'
```

## 📊 性能优化

### 1. 图像生成优化

- 使用合适的DPI设置（300为推荐值）
- 调整device_scale_factor（2-3为最佳）
- 启用图像缓存

### 2. 文档处理优化

- 启用文档缓存
- 限制并发处理数量
- 使用临时文件管理

### 3. 预览功能优化

- 定期清理临时文件
- 使用增量更新
- 优化LibreOffice参数

## 🔒 安全考虑

1. **文件访问控制**：限制可访问的文件路径
2. **临时文件清理**：自动清理过期的临时文件
3. **输入验证**：验证所有输入参数
4. **资源限制**：限制内存和CPU使用

## 📈 监控和日志

### 日志级别

- `DEBUG`：详细调试信息
- `INFO`：一般信息（推荐）
- `WARNING`：警告信息
- `ERROR`：错误信息

### 监控指标

- 图像生成成功率
- 文档处理时间
- 内存使用情况
- 临时文件数量

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

Apache 2.0 License
